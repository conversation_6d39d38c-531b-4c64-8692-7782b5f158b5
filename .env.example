# 数据库配置
DATABASE_URL=sqlite:///./social_media.db

# JWT配置
SECRET_KEY=your_secret_key_here_generate_a_new_one
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 热更新配置
# 设置为 true 启用代码热更新，false 为生产模式
HOT_RELOAD=false

# 前端配置
NODE_ENV=production
FRONTEND_PORT=3000
FRONTEND_INTERNAL_PORT=80

# 开发环境示例（取消注释以启用热更新）
# HOT_RELOAD=true
# NODE_ENV=development
# FRONTEND_DOCKERFILE=Dockerfile.dev
# FRONTEND_INTERNAL_PORT=3000

# 生产环境配置
ENVIRONMENT=production
DEBUG=false

# 登录状态维持服务配置
LOGIN_KEEPER_ENABLED=true
LOGIN_KEEPER_INTERVAL=30
LOGIN_KEEPER_PLATFORMS=wechat_mp,xiaoh<PERSON>shu,wechat_channels
LOGIN_KEEPER_MAX_RETRIES=3
LOGIN_KEEPER_CONCURRENT=3
LOGIN_KEEPER_TIMEOUT=60
LOGIN_KEEPER_LOG_LEVEL=INFO

# CORS配置
ALLOWED_ORIGINS=http://localhost:3000,http://sm.dev.mynatapp.cc