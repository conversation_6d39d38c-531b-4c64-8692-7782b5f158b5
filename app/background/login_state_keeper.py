"""
登录状态维持后台任务调度器
"""
import asyncio
import logging
from datetime import datetime, timedelta
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.executors.asyncio import AsyncIOExecutor

from app.services.login_keeper_service import LoginKeeperService
from app.config.keeper_config import get_interval_minutes, is_keeper_enabled

# 配置日志
logger = logging.getLogger(__name__)

class LoginStateKeeperScheduler:
    """登录状态维持调度器"""
    
    def __init__(self):
        self.scheduler = None
        self.keeper_service = LoginKeeperService()
        self.is_initialized = False
        self.job_id = "login_state_keeper"
    
    def initialize(self):
        """初始化调度器"""
        if self.is_initialized:
            logger.warning("调度器已经初始化")
            return
        
        try:
            # 创建调度器
            executors = {
                'default': AsyncIOExecutor()
            }
            
            job_defaults = {
                'coalesce': True,  # 合并多个相同的任务
                'max_instances': 1,  # 最多只能有一个实例运行
                'misfire_grace_time': 300  # 错过执行时间的宽限期（秒）
            }
            
            self.scheduler = AsyncIOScheduler(
                executors=executors,
                job_defaults=job_defaults,
                timezone='Asia/Shanghai'
            )
            
            # 添加定时任务
            if is_keeper_enabled():
                self._add_keeper_job()
                print("✅ 登录状态维持调度器初始化成功")
                logger.info("登录状态维持调度器初始化成功")
            else:
                print("⚠️  登录状态维持功能已禁用")
                logger.info("登录状态维持功能已禁用")

            self.is_initialized = True
            
        except Exception as e:
            logger.error(f"初始化调度器失败: {e}")
            raise
    
    def _add_keeper_job(self):
        """添加登录状态维持任务"""
        try:
            interval_minutes = get_interval_minutes()

            # 添加任务，但不立即执行，等待第一个间隔后再执行
            next_run = datetime.now() + timedelta(minutes=1)  # 1分钟后开始第一次执行

            self.scheduler.add_job(
                func=self._execute_keeper_task,
                trigger=IntervalTrigger(minutes=interval_minutes),
                id=self.job_id,
                name="登录状态维持任务",
                replace_existing=True,
                next_run_time=next_run,
                max_instances=1,  # 确保同时只有一个实例运行
                coalesce=True,    # 如果错过了执行时间，只执行最新的一次
                misfire_grace_time=300  # 错过执行时间的宽限期（5分钟）
            )

            print(f"⏱️  已添加登录状态维持任务，间隔: {interval_minutes} 分钟，首次执行: {next_run.strftime('%H:%M:%S')}")
            logger.info(f"已添加登录状态维持任务，间隔: {interval_minutes} 分钟")

        except Exception as e:
            error_msg = f"添加登录状态维持任务失败: {e}"
            print(f"❌ {error_msg}")
            logger.error(error_msg)
            raise
    
    async def _execute_keeper_task(self):
        """执行登录状态维持任务"""
        try:
            logger.info("开始执行登录状态维持任务")
            print("🔄 开始执行登录状态维持任务...")

            result = await self.keeper_service.maintain_all_login_states()

            if result["success"]:
                success_msg = f"登录状态维持任务完成: 处理 {result['accounts_processed']} 个账号，成功 {result['successful_accounts']} 个，失败 {result['failed_accounts']} 个"
                logger.info(success_msg)
                print(f"✅ {success_msg}")
            else:
                error_msg = f"登录状态维持任务失败: {result.get('message', '未知错误')}"
                logger.error(error_msg)
                print(f"❌ {error_msg}")

        except asyncio.CancelledError:
            logger.warning("登录状态维持任务被取消")
            print("⚠️  登录状态维持任务被取消")
            raise  # 重新抛出CancelledError以便正确处理
        except Exception as e:
            error_msg = f"执行登录状态维持任务时发生异常: {e}"
            logger.error(error_msg)
            print(f"💥 {error_msg}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
    
    def start(self):
        """启动调度器"""
        if not self.is_initialized:
            print("📋 初始化登录状态维持调度器...")
            self.initialize()

        if not self.scheduler or not self.scheduler.running:
            if not self.scheduler:
                # 如果调度器被停止后为None，重新初始化
                self.initialize()
            self.scheduler.start()
            print("⏰ 登录状态维持调度器已启动")
            logger.info("登录状态维持调度器已启动")
        else:
            print("⚠️  调度器已经在运行")
            logger.warning("调度器已经在运行")
    
    def stop(self):
        """停止调度器"""
        if self.scheduler and self.scheduler.running:
            try:
                # 优雅关闭，等待当前任务完成
                self.scheduler.shutdown(wait=True)
                self.scheduler = None  # 清空调度器引用
                self.is_initialized = False  # 重置初始化标志
                print("🛑 登录状态维持调度器已停止")
                logger.info("登录状态维持调度器已停止")
            except Exception as e:
                # 如果优雅关闭失败，强制关闭
                try:
                    self.scheduler.shutdown(wait=False)
                    self.scheduler = None  # 清空调度器引用
                    self.is_initialized = False  # 重置初始化标志
                    print(f"⚠️  调度器强制停止: {e}")
                    logger.warning(f"调度器强制停止: {e}")
                except Exception as e2:
                    print(f"❌ 调度器停止失败: {e2}")
                    logger.error(f"调度器停止失败: {e2}")
        else:
            print("⚠️  调度器未运行")
            logger.warning("调度器未运行")
    
    def pause_job(self):
        """暂停登录状态维持任务"""
        if self.scheduler and self.scheduler.running:
            try:
                self.scheduler.pause_job(self.job_id)
                logger.info("登录状态维持任务已暂停")
                return True
            except Exception as e:
                logger.error(f"暂停任务失败: {e}")
                return False
        return False
    
    def resume_job(self):
        """恢复登录状态维持任务"""
        if self.scheduler and self.scheduler.running:
            try:
                self.scheduler.resume_job(self.job_id)
                logger.info("登录状态维持任务已恢复")
                return True
            except Exception as e:
                logger.error(f"恢复任务失败: {e}")
                return False
        return False
    
    def modify_job_interval(self, minutes: int):
        """修改任务执行间隔"""
        if self.scheduler and self.scheduler.running:
            try:
                self.scheduler.modify_job(
                    self.job_id,
                    trigger=IntervalTrigger(minutes=minutes)
                )
                # 同时更新keeper服务的配置
                self.keeper_service.config["interval_minutes"] = minutes
                logger.info(f"任务执行间隔已修改为 {minutes} 分钟")
                return True
            except Exception as e:
                logger.error(f"修改任务间隔失败: {e}")
                return False
        return False
    
    async def trigger_job_now(self):
        """立即触发一次登录状态维持任务"""
        try:
            logger.info("手动触发登录状态维持任务")
            result = await self.keeper_service.maintain_all_login_states()
            return result
        except Exception as e:
            logger.error(f"手动触发任务失败: {e}")
            return {"success": False, "error": str(e)}
    
    def get_job_status(self):
        """获取任务状态"""
        if not self.scheduler:
            return {
                "scheduler_running": False,
                "job_exists": False,
                "job_paused": False,
                "next_run_time": None
            }
        
        job = self.scheduler.get_job(self.job_id)
        
        return {
            "scheduler_running": self.scheduler.running,
            "job_exists": job is not None,
            "job_paused": job.next_run_time is None if job else False,
            "next_run_time": job.next_run_time.isoformat() if job and job.next_run_time else None,
            "keeper_service_status": self.keeper_service.get_status()
        }
    
    def get_scheduler_info(self):
        """获取调度器信息"""
        if not self.scheduler:
            return {"error": "调度器未初始化"}
        
        jobs = []
        for job in self.scheduler.get_jobs():
            jobs.append({
                "id": job.id,
                "name": job.name,
                "next_run_time": job.next_run_time.isoformat() if job.next_run_time else None,
                "trigger": str(job.trigger)
            })
        
        return {
            "running": self.scheduler.running,
            "jobs": jobs,
            "timezone": str(self.scheduler.timezone)
        }

# 全局调度器实例
login_keeper_scheduler = LoginStateKeeperScheduler()

def get_login_keeper_scheduler() -> LoginStateKeeperScheduler:
    """获取登录状态维持调度器实例"""
    return login_keeper_scheduler

async def start_login_keeper():
    """启动登录状态维持服务"""
    try:
        print("🚀 正在启动登录状态维持服务...")
        scheduler = get_login_keeper_scheduler()
        scheduler.start()
        print("✅ 登录状态维持服务启动成功")
        logger.info("登录状态维持服务启动成功")
    except Exception as e:
        print(f"❌ 启动登录状态维持服务失败: {e}")
        logger.error(f"启动登录状态维持服务失败: {e}")

async def stop_login_keeper():
    """停止登录状态维持服务"""
    try:
        scheduler = get_login_keeper_scheduler()
        scheduler.stop()
        logger.info("登录状态维持服务停止成功")
    except Exception as e:
        logger.error(f"停止登录状态维持服务失败: {e}")
