"""
登录状态维持服务配置
"""
import os
from typing import Dict, List, Tuple

# 基础配置
KEEPER_CONFIG = {
    # 维持间隔（分钟）
    "interval_minutes": int(os.getenv("LOGIN_KEEPER_INTERVAL", "30")),
    
    # 启用的平台
    "enabled_platforms": os.getenv("LOGIN_KEEPER_PLATFORMS", "wechat_mp,xiaohongshu,wechat_channels").split(","),
    
    # 最大重试次数
    "max_retries": int(os.getenv("LOGIN_KEEPER_MAX_RETRIES", "3")),
    
    # 页面停留时间范围（秒）
    "page_stay_seconds": (5, 15),
    
    # 同时处理的账号数量
    "concurrent_accounts": int(os.getenv("LOGIN_KEEPER_CONCURRENT", "3")),
    
    # 是否启用登录状态维持
    "enabled": os.getenv("LOGIN_KEEPER_ENABLED", "true").lower() == "true",
    
    # 日志级别
    "log_level": os.getenv("LOGIN_KEEPER_LOG_LEVEL", "INFO"),
    
    # 账号间延迟时间（秒）
    "account_delay_seconds": (10, 30),
    
    # 浏览器超时时间（秒）
    "browser_timeout": int(os.getenv("LOGIN_KEEPER_TIMEOUT", "60")),
}

# 注意：平台页面配置和验证规则已移至 app/services/platform_pages_config.py

def get_config() -> Dict:
    """获取配置"""
    return KEEPER_CONFIG

# 这些函数已移至 PlatformPagesConfig 类中

def is_keeper_enabled() -> bool:
    """检查是否启用登录状态维持"""
    return KEEPER_CONFIG["enabled"]

def get_interval_minutes() -> int:
    """获取维持间隔（分钟）"""
    return KEEPER_CONFIG["interval_minutes"]
