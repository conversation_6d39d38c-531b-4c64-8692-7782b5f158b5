"""
微信视频号服务配置
"""

import os


class WeChatChannelsConfig:
    """微信视频号服务配置类"""
    
    # 网络超时配置（毫秒）- 针对服务器环境优化
    DEFAULT_TIMEOUT = 60000  # 增加到60秒
    NAVIGATION_TIMEOUT = 90000  # 增加到90秒（更适合微信视频号页面）
    NETWORK_CHECK_TIMEOUT = 30000  # 增加到30秒
    BROWSER_LAUNCH_TIMEOUT = 90000  # 浏览器启动超时90秒
    WECHAT_CHANNELS_NAVIGATION_TIMEOUT = 30000  # 增加到30秒
    # 重试配置
    MAX_RETRIES = 2
    RETRY_DELAY_BASE = 5  # 基础延迟秒数
    
    # 浏览器配置
    BROWSER_ARGS_BASE = [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu',
        '--disable-web-security',
        '--disable-background-timer-throttling',
        '--disable-renderer-backgrounding',
        '--disable-backgrounding-occluded-windows',
        '--disable-ipc-flooding-protection',
        '--no-default-browser-check',
        '--disable-default-apps',
        '--disable-component-update',
        '--disable-features=VizDisplayCompositor,TranslateUI',
    ]
    

    
    @classmethod
    def get_timeout(cls, timeout_type: str, is_docker_wsl: bool = False) -> int:
        """获取超时时间
        
        Args:
            timeout_type: 超时类型 ('default', 'navigation', 'network_check')
            is_docker_wsl: 是否为Docker/WSL环境
            
        Returns:
            超时时间（毫秒）
        """
        # 从环境变量获取自定义超时时间
        env_key = f"WECHAT_CHANNELS_{timeout_type.upper()}_TIMEOUT"
        env_timeout = os.environ.get(env_key)
        if env_timeout:
            try:
                return int(env_timeout)
            except ValueError:
                pass
        
        # 使用统一的超时配置，针对服务器环境优化
        timeout_map = {
            'default': cls.DEFAULT_TIMEOUT,
            'navigation': cls.NAVIGATION_TIMEOUT,
            'network_check': cls.NETWORK_CHECK_TIMEOUT,
            'browser_launch': cls.BROWSER_LAUNCH_TIMEOUT,
        }

        return timeout_map.get(timeout_type, cls.DEFAULT_TIMEOUT)
    
    @classmethod
    def get_max_retries(cls) -> int:
        """获取最大重试次数"""
        env_retries = os.environ.get("WECHAT_CHANNELS_MAX_RETRIES")
        if env_retries:
            try:
                return int(env_retries)
            except ValueError:
                pass
        return cls.MAX_RETRIES
    
    @classmethod
    def get_retry_delay(cls, attempt: int) -> int:
        """获取重试延迟时间
        
        Args:
            attempt: 当前重试次数（从0开始）
            
        Returns:
            延迟时间（秒）
        """
        base_delay = int(os.environ.get("WECHAT_CHANNELS_RETRY_DELAY", cls.RETRY_DELAY_BASE))
        return base_delay * (attempt + 1)
    
    @classmethod
    def get_browser_args(cls, is_docker_wsl: bool = False, headless: bool = True) -> list:
        """获取浏览器启动参数

        Args:
            is_docker_wsl: 已弃用，保留兼容性
            headless: 是否为无头模式

        Returns:
            浏览器启动参数列表
        """
        args = cls.BROWSER_ARGS_BASE.copy()

        # 添加通用配置
        args.append('--disable-accelerated-2d-canvas')

        if headless:
            args.append('--single-process')
        else:
            args.append('--disable-extensions')

        # 从环境变量获取额外参数
        extra_args = os.environ.get("WECHAT_CHANNELS_BROWSER_ARGS")
        if extra_args:
            args.extend(extra_args.split(','))

        return args
    
    @classmethod
    def is_debug_mode(cls) -> bool:
        """是否为调试模式"""
        return os.environ.get("WECHAT_CHANNELS_DEBUG", "false").lower() == "true"


# 环境变量配置示例：
# export WECHAT_CHANNELS_NAVIGATION_TIMEOUT=120000  # 2分钟导航超时
# export WECHAT_CHANNELS_MAX_RETRIES=5              # 最多重试5次
# export WECHAT_CHANNELS_RETRY_DELAY=10             # 重试间隔10秒
# export WECHAT_CHANNELS_DEBUG=true                 # 启用调试模式
# export WECHAT_CHANNELS_BROWSER_ARGS="--proxy-server=http://proxy:8080"  # 额外浏览器参数
