import os
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from urllib.parse import quote_plus

def get_database_url():
    """获取数据库连接URL"""
    # 优先使用完整的DATABASE_URL环境变量
    database_url = os.getenv("DATABASE_URL")
    if database_url:
        return database_url

    # 如果没有完整URL，则从各个组件构建MySQL连接
    db_host = os.getenv("DB_HOST", "localhost")
    db_port = os.getenv("DB_PORT", "3306")
    db_user = os.getenv("DB_USER", "root")
    db_password = os.getenv("DB_PASSWORD", "")
    db_name = os.getenv("DB_NAME", "social_media_manager")

    # URL编码密码以处理特殊字符
    encoded_password = quote_plus(db_password) if db_password else ""

    if encoded_password:
        mysql_url = f"mysql+pymysql://{db_user}:{encoded_password}@{db_host}:{db_port}/{db_name}?charset=utf8mb4"
    else:
        mysql_url = f"mysql+pymysql://{db_user}@{db_host}:{db_port}/{db_name}?charset=utf8mb4"

    return mysql_url

# 获取数据库连接URL
DATABASE_URL = get_database_url()

# 创建数据库引擎，针对MySQL优化连接池配置
if DATABASE_URL.startswith('mysql'):
    engine = create_engine(
        DATABASE_URL,
        pool_size=10,
        max_overflow=20,
        pool_pre_ping=True,
        pool_recycle=3600,
        echo=False
    )
else:
    # SQLite配置（向后兼容）
    engine = create_engine(DATABASE_URL)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()