"""
自定义异常类定义
"""

class LoginExpiredException(Exception):
    """登录过期异常
    
    当检测到平台登录状态已过期时抛出此异常。
    上级调用者应该捕获此异常并停止后续操作。
    """
    
    def __init__(self, platform: str, account_id: int = None, message: str = None):
        """初始化登录过期异常
        
        Args:
            platform: 平台类型 (如 'wechat_channels', 'xiaohongshu' 等)
            account_id: 账号ID
            message: 自定义错误消息
        """
        self.platform = platform
        self.account_id = account_id
        
        if message is None:
            if account_id:
                message = f"账号 {account_id} 在平台 {platform} 的登录状态已过期，请重新登录"
            else:
                message = f"平台 {platform} 的登录状态已过期，请重新登录"
        
        self.message = message
        super().__init__(self.message)
    
    def __str__(self):
        return self.message
    
    def __repr__(self):
        return f"LoginExpiredException(platform='{self.platform}', account_id={self.account_id}, message='{self.message}')"


class PlatformServiceException(Exception):
    """平台服务异常基类
    
    所有平台服务相关的异常都应该继承此类。
    """
    
    def __init__(self, platform: str, account_id: int = None, message: str = None):
        """初始化平台服务异常
        
        Args:
            platform: 平台类型
            account_id: 账号ID
            message: 错误消息
        """
        self.platform = platform
        self.account_id = account_id
        self.message = message or "平台服务异常"
        super().__init__(self.message)


class NetworkException(PlatformServiceException):
    """网络异常
    
    当网络连接失败或超时时抛出此异常。
    """
    pass


class BrowserException(PlatformServiceException):
    """浏览器异常
    
    当浏览器操作失败时抛出此异常。
    """
    pass


class DataParsingException(PlatformServiceException):
    """数据解析异常
    
    当数据解析失败时抛出此异常。
    """
    pass
