from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import Optional

from app.database import get_db
from app.routers.auth import get_current_user
from app.services.auto_update_service import AutoUpdateService

router = APIRouter(prefix="/auto-update", tags=["auto-update"])


class AutoUpdateConfigRequest(BaseModel):
    enabled: bool
    update_time: str  # HH:MM format
    update_days: int = 30


class AutoUpdateConfigResponse(BaseModel):
    success: bool
    data: Optional[dict] = None
    error: Optional[str] = None


@router.get("/config")
async def get_auto_update_config(
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取自动更新配置"""
    try:
        config = AutoUpdateService.get_config(db)
        
        if config:
            return {
                "success": True,
                "data": {
                    "id": config.id,
                    "enabled": config.enabled,
                    "update_time": config.update_time,
                    "update_days": config.update_days,
                    "last_update": config.last_update.isoformat() if config.last_update else None,
                    "created_at": config.created_at.isoformat(),
                    "updated_at": config.updated_at.isoformat()
                }
            }
        else:
            return {
                "success": True,
                "data": {
                    "enabled": False,
                    "update_time": "02:00",
                    "update_days": 30,
                    "last_update": None
                }
            }
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取配置失败: {str(e)}"
        )


@router.post("/config")
async def update_auto_update_config(
    config_request: AutoUpdateConfigRequest,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新自动更新配置"""
    try:
        result = AutoUpdateService.create_or_update_config(
            db=db,
            enabled=config_request.enabled,
            update_time=config_request.update_time,
            update_days=config_request.update_days
        )
        
        if not result["success"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["error"]
            )
        
        return result
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新配置失败: {str(e)}"
        )


@router.get("/status")
async def get_auto_update_status(
    current_user = Depends(get_current_user)
):
    """获取自动更新状态"""
    try:
        status_info = AutoUpdateService.get_status()
        return {
            "success": True,
            "data": status_info
        }
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取状态失败: {str(e)}"
        )


@router.post("/start")
async def start_auto_update_scheduler(
    current_user = Depends(get_current_user)
):
    """启动自动更新调度器"""
    try:
        AutoUpdateService.start_scheduler()
        return {
            "success": True,
            "message": "自动更新调度器已启动"
        }
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"启动调度器失败: {str(e)}"
        )


@router.post("/stop")
async def stop_auto_update_scheduler(
    current_user = Depends(get_current_user)
):
    """停止自动更新调度器"""
    try:
        AutoUpdateService.stop_scheduler()
        return {
            "success": True,
            "message": "自动更新调度器已停止"
        }
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"停止调度器失败: {str(e)}"
        )


@router.post("/test")
async def test_auto_update(
    current_user = Depends(get_current_user)
):
    """测试自动更新功能"""
    try:
        await AutoUpdateService.execute_auto_update()
        return {
            "success": True,
            "message": "测试自动更新完成"
        }
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"测试自动更新失败: {str(e)}"
        )
