from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel
from app.database import get_db
from app.routers.auth import get_current_user
from app.services.data_details_service import DataDetailsService
from app.models import PlatformAccount
from app.exceptions import LoginExpiredException

router = APIRouter()

# Pydantic模型
class DataListResponse(BaseModel):
    success: bool
    data: list = []
    total: int = 0
    page: int = 1
    page_size: int = 20
    total_pages: int = 0
    error: Optional[str] = None

class DataSummaryResponse(BaseModel):
    success: bool
    total_records: int = 0
    latest_time: Optional[str] = None
    error: Optional[str] = None

@router.get("/wechat-mp/accounts")
async def get_wechat_mp_accounts(
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取用户的微信公众号账号列表"""

    accounts = db.query(PlatformAccount).filter(
        PlatformAccount.user_id == current_user.id,
        (PlatformAccount.platform == "wechat_mp") | (PlatformAccount.platform == "wechat_service")
    ).all()

    account_list = []
    for account in accounts:
        account_list.append({
            "id": account.id,
            "name": account.name,
            "login_status": account.login_status,
            "last_login_time": account.last_login_time.isoformat() if account.last_login_time else None,
            "created_at": account.created_at.isoformat()
        })

    return {
        "success": True,
        "accounts": account_list
    }

@router.get("/wechat-mp/config")
async def get_wechat_mp_config():
    """获取微信公众号数据类型配置信息"""
    # 只返回微信公众号相关的配置
    wechat_mp_config = {
        k: v for k, v in DATA_TYPE_CONFIG.items()
        if k in ['content_trend', 'content_source', 'content_detail', 'user_channel', 'user_source']
    }
    return {
        "success": True,
        "data_types": wechat_mp_config
    }


@router.get("/wechat-channels/accounts")
async def get_wechat_channels_accounts(
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取用户的微信视频号账号列表"""

    accounts = db.query(PlatformAccount).filter(
        PlatformAccount.user_id == current_user.id,
        PlatformAccount.platform == "wechat_channels"
    ).all()

    account_list = []
    for account in accounts:
        account_list.append({
            "id": account.id,
            "name": account.name,
            "platform": account.platform,
            "login_status": account.login_status,
            "last_login_time": account.last_login_time.isoformat() if account.last_login_time else None
        })

    return {
        "success": True,
        "accounts": account_list
    }


@router.get("/wechat-channels/config")
async def get_wechat_channels_config():
    """获取微信视频号数据类型配置信息"""
    # 只返回视频号相关的配置
    wechat_channels_config = {
        k: v for k, v in DATA_TYPE_CONFIG.items()
        if k in ['single_video', 'follower_data']
    }
    return {
        "success": True,
        "data_types": wechat_channels_config
    }


@router.post("/wechat-channels/fetch-follower/{account_id}")
async def fetch_wechat_channels_follower(
    account_id: int,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """抓取微信视频号关注者数据"""

    # 验证账号权限
    account = db.query(PlatformAccount).filter(
        PlatformAccount.id == account_id,
        PlatformAccount.user_id == current_user.id,
        PlatformAccount.platform == "wechat_channels"
    ).first()

    if not account:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="账号不存在或无权限访问"
        )

    try:
        # 导入微信视频号服务
        from app.services.wechat_channels_service import WeChatChannelsService

        # 创建服务实例
        service = WeChatChannelsService(account_id=account_id, headless=False)

        # 获取关注者数据
        follower_data = await service.get_follower_data(auto_import=True)

        # 关闭浏览器
        await service.close()

        if follower_data:
            return {
                "success": True,
                "message": "关注者数据抓取成功",
                "data": follower_data
            }
        else:
            return {
                "success": False,
                "message": "关注者数据抓取失败，请检查账号登录状态"
            }

    except LoginExpiredException as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"登录状态已过期: {e.message}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"抓取关注者数据失败: {str(e)}"
        )

@router.get("/wechat-mp/{data_type}", response_model=DataListResponse)
async def get_wechat_mp_data_details(
    data_type: str,
    account_id: Optional[int] = Query(None, description="账号ID，不提供则显示所有账号数据"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页大小"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    sort_field: Optional[str] = Query(None, description="排序字段"),
    sort_order: str = Query("desc", regex="^(asc|desc)$", description="排序方向"),
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取微信公众号数据明细列表

    支持的数据类型:
    - content_trend: 内容数据趋势明细
    - content_source: 内容流量来源明细
    - content_detail: 内容已通知内容明细
    - user_channel: 用户增长明细
    """

    # 验证数据类型，如果无效则返回空数据
    valid_types = ['content_trend', 'content_source', 'content_detail', 'user_channel', 'user_source']
    if data_type not in valid_types:
        # 返回空数据而不是错误，保持用户体验
        return DataListResponse(
            success=True,
            data=[],
            total=0,
            page=page,
            page_size=page_size,
            total_pages=0
        )

    # 验证账号权限
    if account_id is not None:
        # 验证特定账号权限
        account = db.query(PlatformAccount).filter(
            PlatformAccount.id == account_id,
            PlatformAccount.user_id == current_user.id,
            (PlatformAccount.platform == "wechat_mp") | (PlatformAccount.platform == "wechat_service")
        ).first()

        if not account:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="账号不存在或无权限访问"
            )

        # 使用特定账号ID
        account_ids = [account_id]
    else:
        # 获取用户所有的微信公众号账号
        accounts = db.query(PlatformAccount).filter(
            PlatformAccount.user_id == current_user.id,
            (PlatformAccount.platform == "wechat_mp") | (PlatformAccount.platform == "wechat_service")
        ).all()

        if not accounts:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="未找到任何微信公众号账号"
            )

        # 使用所有账号ID
        account_ids = [acc.id for acc in accounts]

    # 获取数据列表
    result = DataDetailsService.get_data_list(
        db=db,
        account_id=account_ids,
        data_type=data_type,
        page=page,
        page_size=page_size,
        search=search,
        sort_field=sort_field,
        sort_order=sort_order
    )

    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=result["error"]
        )

    return DataListResponse(**result)

@router.get("/wechat-mp/{data_type}/summary", response_model=DataSummaryResponse)
async def get_wechat_mp_data_summary(
    data_type: str,
    account_id: int = Query(..., description="账号ID"),
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取微信公众号数据汇总信息"""

    # 验证数据类型
    valid_types = ['content_trend', 'content_source', 'content_detail', 'user_channel', 'user_source']
    if data_type not in valid_types:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"不支持的数据类型: {data_type}"
        )

    # 验证账号权限
    account = db.query(PlatformAccount).filter(
        PlatformAccount.id == account_id,
        PlatformAccount.user_id == current_user.id,
        (PlatformAccount.platform == "wechat_mp") | (PlatformAccount.platform == "wechat_service")
    ).first()

    if not account:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="账号不存在或无权限访问"
        )

    # 获取汇总信息
    result = DataDetailsService.get_data_summary(
        db=db,
        account_id=account_id,
        data_type=data_type
    )

    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=result["error"]
        )

    return DataSummaryResponse(**result)


@router.get("/account-summary")
async def get_multi_platform_account_summary(
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取多平台账号数据汇总（最近4个周五的关注数）
    包含微信公众号、视频号、小红书的数据
    """

    result = DataDetailsService.get_account_summary(db=db)

    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=result["error"]
        )

    return result


@router.get("/growth-summary")
async def get_multi_platform_growth_summary(
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取多平台关注数合计净增长汇总
    包含微信公众号、视频号、小红书的数据
    """

    result = DataDetailsService.get_growth_summary(db=db)

    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=result["error"]
        )

    return result


@router.get("/wechat-mp/overview/account-summary")
async def get_wechat_mp_account_summary(
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取微信公众号账号数据汇总（最近4个周五的关注数）"""

    result = DataDetailsService.get_account_summary(db=db)

    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=result["error"]
        )

    return result


@router.get("/wechat-channels/{data_type}", response_model=DataListResponse)
async def get_wechat_channels_data_details(
    data_type: str,
    account_id: Optional[int] = Query(None, description="账号ID，不提供则显示所有账号数据"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页大小"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    sort_field: Optional[str] = Query(None, description="排序字段"),
    sort_order: str = Query("desc", regex="^(asc|desc)$", description="排序方向"),
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取微信视频号数据明细列表

    支持的数据类型:
    - single_video: 单篇视频数据
    """

    # 验证数据类型，如果无效则返回空数据
    valid_types = ['single_video', 'follower_data']
    if data_type not in valid_types:
        # 返回空数据而不是错误，保持用户体验
        return DataListResponse(
            success=True,
            data=[],
            total=0,
            page=page,
            page_size=page_size,
            total_pages=0
        )

    # 处理账号权限
    if account_id:
        # 验证单个账号权限
        account = db.query(PlatformAccount).filter(
            PlatformAccount.id == account_id,
            PlatformAccount.user_id == current_user.id,
            PlatformAccount.platform == "wechat_channels"
        ).first()

        if not account:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="账号不存在或无权限访问"
            )

        account_ids = account_id
    else:
        # 获取用户的所有视频号账号
        accounts = db.query(PlatformAccount).filter(
            PlatformAccount.user_id == current_user.id,
            PlatformAccount.platform == "wechat_channels"
        ).all()

        if not accounts:
            return DataListResponse(
                success=True,
                data=[],
                total=0,
                page=page,
                page_size=page_size,
                total_pages=0
            )

        # 使用所有账号ID
        account_ids = [acc.id for acc in accounts]

    # 获取数据列表
    result = DataDetailsService.get_data_list(
        db=db,
        account_id=account_ids,
        data_type=data_type,
        page=page,
        page_size=page_size,
        search=search,
        sort_field=sort_field,
        sort_order=sort_order
    )

    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=result["error"]
        )

    return DataListResponse(**result)


@router.get("/wechat-channels/{data_type}/summary", response_model=DataSummaryResponse)
async def get_wechat_channels_data_summary(
    data_type: str,
    account_id: int = Query(..., description="账号ID"),
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取微信视频号数据汇总信息"""

    # 验证数据类型
    valid_types = ['single_video', 'follower_data']
    if data_type not in valid_types:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"不支持的数据类型: {data_type}"
        )

    # 验证账号权限
    account = db.query(PlatformAccount).filter(
        PlatformAccount.id == account_id,
        PlatformAccount.user_id == current_user.id,
        PlatformAccount.platform == "wechat_channels"
    ).first()

    if not account:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="账号不存在或无权限访问"
        )

    # 获取汇总信息
    result = DataDetailsService.get_data_summary(
        db=db,
        account_id=account_id,
        data_type=data_type
    )

    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=result["error"]
        )

    return DataSummaryResponse(**result)


@router.get("/xiaohongshu/accounts")
async def get_xiaohongshu_accounts(
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取用户的小红书账号列表"""

    accounts = db.query(PlatformAccount).filter(
        PlatformAccount.user_id == current_user.id,
        PlatformAccount.platform == "xiaohongshu"
    ).all()

    account_list = []
    for account in accounts:
        account_list.append({
            "id": account.id,
            "name": account.name,
            "platform": account.platform,
            "login_status": account.login_status,
            "last_login_time": account.last_login_time.isoformat() if account.last_login_time else None
        })

    return {
        "success": True,
        "accounts": account_list
    }


@router.get("/xiaohongshu/config")
async def get_xiaohongshu_config():
    """获取小红书数据类型配置信息"""
    # 只返回小红书相关的配置
    xiaohongshu_config = {
        k: v for k, v in DATA_TYPE_CONFIG.items()
        if k in ['note_data', 'account_overview', 'fans_data']
    }
    return {
        "success": True,
        "data_types": xiaohongshu_config
    }


@router.post("/xiaohongshu/fetch-overview/{account_id}")
async def fetch_xiaohongshu_overview(
    account_id: int,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """抓取小红书账号概览数据"""

    # 验证账号权限
    account = db.query(PlatformAccount).filter(
        PlatformAccount.id == account_id,
        PlatformAccount.user_id == current_user.id,
        PlatformAccount.platform == "xiaohongshu"
    ).first()

    if not account:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="账号不存在或无权限访问"
        )

    try:
        # 导入小红书服务
        from app.services.xiaohongshu_service import XiaohongshuService

        # 创建服务实例
        service = XiaohongshuService(account_id=account_id)

        # 获取账号概览数据
        overview_data = await service.get_account_overview_data(auto_import=True)

        if overview_data:
            return {
                "success": True,
                "message": "账号概览数据抓取成功",
                "data": overview_data
            }
        else:
            return {
                "success": False,
                "message": "账号概览数据抓取失败，请检查账号登录状态"
            }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"抓取账号概览数据失败: {str(e)}"
        )


@router.post("/xiaohongshu/fetch-fans/{account_id}")
async def fetch_xiaohongshu_fans(
    account_id: int,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """抓取小红书粉丝数据"""

    # 验证账号权限
    account = db.query(PlatformAccount).filter(
        PlatformAccount.id == account_id,
        PlatformAccount.user_id == current_user.id,
        PlatformAccount.platform == "xiaohongshu"
    ).first()

    if not account:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="账号不存在或无权限访问"
        )

    try:
        # 导入小红书服务
        from app.services.xiaohongshu_service import XiaohongshuService

        # 创建服务实例
        service = XiaohongshuService(account_id=account_id)

        # 获取粉丝数据
        fans_data = await service.get_fans_data(auto_import=True)

        if fans_data:
            return {
                "success": True,
                "message": "粉丝数据抓取成功",
                "data": fans_data
            }
        else:
            return {
                "success": False,
                "message": "粉丝数据抓取失败，请检查账号登录状态"
            }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"抓取粉丝数据失败: {str(e)}"
        )


@router.get("/xiaohongshu/{data_type}", response_model=DataListResponse)
async def get_xiaohongshu_data_details(
    data_type: str,
    account_id: Optional[int] = Query(None, description="账号ID，不提供则显示所有账号数据"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页大小"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    sort_field: Optional[str] = Query(None, description="排序字段"),
    sort_order: str = Query("desc", regex="^(asc|desc)$", description="排序方向"),
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取小红书数据明细列表

    支持的数据类型:
    - note_data: 笔记数据
    """

    # 验证数据类型，如果无效则返回空数据
    valid_types = ['note_data', 'account_overview', 'fans_data']
    if data_type not in valid_types:
        # 返回空数据而不是错误，保持用户体验
        return DataListResponse(
            success=True,
            data=[],
            total=0,
            page=page,
            page_size=page_size,
            total_pages=0
        )

    # 处理账号权限
    if account_id:
        # 验证单个账号权限
        account = db.query(PlatformAccount).filter(
            PlatformAccount.id == account_id,
            PlatformAccount.user_id == current_user.id,
            PlatformAccount.platform == "xiaohongshu"
        ).first()

        if not account:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="账号不存在或无权限访问"
            )

        account_ids = account_id
    else:
        # 获取用户的所有小红书账号
        accounts = db.query(PlatformAccount).filter(
            PlatformAccount.user_id == current_user.id,
            PlatformAccount.platform == "xiaohongshu"
        ).all()

        if not accounts:
            return DataListResponse(
                success=True,
                data=[],
                total=0,
                page=page,
                page_size=page_size,
                total_pages=0
            )

        # 使用所有账号ID
        account_ids = [acc.id for acc in accounts]

    # 获取数据列表
    result = DataDetailsService.get_data_list(
        db=db,
        account_id=account_ids,
        data_type=data_type,
        page=page,
        page_size=page_size,
        search=search,
        sort_field=sort_field,
        sort_order=sort_order
    )

    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=result["error"]
        )

    return DataListResponse(**result)


@router.get("/xiaohongshu/{data_type}/summary", response_model=DataSummaryResponse)
async def get_xiaohongshu_data_summary(
    data_type: str,
    account_id: int = Query(..., description="账号ID"),
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取小红书数据汇总信息"""

    # 验证数据类型
    valid_types = ['note_data', 'account_overview', 'fans_data']
    if data_type not in valid_types:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"不支持的数据类型: {data_type}"
        )

    # 验证账号权限
    account = db.query(PlatformAccount).filter(
        PlatformAccount.id == account_id,
        PlatformAccount.user_id == current_user.id,
        PlatformAccount.platform == "xiaohongshu"
    ).first()

    if not account:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="账号不存在或无权限访问"
        )

    # 获取汇总信息
    result = DataDetailsService.get_data_summary(
        db=db,
        account_id=account_id,
        data_type=data_type
    )

    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=result["error"]
        )

    return DataSummaryResponse(**result)


@router.get("/wechat-mp/overview/growth-summary")
async def get_wechat_mp_growth_summary(
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取微信公众号关注数合计净增长汇总"""

    result = DataDetailsService.get_growth_summary(db=db)

    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=result["error"]
        )

    return result

# 数据类型配置信息
DATA_TYPE_CONFIG = {
    "content_trend": {
        "name": "内容数据趋势明细",
        "description": "包含阅读次数、分享次数、收藏次数等趋势数据",
        "columns": [
            {"key": "account_name", "title": "账号名称", "type": "text"},
            {"key": "date", "title": "日期", "type": "date"},
            {"key": "read_count", "title": "阅读次数", "type": "number"},
            {"key": "read_user_count", "title": "阅读人数", "type": "number"},
            {"key": "share_count", "title": "分享次数", "type": "number"},
            {"key": "share_user_count", "title": "分享人数", "type": "number"},
            {"key": "read_original_count", "title": "阅读原文次数", "type": "number"},
            {"key": "read_original_user_count", "title": "阅读原文人数", "type": "number"},
            {"key": "collect_count", "title": "收藏次数", "type": "number"},
            {"key": "collect_user_count", "title": "收藏人数", "type": "number"},
            {"key": "publish_count", "title": "群发篇数", "type": "number"},
            {"key": "channel", "title": "渠道", "type": "text"},
            {"key": "updated_at", "title": "更新时间", "type": "datetime"}
        ]
    },
    "content_source": {
        "name": "内容流量来源明细",
        "description": "包含不同传播渠道的流量来源数据",
        "columns": [
            {"key": "account_name", "title": "账号名称", "type": "text"},
            {"key": "channel", "title": "传播渠道", "type": "text"},
            {"key": "publish_date", "title": "发表日期", "type": "date"},
            {"key": "title", "title": "内容标题", "type": "text"},
            {"key": "read_count", "title": "阅读次数", "type": "number"},
            {"key": "read_ratio", "title": "阅读次数占比", "type": "text"},
            {"key": "updated_at", "title": "更新时间", "type": "datetime"}
        ]
    },
    "content_detail": {
        "name": "内容已通知内容明细",
        "description": "包含具体文章的详细数据分析",
        "columns": [
            {"key": "account_name", "title": "账号名称", "type": "text"},
            {"key": "title", "title": "内容标题", "type": "text"},
            {"key": "publish_time", "title": "发表时间", "type": "datetime"},
            {"key": "total_read_user_count", "title": "总阅读人数", "type": "number"},
            {"key": "total_read_count", "title": "总阅读次数", "type": "number"},
            {"key": "total_share_user_count", "title": "总分享人数", "type": "number"},
            {"key": "total_share_count", "title": "总分享次数", "type": "number"},
            {"key": "follow_after_read_count", "title": "阅读后关注人数", "type": "number"},
            {"key": "delivery_count", "title": "送达人数", "type": "number"},
            {"key": "mp_message_read_count", "title": "公众号消息阅读次数", "type": "number"},
            {"key": "delivery_read_rate", "title": "送达阅读率", "type": "text"},
            {"key": "first_share_count", "title": "首次分享次数", "type": "number"},
            {"key": "share_generated_read_count", "title": "分享产生阅读次数", "type": "number"},
            {"key": "first_share_rate", "title": "首次分享率", "type": "text"},
            {"key": "avg_read_per_share", "title": "每次分享带来阅读次数", "type": "number"},
            {"key": "read_completion_rate", "title": "阅读完成率", "type": "text"},
            {"key": "content_url", "title": "内容链接", "type": "url"},
            {"key": "updated_at", "title": "更新时间", "type": "datetime"}
        ]
    },
    "user_channel": {
        "name": "用户增长明细",
        "description": "包含用户关注和取消关注的详细数据",
        "columns": [
            {"key": "account_name", "title": "账号名称", "type": "text"},
            {"key": "date", "title": "时间", "type": "date"},
            {"key": "new_follow_count", "title": "新关注人数", "type": "number"},
            {"key": "unfollow_count", "title": "取消关注人数", "type": "number"},
            {"key": "net_follow_count", "title": "净增关注人数", "type": "number"},
            {"key": "total_follow_count", "title": "累积关注人数", "type": "number"},
            {"key": "updated_at", "title": "更新时间", "type": "datetime"}
        ]
    },
    "follower_data": {
        "name": "关注者数据",
        "description": "包含视频号30天内的关注者变化趋势",
        "columns": [
            {"key": "account_name", "title": "账号名称", "type": "text"},
            {"key": "date", "title": "日期", "type": "date"},
            {"key": "net_follower_increase", "title": "净增关注", "type": "number"},
            {"key": "new_followers", "title": "新增关注", "type": "number"},
            {"key": "unfollowers", "title": "取消关注", "type": "number"},
            {"key": "total_followers", "title": "关注者总数", "type": "number"},
            {"key": "updated_at", "title": "更新时间", "type": "datetime"}
        ]
    },
    "user_source": {
        "name": "用户来源明细",
        "description": "包含不同来源渠道的用户增长数据",
        "columns": [
            {"key": "account_name", "title": "账号名称", "type": "text"},
            {"key": "user_source", "title": "用户来源", "type": "user_source"},
            {"key": "date", "title": "日期", "type": "date"},
            {"key": "new_user", "title": "新用户", "type": "number"},
            {"key": "cancel_user", "title": "取消用户", "type": "number"},
            {"key": "netgain_user", "title": "净增用户", "type": "number"},
            {"key": "cumulate_user", "title": "累计用户", "type": "number"},
            {"key": "updated_at", "title": "更新时间", "type": "datetime"}
        ],
        "user_source_mapping": {
            "0": "其他合计",
            "1": "公众号搜索",
            "17": "名片分享",
            "30": "扫描二维码",
            "57": "文章内账号名称",
            "100": "微信广告",
            "161": "他人转载",
            "149": "小程序关注",
            "200": "视频号",
            "201": "直播"
        }
    },
    "single_video": {
        "name": "单篇视频数据",
        "description": "包含视频播放量、喜欢数、评论数等详细数据",
        "columns": [
            {"key": "account_name", "title": "账号名称", "type": "text"},
            {"key": "video_description", "title": "视频描述", "type": "text"},
            # {"key": "video_id", "title": "视频ID", "type": "text"},
            {"key": "publish_time", "title": "发布时间", "type": "datetime"},
            {"key": "completion_rate", "title": "完播率", "type": "text"},
            {"key": "avg_play_duration", "title": "平均播放时长(秒)", "type": "number"},
            {"key": "play_count", "title": "播放量", "type": "number"},
            {"key": "recommend_count", "title": "推荐", "type": "number"},
            {"key": "like_count", "title": "喜欢", "type": "number"},
            {"key": "comment_count", "title": "评论量", "type": "number"},
            {"key": "share_count", "title": "分享量", "type": "number"},
            {"key": "follow_count", "title": "关注量", "type": "number"},
            {"key": "forward_chat_moments", "title": "转发聊天和朋友圈", "type": "number"},
            {"key": "set_as_ringtone", "title": "设为铃声", "type": "number"},
            {"key": "set_as_status", "title": "设为状态", "type": "number"},
            {"key": "set_as_moments_cover", "title": "设为朋友圈封面", "type": "number"},
            {"key": "updated_at", "title": "更新时间", "type": "datetime"}
        ]
    },
    "note_data": {
        "name": "笔记数据",
        "description": "包含笔记观看量、点赞、评论、收藏等详细数据",
        "columns": [
            {"key": "account_name", "title": "账号名称", "type": "text"},
            {"key": "note_title", "title": "笔记标题", "type": "text"},
            {"key": "first_publish_time", "title": "首次发布时间", "type": "datetime"},
            {"key": "content_type", "title": "体裁", "type": "text"},
            {"key": "view_count", "title": "观看量", "type": "number"},
            {"key": "like_count", "title": "点赞", "type": "number"},
            {"key": "comment_count", "title": "评论", "type": "number"},
            {"key": "collect_count", "title": "收藏", "type": "number"},
            {"key": "follow_count", "title": "涨粉", "type": "number"},
            {"key": "share_count", "title": "分享", "type": "number"},
            {"key": "avg_view_duration", "title": "人均观看时长", "type": "number"},
            {"key": "barrage_count", "title": "弹幕", "type": "number"},
            {"key": "updated_at", "title": "更新时间", "type": "datetime"}
        ]
    },
    "account_overview": {
        "name": "账号概览",
        "description": "包含账号30天内的各项数据趋势",
        "columns": [
            {"key": "account_name", "title": "账号名称", "type": "text"},
            {"key": "date", "title": "日期", "type": "date"},
            {"key": "view_count", "title": "观看量", "type": "number"},
            {"key": "view_time_count", "title": "观看总时长", "type": "number"},
            {"key": "home_view_count", "title": "主页访客量", "type": "number"},
            {"key": "like_count", "title": "点赞数", "type": "number"},
            {"key": "collect_count", "title": "收藏数", "type": "number"},
            {"key": "comment_count", "title": "评论数", "type": "number"},
            {"key": "danmaku_count", "title": "弹幕数", "type": "number"},
            {"key": "rise_fans_count", "title": "涨粉数", "type": "number"},
            {"key": "share_count", "title": "分享数", "type": "number"},
            {"key": "updated_at", "title": "更新时间", "type": "datetime"}
        ]
    },
    "fans_data": {
        "name": "粉丝数据",
        "description": "包含账号30天内的粉丝变化趋势",
        "columns": [
            {"key": "account_name", "title": "账号名称", "type": "text"},
            {"key": "date", "title": "日期", "type": "date"},
            {"key": "total_fans_count", "title": "总关注数", "type": "number"},
            {"key": "new_fans_count", "title": "新增关注数", "type": "number"},
            {"key": "unfans_count", "title": "取关数", "type": "number"},
            {"key": "updated_at", "title": "更新时间", "type": "datetime"}
        ]
    }
}


