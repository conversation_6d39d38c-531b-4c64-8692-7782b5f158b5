from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel
from app.database import get_db
from app.routers.auth import get_current_user
from app.services.data_update_service import DataUpdateService
from app.models import DataUpdateRecord, DataUpdateTaskItem
from app.services.data_update_service import TaskItemStatus

router = APIRouter()

# Pydantic模型
class DataUpdateRequest(BaseModel):
    start_date: str  # YYYY-MM-DD格式
    end_date: str    # YYYY-MM-DD格式

class DataUpdateResponse(BaseModel):
    success: bool
    task_id: Optional[int] = None
    total_accounts: Optional[int] = None
    message: Optional[str] = None
    error: Optional[str] = None

class UpdateStatusResponse(BaseModel):
    success: bool
    task_id: Optional[int] = None
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    status: Optional[str] = None
    total_accounts: Optional[int] = None
    completed_accounts: Optional[int] = None
    current_account_name: Optional[str] = None
    current_step: Optional[str] = None
    error_message: Optional[str] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    completed_at: Optional[str] = None
    progress_percent: Optional[float] = None
    error: Optional[str] = None

class DataRangeResponse(BaseModel):
    success: bool
    min_date: Optional[str] = None
    max_date: Optional[str] = None
    total_records: Optional[int] = None
    last_update_time: Optional[str] = None
    last_update_status: Optional[str] = None
    error: Optional[str] = None

class UpdateHistoryResponse(BaseModel):
    success: bool
    data: list = []
    total: int = 0
    error: Optional[str] = None


@router.post("/start", response_model=DataUpdateResponse)
async def start_data_update(
    request: DataUpdateRequest,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """启动数据更新任务"""
    
    result = await DataUpdateService.start_data_update(
        start_date=request.start_date,
        end_date=request.end_date
    )
    
    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result["error"]
        )
    
    return DataUpdateResponse(**result)


@router.get("/status/{task_id}", response_model=UpdateStatusResponse)
async def get_update_status(
    task_id: int,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取数据更新任务状态"""
    
    result = DataUpdateService.get_update_status(db=db, task_id=task_id)
    
    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=result["error"]
        )
    
    return UpdateStatusResponse(**result)


@router.get("/current-range", response_model=DataRangeResponse)
async def get_current_data_range(
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取当前系统中的数据日期范围"""
    
    result = DataUpdateService.get_current_data_range(db=db)
    
    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=result["error"]
        )
    
    return DataRangeResponse(**result)


@router.get("/history", response_model=UpdateHistoryResponse)
async def get_update_history(
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取历史更新记录"""
    
    try:
        # 查询最近的20条更新记录
        records = db.query(DataUpdateRecord).order_by(
            DataUpdateRecord.created_at.desc()
        ).limit(20).all()
        
        data = []
        for record in records:
            # 计算实际进度：基于已完成的任务项（包括成功和失败）
            total_items = db.query(DataUpdateTaskItem).filter(
                DataUpdateTaskItem.update_record_id == record.id
            ).count()

            completed_items = db.query(DataUpdateTaskItem).filter(
                DataUpdateTaskItem.update_record_id == record.id,
                DataUpdateTaskItem.status.in_([TaskItemStatus.COMPLETED, TaskItemStatus.FAILED])
            ).count()

            # 计算进度百分比
            progress_percent = round((completed_items / total_items) * 100, 1) if total_items > 0 else 0

            data.append({
                "id": record.id,
                "start_date": record.start_date.isoformat(),
                "end_date": record.end_date.isoformat(),
                "status": record.status,
                "total_accounts": record.total_accounts,
                "completed_accounts": record.completed_accounts,
                "current_account_name": record.current_account_name,
                "current_step": record.current_step,
                "error_message": record.error_message,
                "created_at": record.created_at.isoformat(),
                "updated_at": record.updated_at.isoformat(),
                "completed_at": record.completed_at.isoformat() if record.completed_at else None,
                "progress_percent": progress_percent,
                "duration": None  # 可以计算任务耗时
            })
            
            # 计算任务耗时
            if record.completed_at and record.created_at:
                duration_seconds = (record.completed_at - record.created_at).total_seconds()
                data[-1]["duration"] = f"{int(duration_seconds // 60)}分{int(duration_seconds % 60)}秒"
        
        return UpdateHistoryResponse(
            success=True,
            data=data,
            total=len(data)
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取历史记录失败: {str(e)}"
        )


@router.get("/running-task")
async def get_running_task(
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取当前正在运行的任务"""
    
    try:
        running_task = db.query(DataUpdateRecord).filter(
            DataUpdateRecord.status == 'running'
        ).first()
        
        if not running_task:
            return {"success": True, "has_running_task": False}
        
        result = DataUpdateService.get_update_status(db=db, task_id=running_task.id)
        return {
            "success": True,
            "has_running_task": True,
            "task": result
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取运行任务失败: {str(e)}"
        )


# 任务明细相关的Pydantic模型
class TaskItemResponse(BaseModel):
    id: int
    account_name: str
    platform: str
    data_type: str
    data_type_display: str
    status: str
    error_message: Optional[str]
    started_at: Optional[str]
    completed_at: Optional[str]
    can_retry: bool

class TaskItemsResponse(BaseModel):
    success: bool
    items: list[TaskItemResponse]
    total: int
    page: int
    page_size: int
    stats: dict
    error: Optional[str] = None

class TaskSummaryResponse(BaseModel):
    total: int
    pending: int
    running: int
    completed: int
    failed: int
    retrying: int
    success_rate: float

class RetryTaskResponse(BaseModel):
    success: bool
    error: Optional[str] = None
    task_item_id: Optional[int] = None
    account_name: Optional[str] = None
    data_type_display: Optional[str] = None


@router.get("/tasks/{record_id}/items", response_model=TaskItemsResponse)
async def get_task_items(
    record_id: int,
    page: int = 1,
    page_size: int = 20,
    status: Optional[str] = None,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取数据更新任务明细列表"""
    try:
        # 验证更新记录是否存在
        update_record = db.query(DataUpdateRecord).filter(
            DataUpdateRecord.id == record_id
        ).first()

        if not update_record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="更新记录不存在"
            )

        # 获取任务明细
        result = DataUpdateService.get_task_items(
            record_id=record_id,
            page=page,
            page_size=page_size,
            status=status
        )

        return TaskItemsResponse(
            success=True,
            items=[
                TaskItemResponse(
                    id=item["id"],
                    account_name=item["account_name"],
                    platform=item["platform"],
                    data_type=item["data_type"],
                    data_type_display=item["data_type_display"],
                    status=item["status"],
                    error_message=item["error_message"],
                    started_at=item["started_at"].isoformat() if item["started_at"] else None,
                    completed_at=item["completed_at"].isoformat() if item["completed_at"] else None,
                    can_retry=item["can_retry"]
                )
                for item in result["items"]
            ],
            total=result["total"],
            page=result["page"],
            page_size=result["page_size"],
            stats=result["stats"]
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取任务明细失败: {str(e)}"
        )


@router.post("/tasks/items/{item_id}/retry", response_model=RetryTaskResponse)
async def retry_task_item(
    item_id: int,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """重试单个任务项"""
    try:
        result = await DataUpdateService.retry_task_item(
            item_id=item_id,
            user_id=current_user.id
        )

        return RetryTaskResponse(
            success=result["success"],
            error=result.get("error"),
            task_item_id=result.get("task_item_id"),
            account_name=result.get("account_name"),
            data_type_display=result.get("data_type_display")
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"重试任务失败: {str(e)}"
        )


@router.get("/tasks/{record_id}/summary", response_model=TaskSummaryResponse)
async def get_task_summary(
    record_id: int,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取任务汇总信息（各状态的数量统计）"""
    try:
        # 验证更新记录是否存在
        update_record = db.query(DataUpdateRecord).filter(
            DataUpdateRecord.id == record_id
        ).first()

        if not update_record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="更新记录不存在"
            )

        # 获取任务明细统计
        result = DataUpdateService.get_task_items(
            record_id=record_id,
            page=1,
            page_size=1  # 只需要统计信息
        )

        stats = result["stats"]
        total = result["total"]

        # 计算成功率
        completed = stats.get("completed", 0)
        success_rate = (completed / total * 100) if total > 0 else 0

        return TaskSummaryResponse(
            total=total,
            pending=stats.get("pending", 0),
            running=stats.get("running", 0),
            completed=completed,
            failed=stats.get("failed", 0),
            retrying=stats.get("retrying", 0),
            success_rate=round(success_rate, 2)
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取任务汇总失败: {str(e)}"
        )


@router.post("/tasks/{record_id}/stop")
async def stop_task(
    record_id: int,
    current_user: dict = Depends(get_current_user)
):
    """停止正在运行的数据更新任务"""
    try:
        result = await DataUpdateService.stop_data_update_task(record_id)

        if result["success"]:
            return {"success": True, "message": result["message"]}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["error"]
            )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"停止任务失败: {str(e)}"
        )


@router.delete("/tasks/{record_id}")
async def delete_task(
    record_id: int,
    current_user: dict = Depends(get_current_user)
):
    """删除数据更新任务记录"""
    try:
        result = await DataUpdateService.delete_data_update_task(record_id)

        if result["success"]:
            return {"success": True, "message": result["message"]}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["error"]
            )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除任务失败: {str(e)}"
        )
