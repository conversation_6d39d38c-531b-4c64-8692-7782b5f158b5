"""
登录状态维持管理API
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import Dict, Any

from app.database import get_db
from app.models import User
from app.routers.auth import get_current_user
from app.background.login_state_keeper import get_login_keeper_scheduler
from app.config.keeper_config import is_keeper_enabled

router = APIRouter(prefix="/api/login-keeper", tags=["登录状态维持"])

@router.get("/status")
async def get_keeper_status(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """获取登录状态维持服务状态"""
    try:
        scheduler = get_login_keeper_scheduler()
        job_status = scheduler.get_job_status()
        scheduler_info = scheduler.get_scheduler_info()
        
        return {
            "success": True,
            "enabled": is_keeper_enabled(),
            "job_status": job_status,
            "scheduler_info": scheduler_info
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取服务状态失败: {str(e)}"
        )

@router.post("/start")
async def start_keeper_service(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """启动登录状态维持服务"""
    try:
        if not is_keeper_enabled():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="登录状态维持服务已在配置中禁用"
            )
        
        scheduler = get_login_keeper_scheduler()

        # 检查当前状态
        job_status = scheduler.get_job_status()

        if job_status["scheduler_running"] and job_status["job_exists"] and job_status["job_paused"]:
            # 如果调度器在运行但任务被暂停，则恢复任务
            success = scheduler.resume_job()
            if success:
                return {
                    "success": True,
                    "message": "登录状态维持任务已恢复"
                }
            else:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="恢复任务失败"
                )
        else:
            # 否则启动调度器
            scheduler.start()
            return {
                "success": True,
                "message": "登录状态维持服务已启动"
            }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"启动服务失败: {str(e)}"
        )

@router.post("/stop")
async def stop_keeper_service(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """停止登录状态维持服务"""
    try:
        scheduler = get_login_keeper_scheduler()
        scheduler.stop()
        
        return {
            "success": True,
            "message": "登录状态维持服务已停止"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"停止服务失败: {str(e)}"
        )

@router.post("/pause")
async def pause_keeper_job(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """暂停登录状态维持任务"""
    try:
        scheduler = get_login_keeper_scheduler()
        success = scheduler.pause_job()
        
        if success:
            return {
                "success": True,
                "message": "登录状态维持任务已暂停"
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="暂停任务失败，可能调度器未运行"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"暂停任务失败: {str(e)}"
        )

@router.post("/resume")
async def resume_keeper_job(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """恢复登录状态维持任务"""
    try:
        scheduler = get_login_keeper_scheduler()
        success = scheduler.resume_job()
        
        if success:
            return {
                "success": True,
                "message": "登录状态维持任务已恢复"
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="恢复任务失败，可能调度器未运行"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"恢复任务失败: {str(e)}"
        )

@router.post("/trigger")
async def trigger_keeper_job(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """手动触发一次登录状态维持任务"""
    try:
        scheduler = get_login_keeper_scheduler()
        result = await scheduler.trigger_job_now()
        
        return {
            "success": True,
            "message": "手动触发任务完成",
            "result": result
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"触发任务失败: {str(e)}"
        )

@router.post("/modify-interval")
async def modify_job_interval(
    minutes: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """修改任务执行间隔"""
    try:
        if minutes < 5:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="执行间隔不能少于5分钟"
            )
        
        if minutes > 1440:  # 24小时
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="执行间隔不能超过24小时"
            )
        
        scheduler = get_login_keeper_scheduler()
        success = scheduler.modify_job_interval(minutes)
        
        if success:
            return {
                "success": True,
                "message": f"任务执行间隔已修改为 {minutes} 分钟"
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="修改间隔失败，可能调度器未运行"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"修改间隔失败: {str(e)}"
        )

@router.get("/logs")
async def get_keeper_logs(
    limit: int = 50,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """获取登录状态维持日志"""
    try:
        scheduler = get_login_keeper_scheduler()
        keeper_service = scheduler.keeper_service

        # 获取最近的维持记录
        records = keeper_service.get_recent_records(limit)

        return {
            "success": True,
            "records": records,
            "total": len(records),
            "limit": limit
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取日志失败: {str(e)}"
        )

@router.get("/stats/platform")
async def get_platform_stats(
    days: int = 7,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """获取各平台的维持统计"""
    try:
        if days < 1 or days > 90:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="天数范围应在1-90之间"
            )

        scheduler = get_login_keeper_scheduler()
        keeper_service = scheduler.keeper_service

        stats = keeper_service.get_platform_stats(days)

        return {
            "success": True,
            "stats": stats,
            "days": days
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取平台统计失败: {str(e)}"
        )

@router.get("/stats/account/{account_id}")
async def get_account_stats(
    account_id: int,
    days: int = 7,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """获取指定账号的维持统计"""
    try:
        if days < 1 or days > 90:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="天数范围应在1-90之间"
            )

        # 验证账号权限
        from app.models import PlatformAccount
        account = db.query(PlatformAccount).filter(
            PlatformAccount.id == account_id,
            PlatformAccount.user_id == current_user.id
        ).first()

        if not account:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="账号不存在或无权限访问"
            )

        scheduler = get_login_keeper_scheduler()
        keeper_service = scheduler.keeper_service

        stats = keeper_service.get_account_stats(account_id, days)
        stats["account_name"] = account.name
        stats["platform"] = account.platform

        return {
            "success": True,
            "stats": stats
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取账号统计失败: {str(e)}"
        )

@router.get("/accounts")
async def get_keeper_accounts(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """获取可维持登录状态的账号列表"""
    try:
        from app.models import PlatformAccount
        from app.services.platform_pages_config import PlatformPagesConfig

        # 获取用户的所有账号
        accounts = db.query(PlatformAccount).filter(
            PlatformAccount.user_id == current_user.id
        ).all()

        result = []
        for account in accounts:
            # 检查平台是否支持
            is_supported = PlatformPagesConfig.is_platform_supported(account.platform)
            platform_info = PlatformPagesConfig.get_platform_info(account.platform)

            result.append({
                "id": account.id,
                "name": account.name,
                "platform": account.platform,
                "platform_name": platform_info.get("name", account.platform),
                "login_status": account.login_status,
                "last_login_time": account.last_login_time.isoformat() if account.last_login_time else None,
                "is_supported": is_supported,
                "created_at": account.created_at.isoformat()
            })

        return {
            "success": True,
            "accounts": result,
            "total": len(result),
            "supported_platforms": PlatformPagesConfig.get_all_supported_platforms()
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取账号列表失败: {str(e)}"
        )

@router.post("/test-account/{account_id}")
async def test_account_login(
    account_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """测试指定账号的登录状态"""
    try:
        from app.models import PlatformAccount

        # 验证账号权限
        account = db.query(PlatformAccount).filter(
            PlatformAccount.id == account_id,
            PlatformAccount.user_id == current_user.id
        ).first()

        if not account:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="账号不存在或无权限访问"
            )

        scheduler = get_login_keeper_scheduler()
        keeper_service = scheduler.keeper_service

        # 执行单次维持测试
        result = await keeper_service._maintain_account_login_attempt(account)

        return {
            "success": True,
            "test_result": result,
            "message": "账号登录状态测试完成"
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"测试账号登录状态失败: {str(e)}"
        )

@router.get("/config")
async def get_keeper_config(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """获取登录状态维持配置"""
    try:
        from app.config.keeper_config import get_config
        from app.services.platform_pages_config import PlatformPagesConfig

        config = get_config()

        # 获取平台信息
        platform_info = {}
        for platform in PlatformPagesConfig.get_all_supported_platforms():
            info = PlatformPagesConfig.get_platform_info(platform)
            pages = PlatformPagesConfig.get_platform_pages(platform)
            validation = PlatformPagesConfig.get_validation_rules(platform)

            platform_info[platform] = {
                "info": info,
                "pages_count": len(pages),
                "validation_rules": {
                    "success_indicators_count": len(validation.get("success_indicators", [])),
                    "login_required_indicators_count": len(validation.get("login_required_indicators", []))
                }
            }

        return {
            "success": True,
            "config": config,
            "platform_info": platform_info,
            "enabled": is_keeper_enabled()
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取配置失败: {str(e)}"
        )
