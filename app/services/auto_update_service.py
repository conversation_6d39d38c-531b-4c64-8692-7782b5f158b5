import asyncio
import schedule
import time
from datetime import datetime, date, timedelta
from typing import Dict, Any, Optional
from sqlalchemy.orm import Session
from threading import Thread
import logging

from app.models import AutoUpdateConfig, DataUpdateRecord, PlatformAccount
from app.services.data_update_service import DataUpdateService
from app.database import SessionLocal

logger = logging.getLogger(__name__)


class AutoUpdateService:
    """自动更新服务类"""
    
    _instance = None
    _scheduler_thread = None
    _running = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(AutoUpdateService, cls).__new__(cls)
        return cls._instance
    
    @staticmethod
    def get_config(db: Session) -> Optional[AutoUpdateConfig]:
        """获取自动更新配置"""
        return db.query(AutoUpdateConfig).first()
    
    @staticmethod
    def create_or_update_config(
        db: Session, 
        enabled: bool, 
        update_time: str, 
        update_days: int = 30
    ) -> Dict[str, Any]:
        """创建或更新自动更新配置
        
        Args:
            db: 数据库会话
            enabled: 是否启用
            update_time: 更新时间 (HH:MM)
            update_days: 更新天数
            
        Returns:
            操作结果
        """
        try:
            # 验证时间格式
            try:
                datetime.strptime(update_time, "%H:%M")
            except ValueError:
                return {"success": False, "error": "时间格式错误，应为 HH:MM"}
            
            # 验证更新天数
            if update_days < 1 or update_days > 90:
                return {"success": False, "error": "更新天数应在1-90天之间"}
            
            config = db.query(AutoUpdateConfig).first()
            
            if config:
                # 更新现有配置
                config.enabled = enabled
                config.update_time = update_time
                config.update_days = update_days
                config.updated_at = datetime.utcnow()
            else:
                # 创建新配置
                config = AutoUpdateConfig(
                    enabled=enabled,
                    update_time=update_time,
                    update_days=update_days
                )
                db.add(config)
            
            db.commit()
            db.refresh(config)
            
            # 重启调度器
            AutoUpdateService._restart_scheduler()
            
            return {
                "success": True,
                "data": {
                    "id": config.id,
                    "enabled": config.enabled,
                    "update_time": config.update_time,
                    "update_days": config.update_days,
                    "last_update": config.last_update.isoformat() if config.last_update else None
                }
            }
            
        except Exception as e:
            db.rollback()
            logger.error(f"创建或更新自动更新配置失败: {str(e)}")
            return {"success": False, "error": f"操作失败: {str(e)}"}
    
    @staticmethod
    async def execute_auto_update():
        """执行自动更新"""
        db = SessionLocal()
        try:
            logger.info("开始执行自动更新任务")
            
            # 获取配置
            config = AutoUpdateService.get_config(db)
            if not config or not config.enabled:
                logger.info("自动更新未启用，跳过")
                return
            
            # 获取所有已登录账号
            accounts = DataUpdateService.get_logged_accounts(db)
            if not accounts:
                logger.info("没有已登录的账号，跳过自动更新")
                return
            
            # 计算日期范围
            end_date = date.today()
            start_date = end_date - timedelta(days=config.update_days)
            
            # 互斥：如果已有运行中的任务，则跳过本次自动更新
            running_task = db.query(DataUpdateRecord).filter(DataUpdateRecord.status == 'running').first()
            if running_task:
                logger.warning(f"检测到已有运行中的更新任务（ID={running_task.id}），本次自动更新跳过")
                return

            # 创建更新记录
            record = DataUpdateService.create_update_record(
                db, start_date, end_date, len(accounts)
            )

            logger.info(f"创建自动更新记录: {record.id}, 账号数: {len(accounts)}, 日期范围: {start_date} ~ {end_date}")

            # 执行更新任务（跳过清空数据）
            account_ids = [account.id for account in accounts]
            await DataUpdateService._execute_update_task(
                record.id,
                start_date.strftime('%Y-%m-%d'),
                end_date.strftime('%Y-%m-%d'),
                account_ids,
                skip_clear_data=True  # 自动更新不清空数据
            )
            
            # 更新最后更新时间
            config.last_update = datetime.utcnow()
            db.commit()
            
            logger.info("自动更新任务完成")
            
        except Exception as e:
            logger.error(f"自动更新任务失败: {str(e)}")
        finally:
            db.close()
    
    @staticmethod
    def _job_wrapper():
        """任务包装器，用于在调度器中运行异步任务"""
        try:
            asyncio.run(AutoUpdateService.execute_auto_update())
        except Exception as e:
            logger.error(f"自动更新任务包装器失败: {str(e)}")
    
    @staticmethod
    def _scheduler_worker():
        """调度器工作线程"""
        logger.info("自动更新调度器启动")
        while AutoUpdateService._running:
            try:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
            except Exception as e:
                logger.error(f"调度器运行错误: {str(e)}")
                time.sleep(60)
        logger.info("自动更新调度器停止")
    
    @staticmethod
    def start_scheduler():
        """启动调度器"""
        if AutoUpdateService._running:
            logger.info("调度器已在运行")
            return
        
        db = SessionLocal()
        try:
            config = AutoUpdateService.get_config(db)
            if config and config.enabled:
                # 清除现有任务
                schedule.clear()
                
                # 添加定时任务
                schedule.every().day.at(config.update_time).do(AutoUpdateService._job_wrapper)
                
                # 启动调度器线程
                AutoUpdateService._running = True
                AutoUpdateService._scheduler_thread = Thread(
                    target=AutoUpdateService._scheduler_worker,
                    daemon=True
                )
                AutoUpdateService._scheduler_thread.start()
                
                logger.info(f"自动更新调度器已启动，每天 {config.update_time} 执行")
            else:
                logger.info("自动更新未启用，不启动调度器")
        finally:
            db.close()
    
    @staticmethod
    def stop_scheduler():
        """停止调度器"""
        if AutoUpdateService._running:
            AutoUpdateService._running = False
            schedule.clear()
            if AutoUpdateService._scheduler_thread:
                AutoUpdateService._scheduler_thread.join(timeout=10)  # 增加超时时间
                if AutoUpdateService._scheduler_thread.is_alive():
                    logger.warning("调度器线程未能在超时时间内停止")
                else:
                    logger.info("调度器线程已正常停止")
                AutoUpdateService._scheduler_thread = None
            logger.info("自动更新调度器已停止")
    
    @staticmethod
    def _restart_scheduler():
        """重启调度器"""
        AutoUpdateService.stop_scheduler()
        time.sleep(1)  # 等待停止完成
        AutoUpdateService.start_scheduler()
    
    @staticmethod
    def get_status() -> Dict[str, Any]:
        """获取自动更新状态"""
        db = SessionLocal()
        try:
            config = AutoUpdateService.get_config(db)
            
            if not config:
                return {
                    "enabled": False,
                    "update_time": "02:00",
                    "update_days": 30,
                    "last_update": None,
                    "scheduler_running": False,
                    "next_run": None
                }
            
            # 获取下次运行时间
            next_run = None
            if config.enabled and schedule.jobs:
                next_run = schedule.jobs[0].next_run.isoformat() if schedule.jobs[0].next_run else None
            
            return {
                "enabled": config.enabled,
                "update_time": config.update_time,
                "update_days": config.update_days,
                "last_update": config.last_update.isoformat() if config.last_update else None,
                "scheduler_running": AutoUpdateService._running,
                "next_run": next_run
            }
            
        finally:
            db.close()


# 全局实例
auto_update_service = AutoUpdateService()
