import asyncio
import logging
import os
from typing import Optional
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, BrowserContext, Page

logger = logging.getLogger(__name__)


class BrowserManager:
    """全局浏览器管理器：
    - 维护单个 Playwright 和 Browser 实例
    - 通过创建/关闭 BrowserContext 实现账号级隔离
    - 控制并发、提供容灾重启
    """

    _instance: Optional["BrowserManager"] = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        self.playwright = None
        self.browser: Optional[Browser] = None
        self._lock = asyncio.Lock()
        # 全局同时活动的上下文限制（可根据机器性能调整）
        self._semaphore = asyncio.Semaphore(int(os.getenv("MAX_BROWSER_CONTEXTS", "4")))

    async def start(self):
        async with self._lock:
            if self.playwright and self.browser:
                return
            logger.info("启动全局Playwright与浏览器 ...")
            self.playwright = await async_playwright().start()
            args = [
                "--no-sandbox",
                "--disable-dev-shm-usage",
                "--disable-gpu",
                "--disable-extensions",
                "--disable-background-networking",
                "--disable-renderer-backgrounding",
                "--mute-audio",
            ]
            self.browser = await self.playwright.chromium.launch(
                headless=True,
                args=args,
                timeout=30000,
                ignore_default_args=['--enable-automation', '--enable-blink-features=IdleDetection']
            )
            logger.info("全局浏览器已启动")

    async def stop(self):
        async with self._lock:
            logger.info("停止全局浏览器与Playwright ...")
            try:
                if self.browser:
                    # 强制关闭所有上下文
                    contexts = self.browser.contexts
                    for context in contexts:
                        try:
                            await context.close()
                        except Exception as e:
                            logger.warning(f"强制关闭上下文出错: {e}")

                    await self.browser.close()
                    self.browser = None
                    logger.info("浏览器已关闭")
            except Exception as e:
                logger.warning(f"关闭浏览器出错: {e}")

            try:
                if self.playwright:
                    await self.playwright.stop()
                    self.playwright = None
                    logger.info("Playwright已停止")
            except Exception as e:
                logger.warning(f"停止Playwright出错: {e}")

            # 等待一下确保资源完全释放
            await asyncio.sleep(0.5)
            logger.info("全局浏览器已停止")

    async def _ensure_running(self):
        if not self.playwright or not self.browser:
            await self.start()

    async def create_context(self, *, user_agent: Optional[str] = None, viewport: Optional[dict] = None,
                             storage_state: Optional[dict] = None, accept_downloads: bool = True) -> BrowserContext:
        await self._ensure_running()
        await self._semaphore.acquire()
        try:
            context = await self.browser.new_context(
                storage_state=storage_state,
                accept_downloads=accept_downloads,
                user_agent=user_agent or 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120 Safari/537.36',
                viewport=viewport or {"width": 1920, "height": 1080},
            )
            return context
        except Exception:
            # 创建失败时释放信号量
            self._semaphore.release()
            raise

    async def close_context(self, context: Optional[BrowserContext]):
        try:
            if context:
                await context.close()
        except Exception as e:
            logger.warning(f"关闭上下文出错: {e}")
        finally:
            # 无论如何释放信号量
            self._semaphore.release()

    async def open_page(self, context: BrowserContext) -> Page:
        page = await context.new_page()
        # 可注入通用监听器
        page.on("close", lambda: logger.info("页面已关闭"))
        page.on("crash", lambda: logger.warning("页面崩溃"))
        return page


# 全局单例
browser_manager = BrowserManager()

