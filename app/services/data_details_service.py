import pandas as pd
from typing import List, Dict, Any, Optional, Tuple, Union
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc, func
from datetime import datetime, date, timedelta
import io
from app.models import (
    WeChatMPContentTrend, WeChatMPContentSource,
    WeChatMPContentDetail, WeChatMPUserChannel,
    WeChatMPUserSource, PlatformAccount, WeChatChannelsVideoData,
    XiaohongshuNoteData, XiaohongshuAccountOverview, XiaohongshuFansData,
    WeChatChannelsFollowerData
)
from app.services.wechat_service import WeChatMPService


class DataDetailsService:
    """数据明细服务类"""
    
    # 数据类型到模型的映射
    MODEL_MAPPING = {
        'content_trend': WeChatMPContentTrend,
        'content_source': WeChatMPContentSource,
        'content_detail': WeChatMPContentDetail,
        'user_channel': WeChatMPUserChannel,
        'user_source': WeChatMPUserSource,
        'single_video': WeChatChannelsVideoData,
        'follower_data': WeChatChannelsFollowerData,
        'note_data': XiaohongshuNoteData,
        'account_overview': XiaohongshuAccountOverview,
        'fans_data': XiaohongshuFansData
    }
    
    @staticmethod
    def import_excel_data(
        db: Session, 
        account_id: int, 
        data_type: str, 
        excel_content: bytes
    ) -> Dict[str, Any]:
        """从Excel内容导入数据到数据库
        
        Args:
            db: 数据库会话
            account_id: 账号ID
            data_type: 数据类型
            excel_content: Excel文件内容
            
        Returns:
            导入结果统计
        """
        try:
            # 获取对应的模型类
            model_class = DataDetailsService.MODEL_MAPPING.get(data_type)
            if not model_class:
                return {"success": False, "error": f"不支持的数据类型: {data_type}"}
            
            # 获取DOWNLOAD_TEMPLATES配置
            if data_type == 'single_video':
                # 视频号数据使用WeChatChannelsService的配置
                from app.services.wechat_channels_service import WeChatChannelsService
                channels_service = WeChatChannelsService()
                config = channels_service._get_download_config(data_type=data_type)
            elif data_type == 'note_data':
                # 小红书数据使用XiaohongshuService的配置
                from app.services.xiaohongshu_service import XiaohongshuService
                xiaohongshu_service = XiaohongshuService()
                config = xiaohongshu_service._get_download_config(data_type=data_type)
            else:
                # 微信公众号数据使用WeChatMPService的配置
                wechat_service = WeChatMPService()
                config = wechat_service._get_download_config(data_type=data_type)

            if not config:
                return {"success": False, "error": f"找不到数据类型配置: {data_type}"}
            
            # 检查文件内容是否为HTML格式（特别是用户增长表）
            content_str = excel_content.decode('utf-8', errors='ignore')
            if content_str.strip().startswith('<html') and data_type == 'user_channel':
                # 解析HTML表格
                df = DataDetailsService._parse_html_table(content_str)
                # HTML表格通常已经是干净的数据，不需要跳过太多行
                data_start_row = 0  # HTML表格解析后直接使用
                fields = config.get('fields', [])
                data_df = df.copy()
            else:
                # 检测文件类型并选择合适的解析方法
                try:
                    # 尝试将内容解码为字符串，检查是否为CSV
                    content_str = excel_content.decode('utf-8-sig')  # 使用utf-8-sig处理BOM
                    if ',' in content_str and '\n' in content_str:
                        # 看起来像CSV文件，尝试用CSV解析
                        print("检测到CSV格式，使用CSV解析器")
                        df = pd.read_csv(io.StringIO(content_str))
                    else:
                        raise ValueError("不是CSV格式")
                except (UnicodeDecodeError, ValueError):
                    # 如果不是CSV或解码失败，尝试Excel解析
                    print("使用Excel解析器")
                    # 尝试多种Excel解析方法，参考excel_parser.py的逻辑
                    parse_success = False
                    last_error = None

                    # 方法1: 检查是否是HTML格式的Excel
                    try:
                        content_str = excel_content.decode('utf-8', errors='ignore')
                        if '<html' in content_str.lower() or '<table' in content_str.lower():
                            print("检测到HTML格式的Excel文件，使用read_html")
                            tables = pd.read_html(io.BytesIO(excel_content), encoding='utf-8')
                            if tables:
                                df = tables[0]  # 取第一个表格
                                parse_success = True
                                print("HTML表格解析成功")
                    except Exception as html_e:
                        print(f"HTML解析失败: {html_e}")
                        last_error = html_e

                    # 方法2: 标准Excel解析（openpyxl）
                    if not parse_success:
                        try:
                            print("尝试openpyxl引擎解析Excel")
                            df = pd.read_excel(io.BytesIO(excel_content), header=None, engine='openpyxl')
                            parse_success = True
                            print("openpyxl解析成功")
                        except Exception as openpyxl_e:
                            print(f"openpyxl解析失败: {openpyxl_e}")
                            last_error = openpyxl_e

                    # 方法3: 尝试xlrd引擎
                    if not parse_success:
                        try:
                            print("尝试xlrd引擎解析Excel")
                            df = pd.read_excel(io.BytesIO(excel_content), header=None, engine='xlrd')
                            parse_success = True
                            print("xlrd解析成功")
                        except Exception as xlrd_e:
                            print(f"xlrd解析失败: {xlrd_e}")
                            last_error = xlrd_e

                    # 如果所有方法都失败，输出调试信息并抛出异常
                    if not parse_success:
                        print("所有Excel解析方法都失败，输出调试信息:")

                        # 检查文件内容
                        if len(excel_content) >= 4:
                            file_header = excel_content[:4]
                            print(f"文件头 (hex): {file_header.hex()}")
                            print(f"文件头 (bytes): {file_header}")

                            if file_header[:2] != b'PK':
                                print("❌ 文件头不是ZIP格式，不是有效的Excel文件")
                            else:
                                print("✅ 文件头是ZIP格式，应该是有效的Excel文件")

                        # 尝试查看文件内容
                        preview_length = min(200, len(excel_content))
                        try:
                            text_preview = excel_content[:preview_length].decode('utf-8', errors='ignore')
                            print(f"文件内容预览: {repr(text_preview)}")

                            # 检查是否是HTML错误页面
                            if '<html>' in text_preview.lower() or '<!doctype' in text_preview.lower():
                                print("❌ 检测到HTML内容，服务器可能返回了错误页面而不是Excel文件")
                            elif 'error' in text_preview.lower():
                                print("❌ 检测到错误信息")
                        except:
                            print("无法解码文件内容预览")

                        raise Exception(f"所有Excel解析方法都失败，最后一个错误: {last_error}")

                # 获取数据起始行和字段配置
                data_start_row = config.get('data_start_row', 2) - 1  # 转换为0基索引
                fields = config.get('fields', [])

                # 对于CSV文件，通常第一行就是标题，不需要跳过太多行
                if data_start_row > 0 and len(df) > data_start_row:
                    data_df = df.iloc[data_start_row:].copy()
                else:
                    # 如果数据起始行设置不合理，直接使用全部数据
                    data_df = df.copy()
            
            if data_df.empty:
                return {"success": False, "error": "Excel文件中没有数据"}
            
            # 重置列名为字段名
            if len(data_df.columns) >= len(fields):
                field_names = [field[0] for field in fields]
                data_df.columns = field_names + list(data_df.columns[len(fields):])
            
            # 根据数据类型处理数据
            imported_count = 0
            updated_count = 0
            
            if data_type == 'content_trend':
                imported_count, updated_count = DataDetailsService._import_content_trend(
                    db, account_id, data_df
                )
            elif data_type == 'content_source':
                imported_count, updated_count = DataDetailsService._import_content_source(
                    db, account_id, data_df
                )
            elif data_type == 'content_detail':
                imported_count, updated_count = DataDetailsService._import_content_detail(
                    db, account_id, data_df
                )
            elif data_type == 'user_channel':
                imported_count, updated_count = DataDetailsService._import_user_channel(
                    db, account_id, data_df
                )
            elif data_type == 'single_video':
                imported_count, updated_count = DataDetailsService._import_single_video(
                    db, account_id, data_df
                )
            elif data_type == 'note_data':
                imported_count, updated_count = DataDetailsService._import_note_data(
                    db, account_id, data_df
                )
            
            db.commit()
            
            return {
                "success": True,
                "imported_count": imported_count,
                "updated_count": updated_count,
                "total_processed": imported_count + updated_count
            }
            
        except Exception as e:
            db.rollback()
            return {"success": False, "error": f"导入失败: {str(e)}"}
    
    @staticmethod
    def _parse_date(date_value) -> Optional[date]:
        """解析日期值"""
        if pd.isna(date_value):
            return None

        # 处理整数类型（如 ******** 或 **********）
        if isinstance(date_value, (int, float)):
            date_str = str(int(date_value))
            # 如果是10位数字格式（如**********），只取前8位作为日期
            if len(date_str) >= 8:
                date_str = date_str[:8]
        elif isinstance(date_value, str):
            date_str = date_value.strip()
            # 如果是字符串且长度>=8，只取前8位作为日期
            if len(date_str) >= 8 and date_str.isdigit():
                date_str = date_str[:8]
        elif isinstance(date_value, datetime):
            return date_value.date()
        elif isinstance(date_value, date):
            return date_value
        else:
            return None

        # 尝试解析不同格式的日期字符串
        for fmt in ['%Y-%m-%d', '%Y/%m/%d', '%Y%m%d']:
            try:
                return datetime.strptime(date_str, fmt).date()
            except ValueError:
                continue

        return None

    @staticmethod
    def _parse_datetime(datetime_value) -> Optional[datetime]:
        """解析日期时间值"""
        if pd.isna(datetime_value):
            return None

        # 处理整数类型（如 20250718）
        if isinstance(datetime_value, (int, float)):
            datetime_str = str(int(datetime_value))
        elif isinstance(datetime_value, str):
            datetime_str = datetime_value.strip()
        elif isinstance(datetime_value, datetime):
            return datetime_value
        elif isinstance(datetime_value, date):
            # 将date转换为datetime，时间设为00:00:00
            return datetime.combine(datetime_value, datetime.min.time())
        else:
            return None

        # 尝试解析不同格式的日期时间字符串
        formats = [
            '%Y-%m-%d %H:%M:%S',  # 2025-07-18 10:30:00
            '%Y/%m/%d %H:%M:%S',  # 2025/07/18 10:30:00
            '%Y%m%d %H:%M:%S',    # 20250718 10:30:00
            '%Y-%m-%d',           # 2025-07-18 (只有日期，时间设为00:00:00)
            '%Y/%m/%d',           # 2025/07/18
            '%Y%m%d'              # 20250718 (8位数字格式)
        ]

        for fmt in formats:
            try:
                return datetime.strptime(datetime_str, fmt)
            except ValueError:
                continue

        return None

    @staticmethod
    def _parse_html_table(html_content: str) -> pd.DataFrame:
        """解析HTML表格内容为DataFrame，专门处理用户增长表"""
        try:
            from io import StringIO
            import re

            # 直接从HTML中提取表格数据
            # 查找所有的表格行
            tr_pattern = r'<tr[^>]*>(.*?)</tr>'
            tr_matches = re.findall(tr_pattern, html_content, re.DOTALL)

            data_rows = []

            for tr_content in tr_matches:
                # 提取th和td中的数据
                cell_pattern = r'<(?:th|td)[^>]*>(.*?)</(?:th|td)>'
                cells = re.findall(cell_pattern, tr_content, re.DOTALL)

                if len(cells) >= 5:  # 用户增长表有5列
                    # 清理HTML标签和空白字符
                    clean_cells = []
                    for cell in cells:
                        clean_cell = re.sub(r'<[^>]+>', '', cell).strip()
                        clean_cells.append(clean_cell)

                    # 检查是否是数据行（第一个单元格是日期格式）
                    if len(clean_cells) >= 5:
                        first_cell = clean_cells[0]
                        if len(first_cell) == 10 and first_cell.count('-') == 2:
                            # 这是数据行，转换数值
                            row_data = [first_cell]  # 时间
                            for i in range(1, 5):  # 4个数值字段
                                try:
                                    value = int(clean_cells[i])
                                    row_data.append(value)
                                except (ValueError, TypeError):
                                    row_data.append(0)

                            if len(row_data) == 5:
                                data_rows.append(row_data)

            # 创建DataFrame
            if data_rows:
                df = pd.DataFrame(data_rows, columns=['时间', '新关注人数', '取消关注人数', '净增关注人数', '累积关注人数'])
                print(f"HTML表格处理成功，转换为 {df.shape} 的DataFrame")
                return df
            else:
                print("未找到有效的数据行")
                return pd.DataFrame()

        except Exception as e:
            print(f"HTML表格解析失败: {e}")
            import traceback
            traceback.print_exc()
            return pd.DataFrame()
    
    @staticmethod
    def _parse_int(value) -> int:
        """解析整数值"""
        if pd.isna(value):
            return 0
        try:
            return int(float(value))
        except (ValueError, TypeError):
            return 0
    
    @staticmethod
    def _parse_str(value) -> str:
        """解析字符串值"""
        if pd.isna(value):
            return ""
        return str(value).strip()
    
    @staticmethod
    def _import_content_trend(db: Session, account_id: int, df: pd.DataFrame) -> Tuple[int, int]:
        """导入内容数据趋势明细"""
        imported_count = 0
        updated_count = 0
        
        for _, row in df.iterrows():
            date_value = DataDetailsService._parse_date(row.get('日期'))
            if not date_value:
                continue
            
            # 检查是否已存在
            existing = db.query(WeChatMPContentTrend).filter(
                and_(
                    WeChatMPContentTrend.account_id == account_id,
                    WeChatMPContentTrend.date == date_value
                )
            ).first()
            
            data = {
                'account_id': account_id,
                'date': date_value,
                'read_count': DataDetailsService._parse_int(row.get('阅读次数')),
                'read_user_count': DataDetailsService._parse_int(row.get('阅读人数')),
                'share_count': DataDetailsService._parse_int(row.get('分享次数')),
                'share_user_count': DataDetailsService._parse_int(row.get('分享人数')),
                'read_original_count': DataDetailsService._parse_int(row.get('阅读原文次数')),
                'read_original_user_count': DataDetailsService._parse_int(row.get('阅读原文人数')),
                'collect_count': DataDetailsService._parse_int(row.get('收藏次数')),
                'collect_user_count': DataDetailsService._parse_int(row.get('收藏人数')),
                'publish_count': DataDetailsService._parse_int(row.get('群发篇数')),
                'channel': DataDetailsService._parse_str(row.get('渠道'))
            }
            
            if existing:
                # 更新现有记录
                for key, value in data.items():
                    if key != 'account_id':  # 不更新account_id
                        setattr(existing, key, value)
                existing.updated_at = datetime.utcnow()
                updated_count += 1
            else:
                # 创建新记录
                record = WeChatMPContentTrend(**data)
                db.add(record)
                imported_count += 1
        
        return imported_count, updated_count
    
    @staticmethod
    def _import_content_source(db: Session, account_id: int, df: pd.DataFrame) -> Tuple[int, int]:
        """导入内容流量来源明细"""
        imported_count = 0
        updated_count = 0
        
        for _, row in df.iterrows():
            channel = DataDetailsService._parse_str(row.get('传播渠道'))
            title = DataDetailsService._parse_str(row.get('内容标题'))
            publish_date = DataDetailsService._parse_date(row.get('发表日期'))
            
            if not channel or not title:
                continue
            
            # 检查是否已存在（基于渠道、标题和发表日期）
            existing = db.query(WeChatMPContentSource).filter(
                and_(
                    WeChatMPContentSource.account_id == account_id,
                    WeChatMPContentSource.channel == channel,
                    WeChatMPContentSource.title == title,
                    WeChatMPContentSource.publish_date == publish_date
                )
            ).first()
            
            data = {
                'account_id': account_id,
                'channel': channel,
                'publish_date': publish_date,
                'title': title,
                'read_count': DataDetailsService._parse_int(row.get('阅读次数')),
                'read_ratio': DataDetailsService._parse_str(row.get('阅读次数占比'))
            }
            
            if existing:
                # 更新现有记录
                for key, value in data.items():
                    if key != 'account_id':
                        setattr(existing, key, value)
                existing.updated_at = datetime.utcnow()
                updated_count += 1
            else:
                # 创建新记录
                record = WeChatMPContentSource(**data)
                db.add(record)
                imported_count += 1
        
        return imported_count, updated_count

    @staticmethod
    def _import_content_detail(db: Session, account_id: int, df: pd.DataFrame) -> Tuple[int, int]:
        """导入内容已通知内容明细"""
        imported_count = 0
        updated_count = 0

        for _, row in df.iterrows():
            title = DataDetailsService._parse_str(row.get('内容标题'))
            publish_time_value = row.get('发表时间')

            if not title:
                continue

            # 解析发表时间，支持多种格式包括8位数字格式
            publish_time = DataDetailsService._parse_datetime(publish_time_value)

            # 检查是否已存在（基于标题和发表时间）
            existing = db.query(WeChatMPContentDetail).filter(
                and_(
                    WeChatMPContentDetail.account_id == account_id,
                    WeChatMPContentDetail.title == title,
                    WeChatMPContentDetail.publish_time == publish_time
                )
            ).first()

            data = {
                'account_id': account_id,
                'title': title,
                'publish_time': publish_time,
                'total_read_user_count': DataDetailsService._parse_int(row.get('总阅读人数')),
                'total_read_count': DataDetailsService._parse_int(row.get('总阅读次数')),
                'total_share_user_count': DataDetailsService._parse_int(row.get('总分享人数')),
                'total_share_count': DataDetailsService._parse_int(row.get('总分享次数')),
                'follow_after_read_count': DataDetailsService._parse_int(row.get('阅读后关注人数')),
                'delivery_count': DataDetailsService._parse_int(row.get('送达人数')),
                'mp_message_read_count': DataDetailsService._parse_int(row.get('公众号消息阅读次数')),
                'delivery_read_rate': DataDetailsService._parse_str(row.get('送达阅读率')),
                'first_share_count': DataDetailsService._parse_int(row.get('首次分享次数')),
                'share_generated_read_count': DataDetailsService._parse_int(row.get('分享产生阅读次数')),
                'first_share_rate': DataDetailsService._parse_str(row.get('首次分享率')),
                'avg_read_per_share': DataDetailsService._parse_int(row.get('每次分享带来阅读次数')),
                'read_completion_rate': DataDetailsService._parse_str(row.get('阅读完成率')),
                'content_url': DataDetailsService._parse_str(row.get('内容url'))
            }

            if existing:
                # 更新现有记录
                for key, value in data.items():
                    if key != 'account_id':
                        setattr(existing, key, value)
                existing.updated_at = datetime.utcnow()
                updated_count += 1
            else:
                # 创建新记录
                record = WeChatMPContentDetail(**data)
                db.add(record)
                imported_count += 1

        return imported_count, updated_count

    @staticmethod
    def _import_user_channel(db: Session, account_id: int, df: pd.DataFrame) -> Tuple[int, int]:
        """导入用户增长明细"""
        imported_count = 0
        updated_count = 0

        for _, row in df.iterrows():
            date_value = DataDetailsService._parse_date(row.get('时间'))
            if not date_value:
                continue

            # 检查是否已存在
            existing = db.query(WeChatMPUserChannel).filter(
                and_(
                    WeChatMPUserChannel.account_id == account_id,
                    WeChatMPUserChannel.date == date_value
                )
            ).first()

            data = {
                'account_id': account_id,
                'date': date_value,
                'new_follow_count': DataDetailsService._parse_int(row.get('新关注人数')),
                'unfollow_count': DataDetailsService._parse_int(row.get('取消关注人数')),
                'net_follow_count': DataDetailsService._parse_int(row.get('净增关注人数')),
                'total_follow_count': DataDetailsService._parse_int(row.get('累积关注人数'))
            }

            if existing:
                # 更新现有记录
                for key, value in data.items():
                    if key != 'account_id':
                        setattr(existing, key, value)
                existing.updated_at = datetime.utcnow()
                updated_count += 1
            else:
                # 创建新记录
                record = WeChatMPUserChannel(**data)
                db.add(record)
                imported_count += 1

        return imported_count, updated_count

    @staticmethod
    def get_data_list(
        db: Session,
        account_id: Union[int, List[int], None],
        data_type: str,
        page: int = 1,
        page_size: int = 20,
        search: Optional[str] = None,
        sort_field: Optional[str] = None,
        sort_order: str = 'desc'
    ) -> Dict[str, Any]:
        """获取数据明细列表

        Args:
            account_id: 账号ID，可以是单个ID、ID列表或None（获取所有账号数据）
        """
        try:
            model_class = DataDetailsService.MODEL_MAPPING.get(data_type)
            if not model_class:
                return {"success": False, "error": f"不支持的数据类型: {data_type}"}

            # 构建查询条件，联接账号表获取账号名称
            base_query = db.query(model_class, PlatformAccount.name.label('account_name')).join(
                PlatformAccount, model_class.account_id == PlatformAccount.id
            )

            if account_id is None:
                # 不过滤账号，获取所有数据
                query = base_query
            elif isinstance(account_id, list):
                # 多个账号ID
                query = base_query.filter(model_class.account_id.in_(account_id))
            else:
                # 单个账号ID
                query = base_query.filter(model_class.account_id == account_id)

            # 添加搜索条件
            if search:
                if data_type == 'content_detail':
                    query = query.filter(model_class.title.contains(search))
                elif data_type == 'content_source':
                    query = query.filter(
                        or_(
                            model_class.title.contains(search),
                            model_class.channel.contains(search)
                        )
                    )

            # 添加排序（白名单校验 + 支持按账号名称排序）
            allowed_fields = {col.name for col in model_class.__table__.columns}
            allowed_fields.add('account_name')  # 加入联接字段

            # 计算安全的排序字段与默认值
            default_sort_field = 'created_at' if 'created_at' in allowed_fields else ('id' if 'id' in allowed_fields else None)
            safe_sort_field = sort_field if (sort_field in allowed_fields) else default_sort_field

            if safe_sort_field:
                if safe_sort_field == 'account_name':
                    sort_column = PlatformAccount.name
                else:
                    sort_column = getattr(model_class, safe_sort_field)
                if sort_order == 'asc':
                    query = query.order_by(asc(sort_column))
                else:
                    query = query.order_by(desc(sort_column))
            else:
                # 无可用排序字段，保持原顺序
                pass

            total = query.count()
            offset = (page - 1) * page_size
            items = query.offset(offset).limit(page_size).all()

            # 转换为字典格式
            data_list = []
            for row in items:
                # row是一个包含模型实例和account_name的元组
                model_instance = row[0]  # 模型实例
                account_name = row[1]    # 账号名称

                item_dict = {'account_name': account_name}  # 先添加账号名称

                # 添加模型的所有字段
                for column in model_class.__table__.columns:
                    value = getattr(model_instance, column.name)
                    if isinstance(value, (date, datetime)):
                        value = value.isoformat()
                    item_dict[column.name] = value

                data_list.append(item_dict)

            return {
                "success": True,
                "data": data_list,
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": (total + page_size - 1) // page_size
            }

        except Exception as e:
            return {"success": False, "error": f"查询失败: {str(e)}"}

    @staticmethod
    def get_data_summary(db: Session, account_id: int, data_type: str) -> Dict[str, Any]:
        """获取数据汇总信息"""
        try:
            model_class = DataDetailsService.MODEL_MAPPING.get(data_type)
            if not model_class:
                return {"success": False, "error": f"不支持的数据类型: {data_type}"}

            total_records = db.query(model_class).filter(
                model_class.account_id == account_id
            ).count()

            latest_record = db.query(model_class).filter(
                model_class.account_id == account_id
            ).order_by(desc(model_class.created_at)).first()

            latest_time = None
            if latest_record:
                latest_time = latest_record.created_at.isoformat()

            return {
                "success": True,
                "total_records": total_records,
                "latest_time": latest_time
            }

        except Exception as e:
            return {"success": False, "error": f"获取汇总失败: {str(e)}"}

    @staticmethod
    def calculate_recent_fridays(count: int = 4) -> List[date]:
        """计算最近的几个周五日期，最新的排在前面

        Args:
            count: 需要的周五数量

        Returns:
            周五日期列表，按时间倒序排列（最新的在前）
        """
        today = date.today()
        # 计算距离上个周五的天数（周五是4，周一是0）
        days_since_friday = (today.weekday() + 3) % 7
        if days_since_friday == 0 and today.weekday() == 4:
            # 如果今天是周五，从今天开始
            last_friday = today
        else:
            # 否则找上个周五
            last_friday = today - timedelta(days=days_since_friday)

        fridays = []
        for i in range(count):
            fridays.append(last_friday - timedelta(weeks=i))

        # 返回按时间倒序排列的日期（最新的在前）
        return fridays

    @staticmethod
    def calculate_recent_fridays_from_date(end_date: date, count: int = 4) -> List[date]:
        """基于指定日期计算最近的几个周五日期，最新的排在前面

        Args:
            end_date: 结束日期
            count: 需要的周五数量

        Returns:
            周五日期列表，按时间倒序排列（最新的在前）
        """
        # 计算距离指定日期最近的周五
        days_since_friday = (end_date.weekday() + 3) % 7
        if days_since_friday == 0 and end_date.weekday() == 4:
            # 如果指定日期就是周五，从这天开始
            last_friday = end_date
        else:
            # 否则找上个周五
            last_friday = end_date - timedelta(days=days_since_friday)

        fridays = []
        for i in range(count):
            fridays.append(last_friday - timedelta(weeks=i))

        # 返回按时间倒序排列的日期（最新的在前）
        return fridays

    @staticmethod
    def get_account_summary(db: Session) -> Dict[str, Any]:
        """获取社媒账号数据汇总（最近4个周五的关注数）
        包含微信公众号、视频号、小红书的数据

        Returns:
            包含账号关注数汇总的数据
        """
        try:
            # 获取同步历史记录中的日期范围
            from app.models import DataUpdateRecord, WeChatChannelsFollowerData, XiaohongshuFansData, DataUpdateTaskItem
            latest_sync = db.query(DataUpdateRecord).filter(
                DataUpdateRecord.status.in_(['completed', 'failed'])
            ).order_by(DataUpdateRecord.created_at.desc()).first()

            data_date_range = None
            recent_fridays = []

            if latest_sync:
                start_date = latest_sync.start_date.strftime('%m-%d')
                end_date = latest_sync.end_date.strftime('%m-%d')
                data_date_range = f"{start_date}～{end_date}"

                # 基于end_date计算最近4个周五
                recent_fridays = DataDetailsService.calculate_recent_fridays_from_date(latest_sync.end_date, 4)
            else:
                # 如果没有历史记录，使用当前日期计算
                recent_fridays = DataDetailsService.calculate_recent_fridays(4)

            # 查询所有平台账号
            all_accounts = db.query(PlatformAccount).filter(
                PlatformAccount.platform.in_(["wechat_mp", "wechat_service", "wechat_channels", "xiaohongshu"])
            ).all()

            result = []
            for account in all_accounts:
                row_data = {
                    "account_name": account.name,
                    "platform": account.platform
                }

                # 获取该账号所有已完成任务中最老的完成日期
                latest_update_date = None
                oldest_completed_item = db.query(DataUpdateTaskItem).filter(
                    DataUpdateTaskItem.account_id == account.id,
                    DataUpdateTaskItem.completed_at.isnot(None)
                ).order_by(DataUpdateTaskItem.completed_at.desc()).first()

                if oldest_completed_item:
                    latest_update_date = oldest_completed_item.completed_at.strftime('%Y-%m-%d %H:%M')

                row_data["latest_update_date"] = latest_update_date

                # 根据平台类型查询不同的数据表
                for friday in recent_fridays:
                    date_key = friday.strftime('%Y-%m-%d')
                    follow_count = 0

                    if account.platform in ["wechat_mp", "wechat_service"]:
                        # 微信公众号数据
                        follow_data = db.query(WeChatMPUserChannel).filter(
                            WeChatMPUserChannel.account_id == account.id,
                            WeChatMPUserChannel.date == friday
                        ).first()
                        follow_count = follow_data.total_follow_count if follow_data else 0

                    elif account.platform == "wechat_channels":
                        # 微信视频号数据
                        follow_data = db.query(WeChatChannelsFollowerData).filter(
                            WeChatChannelsFollowerData.account_id == account.id,
                            WeChatChannelsFollowerData.date == friday
                        ).first()
                        follow_count = follow_data.total_followers if follow_data else 0

                    elif account.platform == "xiaohongshu":
                        # 小红书数据
                        follow_data = db.query(XiaohongshuFansData).filter(
                            XiaohongshuFansData.account_id == account.id,
                            XiaohongshuFansData.date == friday
                        ).first()
                        follow_count = follow_data.total_fans_count if follow_data else 0

                    row_data[date_key] = follow_count

                result.append(row_data)

            # 格式化日期列（最新的在前）
            date_columns = [friday.strftime('%Y-%m-%d') for friday in recent_fridays]

            return {
                "success": True,
                "data": result,
                "date_columns": date_columns,
                "data_date_range": data_date_range
            }

        except Exception as e:
            return {"success": False, "error": f"获取账号汇总失败: {str(e)}"}

    @staticmethod
    def get_growth_summary(db: Session) -> Dict[str, Any]:
        """获取关注数合计净增长汇总
        包含微信公众号、视频号、小红书的数据

        Returns:
            包含各账号增长数据和用户来源分布的汇总
        """
        try:
            from app.models import DataUpdateRecord, WeChatChannelsFollowerData, XiaohongshuFansData, DataUpdateTaskItem

            # 获取最近一次数据更新记录
            latest_sync = db.query(DataUpdateRecord).filter(
                DataUpdateRecord.status.in_(['completed', 'failed'])
            ).order_by(DataUpdateRecord.created_at.desc()).first()

            # 查询所有平台账号
            all_accounts = db.query(PlatformAccount).filter(
                PlatformAccount.platform.in_(["wechat_mp", "wechat_service", "wechat_channels", "xiaohongshu"])
            ).all()

            # 获取所有存在的user_source类型（仅微信公众号有用户来源数据）
            user_sources = db.query(WeChatMPUserSource.user_source).distinct().order_by(
                WeChatMPUserSource.user_source
            ).all()
            user_source_list = [source[0] for source in user_sources]

            result = []
            for account in all_accounts:
                row_data = {
                    "account_name": account.name,
                    "platform": account.platform,
                    "new_user": 0,
                    "cancel_user": 0,
                    "cumulate_user": 0
                }

                # 获取该账号所有已完成任务中最老的完成日期
                latest_update_date = None
                oldest_completed_item = db.query(DataUpdateTaskItem).filter(
                    DataUpdateTaskItem.account_id == account.id,
                    DataUpdateTaskItem.completed_at.isnot(None)
                ).order_by(DataUpdateTaskItem.completed_at.desc()).first()

                if oldest_completed_item:
                    latest_update_date = oldest_completed_item.completed_at.strftime('%Y-%m-%d %H:%M')

                row_data["latest_update_date"] = latest_update_date

                if account.platform in ["wechat_mp", "wechat_service"]:
                    # 微信公众号数据汇总（所有来源的总和）
                    base_summary = db.query(
                        func.sum(WeChatMPUserSource.new_user).label('total_new'),
                        func.sum(WeChatMPUserSource.cancel_user).label('total_cancel'),
                        func.max(WeChatMPUserSource.cumulate_user).label('max_cumulate')
                    ).filter(WeChatMPUserSource.account_id == account.id).first()

                    row_data.update({
                        "new_user": base_summary.total_new or 0,
                        "cancel_user": base_summary.total_cancel or 0,
                        "cumulate_user": base_summary.max_cumulate or 0
                    })

                    # 按user_source分组的数据（仅微信公众号有）
                    for source in user_source_list:
                        source_data = db.query(
                            func.sum(WeChatMPUserSource.new_user)
                        ).filter(
                            WeChatMPUserSource.account_id == account.id,
                            WeChatMPUserSource.user_source == source
                        ).scalar()

                        row_data[f"source_{source}"] = source_data or 0

                elif account.platform == "wechat_channels":
                    # 微信视频号数据汇总
                    channels_summary = db.query(
                        func.sum(WeChatChannelsFollowerData.new_followers).label('total_new'),
                        func.sum(WeChatChannelsFollowerData.unfollowers).label('total_cancel'),
                        func.max(WeChatChannelsFollowerData.total_followers).label('max_cumulate')
                    ).filter(WeChatChannelsFollowerData.account_id == account.id).first()

                    row_data.update({
                        "new_user": channels_summary.total_new or 0,
                        "cancel_user": channels_summary.total_cancel or 0,
                        "cumulate_user": channels_summary.max_cumulate or 0
                    })

                elif account.platform == "xiaohongshu":
                    # 小红书数据汇总
                    xhs_summary = db.query(
                        func.sum(XiaohongshuFansData.new_fans_count).label('total_new'),
                        func.sum(XiaohongshuFansData.unfans_count).label('total_cancel'),
                        func.max(XiaohongshuFansData.total_fans_count).label('max_cumulate')
                    ).filter(XiaohongshuFansData.account_id == account.id).first()

                    row_data.update({
                        "new_user": xhs_summary.total_new or 0,
                        "cancel_user": xhs_summary.total_cancel or 0,
                        "cumulate_user": xhs_summary.max_cumulate or 0
                    })

                result.append(row_data)

            # 获取日期范围
            data_date_range = None
            if latest_sync:
                start_date = latest_sync.start_date.strftime('%m-%d')
                end_date = latest_sync.end_date.strftime('%m-%d')
                data_date_range = f"{start_date}～{end_date}"

            return {
                "success": True,
                "data": result,
                "user_sources": user_source_list,
                "data_date_range": data_date_range
            }

        except Exception as e:
            return {"success": False, "error": f"获取增长汇总失败: {str(e)}"}

    @staticmethod
    def _import_single_video(db: Session, account_id: int, df: pd.DataFrame) -> Tuple[int, int]:
        """导入视频号单篇视频数据"""
        imported_count = 0
        updated_count = 0

        for _, row in df.iterrows():
            # 解析视频ID和发布时间，用于唯一性检查
            video_id = DataDetailsService._parse_str(row.get('视频ID'))
            publish_time = DataDetailsService._parse_datetime(row.get('发布时间'))

            if not video_id:
                continue  # 跳过没有视频ID的记录

            # 检查是否已存在（基于account_id, video_id, publish_time的唯一约束）
            existing = db.query(WeChatChannelsVideoData).filter(
                and_(
                    WeChatChannelsVideoData.account_id == account_id,
                    WeChatChannelsVideoData.video_id == video_id,
                    WeChatChannelsVideoData.publish_time == publish_time
                )
            ).first()

            data = {
                'account_id': account_id,
                'video_description': DataDetailsService._parse_str(row.get('视频描述')),
                'video_id': video_id,
                'publish_time': publish_time,
                'completion_rate': DataDetailsService._parse_str(row.get('完播率')),
                'avg_play_duration': DataDetailsService._parse_int(row.get('平均播放时长')),
                'play_count': DataDetailsService._parse_int(row.get('播放量')),
                'recommend_count': DataDetailsService._parse_int(row.get('推荐')),
                'like_count': DataDetailsService._parse_int(row.get('喜欢')),
                'comment_count': DataDetailsService._parse_int(row.get('评论量')),
                'share_count': DataDetailsService._parse_int(row.get('分享量')),
                'follow_count': DataDetailsService._parse_int(row.get('关注量')),
                'forward_chat_moments': DataDetailsService._parse_int(row.get('转发聊天和朋友圈')),
                'set_as_ringtone': DataDetailsService._parse_int(row.get('设为铃声')),
                'set_as_status': DataDetailsService._parse_int(row.get('设为状态')),
                'set_as_moments_cover': DataDetailsService._parse_int(row.get('设为朋友圈封面'))
            }

            if existing:
                # 更新现有记录
                for key, value in data.items():
                    if key != 'account_id':  # 不更新account_id
                        setattr(existing, key, value)
                existing.updated_at = datetime.utcnow()
                updated_count += 1
            else:
                # 创建新记录
                record = WeChatChannelsVideoData(**data)
                db.add(record)
                imported_count += 1

        return imported_count, updated_count

    @staticmethod
    def _import_note_data(db: Session, account_id: int, df: pd.DataFrame) -> Tuple[int, int]:
        """导入小红书笔记数据"""
        imported_count = 0
        updated_count = 0

        for _, row in df.iterrows():
            # 解析笔记标题和首次发布时间，用于唯一性检查
            note_title = DataDetailsService._parse_str(row.get('笔记标题'))
            first_publish_time = DataDetailsService._parse_datetime(row.get('首次发布时间'))

            if not note_title:
                continue  # 跳过没有笔记标题的记录

            # 检查是否已存在（基于account_id, note_title, first_publish_time的唯一约束）
            existing = db.query(XiaohongshuNoteData).filter(
                and_(
                    XiaohongshuNoteData.account_id == account_id,
                    XiaohongshuNoteData.note_title == note_title,
                    XiaohongshuNoteData.first_publish_time == first_publish_time
                )
            ).first()

            # 解析人均观看时长（可能是时间格式，需要转换为秒）
            avg_view_duration_raw = row.get('人均观看时长')
            avg_view_duration = DataDetailsService._parse_duration_to_seconds(avg_view_duration_raw)

            data = {
                'account_id': account_id,
                'note_title': note_title,
                'first_publish_time': first_publish_time,
                'content_type': DataDetailsService._parse_str(row.get('体裁')),
                'view_count': DataDetailsService._parse_int(row.get('观看量')),
                'like_count': DataDetailsService._parse_int(row.get('点赞')),
                'comment_count': DataDetailsService._parse_int(row.get('评论')),
                'collect_count': DataDetailsService._parse_int(row.get('收藏')),
                'follow_count': DataDetailsService._parse_int(row.get('涨粉')),
                'share_count': DataDetailsService._parse_int(row.get('分享')),
                'avg_view_duration': avg_view_duration,
                'barrage_count': DataDetailsService._parse_int(row.get('弹幕'))
            }

            if existing:
                # 更新现有记录
                for key, value in data.items():
                    if key != 'account_id':  # 不更新account_id
                        setattr(existing, key, value)
                existing.updated_at = datetime.utcnow()
                updated_count += 1
            else:
                # 创建新记录
                record = XiaohongshuNoteData(**data)
                db.add(record)
                imported_count += 1

        return imported_count, updated_count

    @staticmethod
    def _parse_duration_to_seconds(duration_str) -> float:
        """解析时长字符串为秒数

        支持格式：
        - "1:30" -> 90秒
        - "0:45" -> 45秒
        - "90" -> 90秒
        - "1.5" -> 1.5秒
        """
        if pd.isna(duration_str) or duration_str is None:
            return 0.0

        duration_str = str(duration_str).strip()
        if not duration_str:
            return 0.0

        try:
            # 如果包含冒号，按分:秒格式解析
            if ':' in duration_str:
                parts = duration_str.split(':')
                if len(parts) == 2:
                    minutes = float(parts[0])
                    seconds = float(parts[1])
                    return minutes * 60 + seconds

            # 否则直接解析为秒数
            return float(duration_str)
        except (ValueError, TypeError):
            return 0.0
