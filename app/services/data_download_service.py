import asyncio
import os
import shutil
import zipfile
from datetime import datetime, date
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_

from app.models import DataDownloadRecord, PlatformAccount, User
from app.services.wechat_service import WeChatMPService
from app.services.wechat_channels_service import WeChatChannelsService
from app.services.xiaohongshu_service import XiaohongshuService
from app.database import SessionLocal
from app.utils.excel_parser import ExcelDataParser
from app.exceptions import LoginExpiredException
import logging

logger = logging.getLogger(__name__)


class DataDownloadService:
    """数据下载服务类"""

    # 微信公众号数据类型配置
    WECHAT_MP_DATA_TYPE_CONFIG = {
        "content_trend": "内容数据趋势明细",
        "content_source": "内容流量来源明细",
        "content_detail": "内容已通知内容明细",
        "user_channel": "用户增长明细",
        "user_source": "用户来源明细"
    }

    # 微信视频号数据类型配置
    WECHAT_CHANNELS_DATA_TYPE_CONFIG = {
        "single_video": "单篇视频数据"
    }

    # 小红书数据类型配置
    XIAOHONGSHU_DATA_TYPE_CONFIG = {
        "note_data": "笔记数据"
    }

    # 合并所有数据类型配置
    DATA_TYPE_CONFIG = {
        **WECHAT_MP_DATA_TYPE_CONFIG,
        **WECHAT_CHANNELS_DATA_TYPE_CONFIG,
        **XIAOHONGSHU_DATA_TYPE_CONFIG
    }

    # Excel下载支持的数据类型
    EXCEL_DOWNLOAD_TYPES = ["content_trend", "content_source", "content_detail", "user_channel"]

    # 视频号Excel下载支持的数据类型
    WECHAT_CHANNELS_EXCEL_TYPES = ["single_video"]

    # 小红书Excel下载支持的数据类型
    XIAOHONGSHU_EXCEL_TYPES = ["note_data"]

    # AJAX获取的数据类型
    AJAX_DATA_TYPES = ["user_source"]

    # 用户来源字段映射
    USER_SOURCE_MAPPING = {
        0: "其他合计",
        1: "公众号搜索",
        17: "名片分享",
        30: "扫描二维码",
        57: "文章内账号名称",
        100: "微信广告",
        149: "小程序关注",
        161: "他人转载",
        200: "视频号",
        201: "直播"
    }

    # 支持生成饼图的数据类型
    CHART_SUPPORTED_TYPES = ["content_trend", "user_source"]

    # 临时下载目录
    DOWNLOAD_BASE_DIR = "temp_downloads"

    @staticmethod
    def get_platform_data_types(platform: str) -> List[str]:
        """根据平台类型获取支持的数据类型

        Args:
            platform: 平台类型

        Returns:
            支持的数据类型列表
        """
        if platform == "wechat_channels":
            return list(DataDownloadService.WECHAT_CHANNELS_DATA_TYPE_CONFIG.keys())
        elif platform in ["wechat_mp", "wechat_service"]:
            return list(DataDownloadService.WECHAT_MP_DATA_TYPE_CONFIG.keys())
        elif platform == "xiaohongshu":
            return list(DataDownloadService.XIAOHONGSHU_DATA_TYPE_CONFIG.keys())
        else:
            return []
    
    @staticmethod
    def validate_download_request(start_date: str, end_date: str, account_ids: List[int],
                                data_types: Optional[List[str]] = None) -> Dict[str, Any]:
        """验证下载请求参数
        
        Args:
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            account_ids: 账号ID列表
            data_types: 数据类型列表
            
        Returns:
            验证结果
        """
        try:
            start = datetime.strptime(start_date, '%Y-%m-%d').date()
            end = datetime.strptime(end_date, '%Y-%m-%d').date()
            
            if start > end:
                return {"success": False, "error": "开始日期不能晚于结束日期"}
            
            if (end - start).days > 90:
                return {"success": False, "error": "日期范围不能超过90天"}
            
            if not account_ids:
                return {"success": False, "error": "请至少选择一个账号"}

            # 如果提供了数据类型，则验证数据类型
            if data_types:
                invalid_types = [dt for dt in data_types if dt not in DataDownloadService.DATA_TYPE_CONFIG]
                if invalid_types:
                    return {"success": False, "error": f"不支持的数据类型: {', '.join(invalid_types)}"}
            
            return {"success": True, "start_date": start, "end_date": end}
            
        except ValueError as e:
            return {"success": False, "error": f"日期格式错误: {str(e)}"}
    
    @staticmethod
    def get_user_accounts(db: Session, user_id: int, account_ids: List[int]) -> List[PlatformAccount]:
        """获取用户的指定账号列表

        Args:
            db: 数据库会话
            user_id: 用户ID
            account_ids: 账号ID列表

        Returns:
            账号列表
        """
        return db.query(PlatformAccount).filter(
            and_(
                PlatformAccount.id.in_(account_ids),
                PlatformAccount.user_id == user_id,
                PlatformAccount.platform.in_(["wechat_mp", "wechat_service", "wechat_channels", "xiaohongshu"]),
                PlatformAccount.login_status == True
            )
        ).all()
    
    @staticmethod
    def create_download_record(db: Session, user_id: int, start_date: date, end_date: date,
                             account_ids: List[int], data_types: List[str]) -> DataDownloadRecord:
        """创建下载记录
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            start_date: 开始日期
            end_date: 结束日期
            account_ids: 账号ID列表
            data_types: 数据类型列表
            
        Returns:
            下载记录对象
        """
        # 根据账号平台类型计算总文件数
        accounts = db.query(PlatformAccount).filter(PlatformAccount.id.in_(account_ids)).all()

        total_files = 0
        for account in accounts:
            # 获取该账号平台支持的数据类型
            account_data_types = DataDownloadService.get_platform_data_types(account.platform)

            # 每个数据类型生成1个Excel文件
            excel_files_count = len(account_data_types)

            # 计算可能生成的饼图文件数（仅微信公众号支持）
            chart_files_count = 0
            if account.platform in ["wechat_mp", "wechat_service"]:
                chart_files_count = len([dt for dt in account_data_types if dt in DataDownloadService.CHART_SUPPORTED_TYPES])

            total_files += excel_files_count + chart_files_count
        
        record = DataDownloadRecord(
            user_id=user_id,
            start_date=start_date,
            end_date=end_date,
            selected_accounts=account_ids,
            selected_data_types=data_types,
            status='running',
            total_files=total_files,
            completed_files=0
        )
        db.add(record)
        db.commit()
        db.refresh(record)
        return record
    
    @staticmethod
    def update_record_status(db: Session, record_id: int, **kwargs) -> bool:
        """更新记录状态
        
        Args:
            db: 数据库会话
            record_id: 记录ID
            **kwargs: 要更新的字段
            
        Returns:
            更新是否成功
        """
        try:
            record = db.query(DataDownloadRecord).filter(DataDownloadRecord.id == record_id).first()
            if not record:
                return False
                
            for key, value in kwargs.items():
                if hasattr(record, key):
                    setattr(record, key, value)
            
            record.updated_at = datetime.utcnow()
            
            if kwargs.get('status') == 'completed':
                record.completed_at = datetime.utcnow()
                
            db.commit()
            return True
            
        except Exception as e:
            logger.error(f"更新记录状态失败: {str(e)}")
            db.rollback()
            return False
    
    @staticmethod
    def create_download_directory(record_id: int) -> str:
        """创建下载目录

        Args:
            record_id: 记录ID

        Returns:
            下载目录路径
        """
        # 确保基础目录存在
        os.makedirs(DataDownloadService.DOWNLOAD_BASE_DIR, exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        download_dir = os.path.join(DataDownloadService.DOWNLOAD_BASE_DIR, f"download_{record_id}_{timestamp}")

        os.makedirs(download_dir, exist_ok=True)
        return download_dir
    
    @staticmethod
    def generate_filename(account_name: str, data_type: str, start_date: str, end_date: str, platform: str = "wechat_mp") -> str:
        """生成文件名

        Args:
            account_name: 账号名称
            data_type: 数据类型
            start_date: 开始日期
            end_date: 结束日期
            platform: 平台类型

        Returns:
            文件名
        """
        data_type_name = DataDownloadService.DATA_TYPE_CONFIG.get(data_type, data_type)
        # 清理文件名中的特殊字符
        safe_account_name = "".join(c for c in account_name if c.isalnum() or c in (' ', '-', '_')).strip()

        # 根据平台类型添加前缀
        if platform == "wechat_channels":
            prefix = "视频号"
        elif platform == "wechat_mp" or platform == "wechat_service":
            prefix = "公众号"
        elif platform == "xiaohongshu":
            prefix = "小红书"
        elif platform == "douyin":
            prefix = "抖音"
        else:
            # 其他平台使用平台名作为前缀
            prefix = f"{platform}_data"

        return f"{prefix}_{safe_account_name}+{data_type_name}+{start_date}_to_{end_date}.xlsx"

    @staticmethod
    async def download_account_data(account_id: int, data_types: List[str], start_date: str,
                                  end_date: str, download_dir: str, record_id: int) -> Dict[str, Any]:
        """下载单个账号的多个数据类型

        Args:
            account_id: 账号ID
            data_types: 数据类型列表
            start_date: 开始日期
            end_date: 结束日期
            download_dir: 下载目录
            record_id: 下载记录ID

        Returns:
            下载结果
        """
        db = SessionLocal()
        try:
            # 获取账号信息
            account = db.query(PlatformAccount).filter(PlatformAccount.id == account_id).first()
            if not account:
                return {"success": False, "error": f"账号ID {account_id} 不存在"}

            downloaded_files = []
            failed_files = []

            # 根据平台类型选择服务
            if account.platform == "wechat_channels":
                # 处理视频号账号
                channels_service = WeChatChannelsService(account_id=account_id)

                # 尝试加载已保存的登录状态
                if not await channels_service.load_login_state():
                    error_msg = f"视频号账号 {account.name} 登录状态恢复失败，请重新登录"
                    logger.error(error_msg)
                    return {"success": False, "error": error_msg}

                # 处理视频号数据类型
                channels_types = [dt for dt in data_types if dt in DataDownloadService.WECHAT_CHANNELS_EXCEL_TYPES]

                for data_type in channels_types:
                    try:
                        logger.info(f"下载视频号账号 {account.name} 的 {data_type} 数据")

                        # 下载视频号数据
                        excel_data = await channels_service.download_single_video_data(
                            start_date=start_date,
                            end_date=end_date
                        )

                        if not excel_data:
                            failed_files.append({
                                "data_type": data_type,
                                "error": "下载数据失败"
                            })
                            continue

                        # 生成文件名
                        filename = DataDownloadService.generate_filename(
                            account.name, data_type, start_date, end_date, account.platform
                        )

                        # 保存文件
                        parser = ExcelDataParser()
                        if not parser.save_excel_file(download_dir, filename, excel_data):
                            failed_files.append({
                                "data_type": data_type,
                                "error": "保存文件失败"
                            })
                            continue

                        downloaded_files.append({
                            "filename": filename,
                            "data_type": data_type
                        })

                        logger.info(f"成功下载: {filename}")

                    except LoginExpiredException as e:
                        # 登录过期，立即返回错误，不再继续下载
                        error_msg = f"视频号账号 {account.name} 登录状态已过期: {e.message}"
                        logger.error(error_msg)
                        return {"success": False, "error": error_msg, "login_expired": True}
                    except Exception as e:
                        logger.error(f"下载视频号数据失败: {str(e)}")
                        failed_files.append({
                            "data_type": data_type,
                            "error": str(e)
                        })

                # 关闭视频号服务
                await channels_service.close()

            elif account.platform == "xiaohongshu":
                # 处理小红书账号
                xiaohongshu_service = XiaohongshuService(account_id=account_id)

                # 尝试加载已保存的登录状态
                if not await xiaohongshu_service.load_login_state():
                    error_msg = f"小红书账号 {account.name} 登录状态恢复失败，请重新登录"
                    logger.error(error_msg)
                    return {"success": False, "error": error_msg}

                # 处理小红书数据类型
                xiaohongshu_types = [dt for dt in data_types if dt in DataDownloadService.XIAOHONGSHU_EXCEL_TYPES]

                for data_type in xiaohongshu_types:
                    try:
                        logger.info(f"下载小红书账号 {account.name} 的 {data_type} 数据")

                        # 下载小红书笔记数据
                        excel_data = await xiaohongshu_service.download_note_data_excel(
                            begin_date=start_date,
                            end_date=end_date
                        )

                        if not excel_data:
                            failed_files.append({
                                "data_type": data_type,
                                "error": "下载数据失败"
                            })
                            continue

                        # 生成文件名
                        filename = DataDownloadService.generate_filename(
                            account.name, data_type, start_date, end_date, account.platform
                        )

                        # 保存文件
                        parser = ExcelDataParser()
                        if not parser.save_excel_file(download_dir, filename, excel_data):
                            failed_files.append({
                                "data_type": data_type,
                                "error": "保存文件失败"
                            })
                            continue

                        downloaded_files.append({
                            "filename": filename,
                            "data_type": data_type
                        })

                        logger.info(f"成功下载: {filename}")

                    except Exception as e:
                        logger.error(f"下载小红书数据失败: {str(e)}")
                        failed_files.append({
                            "data_type": data_type,
                            "error": str(e)
                        })

                # 截图粉丝数据概览
                try:
                    logger.info(f"开始截图小红书账号 {account.name} 的粉丝数据概览")

                    # 生成截图文件名
                    screenshot_filename = f"小红书_{account.name}_fans_overview_{start_date}_{end_date}.png"
                    screenshot_path = os.path.join(download_dir, screenshot_filename)

                    # 执行截图
                    result_path = await xiaohongshu_service.screenshot_fans_overview(screenshot_path)

                    if result_path:
                        downloaded_files.append({
                            "filename": screenshot_filename,
                            "path": result_path,
                            "data_type": "fans_overview_screenshot",
                            "account_name": account.name,
                            "platform": account.platform
                        })
                        logger.info(f"成功截图粉丝数据概览: {screenshot_filename}")
                    else:
                        logger.warning(f"截图粉丝数据概览失败")
                        failed_files.append({
                            "data_type": "fans_overview_screenshot",
                            "error": "截图失败"
                        })

                except Exception as e:
                    logger.error(f"截图粉丝数据概览失败: {str(e)}")
                    failed_files.append({
                        "data_type": "fans_overview_screenshot",
                        "error": str(e)
                    })

                # 截图账号总览
                try:
                    logger.info(f"开始截图小红书账号 {account.name} 的账号总览")

                    # 生成截图文件名
                    account_screenshot_filename = f"小红书_{account.name}_account_overview_{start_date}_{end_date}.png"
                    account_screenshot_path = os.path.join(download_dir, account_screenshot_filename)

                    # 执行截图
                    result_path = await xiaohongshu_service.screenshot_account_overview(account_screenshot_path)

                    if result_path:
                        downloaded_files.append({
                            "filename": account_screenshot_filename,
                            "path": result_path,
                            "data_type": "account_overview_screenshot",
                            "account_name": account.name,
                            "platform": account.platform
                        })
                        logger.info(f"成功截图账号总览: {account_screenshot_filename}")
                    else:
                        logger.warning(f"截图账号总览失败")
                        failed_files.append({
                            "data_type": "account_overview_screenshot",
                            "error": "截图失败"
                        })

                except Exception as e:
                    logger.error(f"截图账号总览失败: {str(e)}")
                    failed_files.append({
                        "data_type": "account_overview_screenshot",
                        "error": str(e)
                    })

                # 关闭小红书服务
                await xiaohongshu_service.close()

            else:
                # 处理微信公众号账号
                wechat_service = WeChatMPService(account_id=account_id)

                # 尝试加载已保存的登录状态
                if not await wechat_service.load_login_state():
                    error_msg = f"账号 {account.name} 登录状态恢复失败，请重新登录"
                    logger.error(error_msg)
                    return {"success": False, "error": error_msg}

                # 分别处理Excel下载和AJAX获取的数据类型
                excel_types = [dt for dt in data_types if dt in DataDownloadService.EXCEL_DOWNLOAD_TYPES]
                ajax_types = [dt for dt in data_types if dt in DataDownloadService.AJAX_DATA_TYPES]

                # 处理Excel下载的数据类型
                for data_type in excel_types:
                    try:
                        logger.info(f"下载账号 {account.name} 的 {data_type} 数据")

                        # 下载Excel数据（设置auto_import=False避免自动导入）
                        excel_data = await wechat_service.download_data_excel(
                            begin_date=start_date,
                            end_date=end_date,
                            data_type=data_type,
                            auto_import=False
                        )

                        if not excel_data:
                            failed_files.append({
                                "data_type": data_type,
                                "error": "下载数据失败"
                            })
                            continue

                        # 生成文件名
                        filename = DataDownloadService.generate_filename(
                            account.name, data_type, start_date, end_date, account.platform
                        )

                        # 保存文件
                        parser = ExcelDataParser()
                        if not parser.save_excel_file(download_dir, filename, excel_data):
                            failed_files.append({
                                "data_type": data_type,
                                "error": "保存文件失败"
                            })
                            continue

                        downloaded_files.append({
                            "filename": filename,
                            "data_type": data_type
                        })

                        logger.info(f"成功下载: {filename}")

                    except Exception as e:
                        error_msg = f"下载数据类型 {data_type} 时发生错误: {str(e)}"
                        logger.error(error_msg)
                        failed_files.append({
                            "data_type": data_type,
                            "error": error_msg
                        })

                # 处理AJAX获取的数据类型（如user_source）
                for data_type in ajax_types:
                    try:
                        logger.info(f"获取账号 {account.name} 的 {data_type} 数据")

                        if data_type == "user_source":
                            # 如果没有进行过Excel下载，需要先确保页面已导航到微信公众号
                            if not excel_types:
                                # 确保页面已初始化并导航到微信公众号
                                if not wechat_service.page:
                                    logger.error("页面未初始化，无法获取用户来源数据")
                                    failed_files.append({
                                        "data_type": data_type,
                                        "error": "页面未初始化"
                                    })
                                    continue

                                # 检查当前URL，如果不在微信公众号页面，先导航过去
                                current_url = wechat_service.page.url
                                logger.info(f"当前URL: {current_url}")

                                if "mp.weixin.qq.com" not in current_url or "token=" not in current_url:
                                    logger.info("当前不在微信公众号页面，正在导航到微信公众号主页...")
                                    try:
                                        await wechat_service.page.goto("https://mp.weixin.qq.com",
                                                                   wait_until="domcontentloaded", timeout=15000)
                                        await asyncio.sleep(3)  # 等待自动跳转完成

                                        # 更新当前URL
                                        current_url = wechat_service.page.url
                                        logger.info(f"导航后的URL: {current_url}")

                                        # 再次检查是否有token
                                        if "token=" not in current_url:
                                            logger.error("导航后仍未检测到登录状态，可能登录已过期")
                                            failed_files.append({
                                                "data_type": data_type,
                                                "error": "登录状态已过期"
                                            })
                                            continue

                                    except Exception as nav_e:
                                        logger.error(f"导航到微信公众号页面失败: {nav_e}")
                                        failed_files.append({
                                            "data_type": data_type,
                                            "error": f"导航失败: {str(nav_e)}"
                                        })
                                        continue

                            # 获取用户来源数据
                            user_source_data = await wechat_service.fetch_user_source_data(
                                begin_date=start_date,
                                end_date=end_date
                            )

                            if not user_source_data:
                                failed_files.append({
                                    "data_type": data_type,
                                    "error": "获取用户来源数据失败"
                                })
                                continue

                            # 将数据转换为Excel格式并保存
                            excel_data = DataDownloadService.convert_user_source_to_excel(
                                user_source_data, account.name, start_date, end_date
                            )

                            if not excel_data:
                                failed_files.append({
                                    "data_type": data_type,
                                    "error": "转换用户来源数据为Excel失败"
                                })
                                continue

                            # 生成文件名
                            filename = DataDownloadService.generate_filename(
                                account.name, data_type, start_date, end_date, account.platform
                            )

                            # 保存文件
                            parser = ExcelDataParser()
                            if not parser.save_excel_file(download_dir, filename, excel_data):
                                failed_files.append({
                                    "data_type": data_type,
                                    "error": "保存文件失败"
                                })
                                continue

                            downloaded_files.append({
                                "filename": filename,
                                "data_type": data_type
                            })

                            logger.info(f"成功获取并保存: {filename}")

                    except Exception as e:
                        error_msg = f"获取数据类型 {data_type} 时发生错误: {str(e)}"
                        logger.error(error_msg)
                        failed_files.append({
                            "data_type": data_type,
                            "error": error_msg
                        })

                # 检查是否下载了content_trend文件，如果有则生成饼图
                content_trend_files = [f for f in downloaded_files if f["data_type"] == "content_trend"]

                for content_trend_file in content_trend_files:
                    try:
                        # 生成饼图
                        excel_file_path = os.path.join(download_dir, content_trend_file["filename"])
                        chart_path = DataDownloadService.generate_content_trend_pie_chart(
                            excel_file_path, account.name, start_date, end_date, download_dir
                        )

                        if chart_path:
                            # 将图片文件添加到下载文件列表
                            chart_filename = os.path.basename(chart_path)
                            downloaded_files.append({
                                "filename": chart_filename,
                                "data_type": "content_source_chart"
                            })
                            logger.info(f"成功生成内容流量来源饼图: {chart_filename}")
                        else:
                            logger.warning(f"生成内容流量来源饼图失败: {content_trend_file['filename']}")

                    except Exception as e:
                        logger.error(f"生成内容流量来源饼图时发生错误: {str(e)}")

                # 检查是否下载了user_source文件，如果有则生成用户来源饼图
                user_source_files = [f for f in downloaded_files if f["data_type"] == "user_source"]

                for user_source_file in user_source_files:
                    try:
                        # 生成用户来源饼图
                        excel_file_path = os.path.join(download_dir, user_source_file["filename"])
                        chart_path = DataDownloadService.generate_user_source_pie_chart(
                            excel_file_path, account.name, start_date, end_date, download_dir
                        )

                        if chart_path:
                            # 将图片文件添加到下载文件列表
                            chart_filename = os.path.basename(chart_path)
                            downloaded_files.append({
                                "filename": chart_filename,
                                "data_type": "user_source_chart"
                            })
                            logger.info(f"成功生成用户来源饼图: {chart_filename}")
                        else:
                            logger.warning(f"生成用户来源饼图失败: {user_source_file['filename']}")

                    except Exception as e:
                        logger.error(f"生成用户来源饼图时发生错误: {str(e)}")

            return {
                "success": True,
                "account_name": account.name,
                "downloaded_files": downloaded_files,
                "failed_files": failed_files,
                "total_downloaded": len(downloaded_files),
                "total_failed": len(failed_files)
            }

        except Exception as e:
            error_msg = f"下载账号 {account_id} 时发生错误: {str(e)}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
        finally:
            db.close()

    @staticmethod
    def create_zip_file(download_dir: str, record_id: int) -> str:
        """创建ZIP压缩文件

        Args:
            download_dir: 下载目录
            record_id: 记录ID

        Returns:
            ZIP文件路径
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        zip_filename = f"data_download_{record_id}_{timestamp}.zip"
        zip_path = os.path.join(DataDownloadService.DOWNLOAD_BASE_DIR, zip_filename)

        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, _, files in os.walk(download_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    # 在ZIP中保持目录结构
                    arcname = os.path.relpath(file_path, download_dir)
                    zipf.write(file_path, arcname)

        return zip_path

    @staticmethod
    def convert_user_source_to_excel(user_source_data: dict, account_name: str,
                                   start_date: str, end_date: str) -> Optional[bytes]:
        """将用户来源数据转换为Excel格式

        Args:
            user_source_data: 用户来源数据
            account_name: 账号名称
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            Excel文件的二进制数据
        """
        try:
            import pandas as pd
            from io import BytesIO

            # 解析用户来源数据
            category_list = user_source_data.get('category_list', [])

            # 准备Excel数据
            excel_data = []

            for category in category_list:
                user_source = category.get('user_source')
                data_list = category.get('list', [])

                if user_source is None:
                    continue

                for item in data_list:
                    excel_data.append({
                        '账号名称': account_name,
                        '用户来源': user_source,
                        '日期': item.get('date', ''),
                        '新增用户': item.get('new_user', 0),
                        '取消关注用户': item.get('cancel_user', 0),
                        '净增用户': item.get('netgain_user', 0),
                        '累计用户': item.get('cumulate_user', 0)
                    })

            if not excel_data:
                logger.warning("用户来源数据为空")
                return None

            # 创建DataFrame
            df = pd.DataFrame(excel_data)

            # 将DataFrame写入Excel
            buffer = BytesIO()
            with pd.ExcelWriter(buffer, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='用户来源明细', index=False)

            buffer.seek(0)
            excel_bytes = buffer.getvalue()
            buffer.close()

            logger.info(f"用户来源数据转换为Excel成功，数据行数: {len(excel_data)}")
            return excel_bytes

        except Exception as e:
            logger.error(f"转换用户来源数据为Excel失败: {str(e)}")
            return None

    @staticmethod
    def generate_content_trend_pie_chart(excel_file_path: str, account_name: str,
                                         start_date: str, end_date: str, output_dir: str) -> Optional[str]:
        """基于内容数据趋势明细Excel文件生成饼图

        Args:
            excel_file_path: Excel文件路径
            account_name: 账号名称
            start_date: 开始日期
            end_date: 结束日期
            output_dir: 输出目录

        Returns:
            生成的图片文件路径，失败返回None
        """
        try:
            import pandas as pd
            import matplotlib.pyplot as plt
            import matplotlib.font_manager as fm
            import numpy as np
            from collections import defaultdict

            # 导入字体配置
            from app.utils.font_config import configure_chinese_fonts

            # 配置中文字体支持
            configure_chinese_fonts()

            # 读取Excel文件，尝试不同的引擎
            df = None
            engines = ['openpyxl', 'xlrd']

            for engine in engines:
                try:
                    df = pd.read_excel(excel_file_path, engine=engine)
                    logger.info(f"成功使用 {engine} 引擎读取Excel文件")
                    break
                except Exception as e:
                    logger.warning(f"{engine} 引擎读取失败: {str(e)}")
                    continue

            if df is None:
                logger.error("所有引擎都无法读取Excel文件")
                return None

            # 检查必要的列是否存在
            required_columns = ['渠道', '阅读次数']
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                logger.error(f"Excel文件缺少必要的列: {missing_columns}")
                return None

            # 按渠道聚合数据，排除"全部"统计项
            channel_data = defaultdict(int)

            for _, row in df.iterrows():
                channel = str(row['渠道']).strip()
                read_count = row['阅读次数']

                # 处理数字类型
                if pd.isna(read_count):
                    read_count = 0
                else:
                    try:
                        read_count = int(float(read_count))
                    except (ValueError, TypeError):
                        read_count = 0

                # 排除"全部"统计项，因为它是其他渠道的总和
                if channel and channel != 'nan' and channel != '全部':
                    channel_data[channel] += read_count

            if not channel_data:
                logger.warning("没有找到有效的渠道数据")
                return None

            # 计算总阅读次数
            total_reads = sum(channel_data.values())

            if total_reads == 0:
                logger.warning("总阅读次数为0，无法生成饼图")
                return None

            # 准备饼图数据
            channels = list(channel_data.keys())
            values = list(channel_data.values())
            percentages = [v / total_reads * 100 for v in values]

            # 中文字体已通过font_config模块配置

            # 创建图形，增大尺寸为连接线和标签留出空间
            _, ax = plt.subplots(figsize=(12, 10))

            # 定义颜色方案（参考样例图片）
            colors = ['#2ECC71', '#1ABC9C', '#3498DB', '#9B59B6', '#E74C3C', '#F39C12', '#34495E', '#95A5A6']

            # 创建饼图，不显示标签（稍后手动添加带连接线的标签）
            wedges, _, _ = ax.pie(
                values,
                labels=None,  # 不显示自动标签
                colors=colors[:len(channels)],
                autopct='',  # 不显示自动百分比
                startangle=90,
                wedgeprops=dict(width=0.3),  # 创建环形图
                pctdistance=0.85
            )

            # 智能标签布局算法 - 增强版防重叠算法
            def calculate_smart_label_positions(wedges, channels, values, percentages):
                """计算智能标签位置，彻底避免重叠"""

                # 环形图的半径配置
                outer_radius = 1.0  # 外圆半径（连接线起点）
                min_label_radius = 1.4  # 最小标签距离（增大）
                max_label_radius = 2.2  # 最大标签距离（增大）
                min_label_spacing = 0.25  # 标签间最小间距（增大）
                label_height = 0.08  # 估算标签高度

                # 收集所有楔形的信息
                wedge_info = []
                for i, (wedge, channel, value, pct) in enumerate(zip(wedges, channels, values, percentages)):
                    angle = (wedge.theta2 + wedge.theta1) / 2
                    # 标准化角度到 0-360 范围
                    if angle < 0:
                        angle += 360
                    
                    wedge_info.append({
                        'index': i,
                        'angle': angle,
                        'channel': channel,
                        'value': value,
                        'pct': pct,
                        'wedge': wedge,
                        'size': pct,
                        'label_width': len(str(channel)) * 0.01 + 0.1,  # 估算标签宽度
                        'label_height': label_height
                    })

                def check_label_overlap(x1, y1, w1, h1, x2, y2, w2, h2, padding=0.05):
                    """检查两个矩形标签是否重叠（考虑标签的实际尺寸）"""
                    # 扩展边界框，增加padding
                    left1, right1 = x1 - w1/2 - padding, x1 + w1/2 + padding
                    top1, bottom1 = y1 + h1/2 + padding, y1 - h1/2 - padding
                    
                    left2, right2 = x2 - w2/2 - padding, x2 + w2/2 + padding
                    top2, bottom2 = y2 + h2/2 + padding, y2 - h2/2 - padding
                    
                    # 检查是否重叠
                    return not (right1 < left2 or right2 < left1 or bottom1 > top2 or bottom2 > top1)

                def find_best_position_for_label(info, existing_labels):
                    """为标签找到最佳位置，彻底避免重叠"""
                    angle = info['angle']
                    original_angle_rad = np.radians(angle)
                    
                    # 连接线起点
                    x1 = outer_radius * np.cos(original_angle_rad)
                    y1 = outer_radius * np.sin(original_angle_rad)
                    
                    best_position = None
                    min_distance_from_original = float('inf')
                    
                    # 尝试不同的位置组合
                    radius_steps = [1.4, 1.5, 1.6, 1.7, 1.8, 1.9, 2.0, 2.1, 2.2]
                    angle_offsets = [0, 10, -10, 15, -15, 20, -20, 25, -25, 30, -30, 
                                   35, -35, 40, -40, 45, -45, 50, -50, 60, -60]
                    
                    for radius in radius_steps:
                        for angle_offset in angle_offsets:
                            test_angle = angle + angle_offset
                            test_angle_rad = np.radians(test_angle)
                            
                            x_test = radius * np.cos(test_angle_rad)
                            y_test = radius * np.sin(test_angle_rad)
                            
                            # 检查是否与现有标签重叠
                            has_overlap = False
                            for existing in existing_labels:
                                if check_label_overlap(
                                    x_test, y_test, info['label_width'], info['label_height'],
                                    existing['x_label'], existing['y_label'], 
                                    existing['label_width'], existing['label_height']
                                ):
                                    has_overlap = True
                                    break
                            
                            if not has_overlap:
                                # 计算与原始位置的距离
                                original_x = min_label_radius * np.cos(original_angle_rad)
                                original_y = min_label_radius * np.sin(original_angle_rad)
                                distance = np.sqrt((x_test - original_x)**2 + (y_test - original_y)**2)
                                
                                if distance < min_distance_from_original:
                                    min_distance_from_original = distance
                                    best_position = {
                                        'x_label': x_test,
                                        'y_label': y_test,
                                        'radius': radius,
                                        'angle_offset': angle_offset
                                    }
                    
                    # 如果找不到无重叠位置，使用强制分离策略
                    if best_position is None:
                        logger.warning(f"无法为标签 {info['channel']} 找到无重叠位置，使用强制分离")
                        # 使用最大半径和较大角度偏移
                        forced_angle = angle + (len(existing_labels) * 25 % 360)
                        forced_angle_rad = np.radians(forced_angle)
                        best_position = {
                            'x_label': max_label_radius * np.cos(forced_angle_rad),
                            'y_label': max_label_radius * np.sin(forced_angle_rad),
                            'radius': max_label_radius,
                            'angle_offset': forced_angle - angle
                        }
                    
                    return best_position

                # 按大小排序，大的扇形优先获得最佳位置
                wedge_info.sort(key=lambda x: -x['size'])
                
                positioned_labels = []
                
                for info in wedge_info:
                    # 为当前标签找到最佳位置
                    best_pos = find_best_position_for_label(info, positioned_labels)
                    
                    # 计算连接线起点
                    angle_rad = np.radians(info['angle'])
                    x1 = outer_radius * np.cos(angle_rad)
                    y1 = outer_radius * np.sin(angle_rad)
                    
                    # 计算连接线中间点（使用贝塞尔曲线控制点）
                    control_factor = 0.3
                    x_mid = x1 + (best_pos['x_label'] - x1) * control_factor
                    y_mid = y1 + (best_pos['y_label'] - y1) * control_factor
                    
                    # 确定文本对齐方式
                    ha = 'left' if best_pos['x_label'] > 0 else 'right'
                    
                    positioned_labels.append({
                        **info,
                        'x1': x1, 'y1': y1,  # 连接线起点
                        'x_mid': x_mid, 'y_mid': y_mid,  # 中间点
                        'x_label': best_pos['x_label'], 
                        'y_label': best_pos['y_label'],  # 标签位置
                        'ha': ha,
                        'final_radius': best_pos['radius'],
                        'angle_offset': best_pos['angle_offset']
                    })
                
                # 最后按原始索引排序返回
                positioned_labels.sort(key=lambda x: x['index'])
                
                # 输出调试信息
                logger.info("标签布局结果:")
                for label in positioned_labels:
                    logger.info(f"  {label['channel']}: 半径={label['final_radius']:.2f}, "
                              f"角度偏移={label['angle_offset']:.1f}°, "
                              f"位置=({label['x_label']:.2f}, {label['y_label']:.2f})")
                
                return positioned_labels

            # 计算智能标签位置
            positioned_labels = calculate_smart_label_positions(wedges, channels, values, percentages)

            # 绘制标签和连接线
            bbox_props = dict(boxstyle="round,pad=0.4", facecolor="white", alpha=0.95,
                            edgecolor="#999999", linewidth=0.8)

            for label_info in positioned_labels:
                # 绘制更优雅的连接线 - 使用曲线
                x1, y1 = label_info['x1'], label_info['y1']
                x_label, y_label = label_info['x_label'], label_info['y_label']
                
                # 计算控制点创建平滑曲线
                mid_x = (x1 + x_label) * 0.5
                mid_y = (y1 + y_label) * 0.5
                
                # 绘制分段连接线
                ax.plot([x1, mid_x], [y1, mid_y],
                       color='#888888', linewidth=1.5, alpha=0.8)
                ax.plot([mid_x, x_label], [mid_y, y_label],
                       color='#888888', linewidth=1.5, alpha=0.8)

                # 在连接线末端添加小圆点作为指示器
                ax.plot(x_label, y_label, 'o', color='#666666', markersize=3, alpha=0.8)

                # 添加标签 - 调整字体大小防止过于拥挤
                label_text = f"{label_info['channel']}\n{label_info['value']}次\n({label_info['pct']:.1f}%)"

                # 根据标签长度调整字体大小
                font_size = 9 if len(label_info['channel']) <= 6 else 8

                ax.text(x_label, y_label, label_text,
                       fontsize=font_size, ha=label_info['ha'], va='center',
                       bbox=bbox_props, zorder=10,
                       linespacing=1.2)  # 增加行间距

            # 在中心添加总次数文本
            ax.text(0, 0.05, '总次数', ha='center', va='center', fontsize=12, color='#999999')
            ax.text(0, -0.05, f'{total_reads}次', ha='center', va='center', fontsize=14, fontweight='bold', color='#333333')

            # 不需要图例，因为已经有带连接线的标签

            # 设置标题
            title = f'{account_name} 内容流量来源分布\n{start_date} 至 {end_date}'
            ax.set_title(title, fontsize=16, fontweight='bold', pad=20)

            # 确保图形是圆形
            ax.axis('equal')

            # 生成文件名
            safe_account_name = "".join(c for c in account_name if c.isalnum() or c in (' ', '-', '_')).strip()
            filename = f"{safe_account_name}_内容流量来源分布_{start_date}_to_{end_date}.png"
            output_path = os.path.join(output_dir, filename)

            # 保存图片
            plt.tight_layout()
            plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()

            logger.info(f"成功生成饼图: {output_path}")
            return output_path

        except Exception as e:
            logger.error(f"生成内容流量来源饼图失败: {str(e)}")
            return None

    @staticmethod
    async def start_download_task(user_id: int, start_date: str, end_date: str,
                                account_ids: List[int], data_types: Optional[List[str]] = None) -> Dict[str, Any]:
        """启动数据下载任务

        Args:
            user_id: 用户ID
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            account_ids: 账号ID列表
            data_types: 数据类型列表

        Returns:
            任务启动结果，包含任务ID
        """
        # 验证请求参数
        validation = DataDownloadService.validate_download_request(
            start_date, end_date, account_ids, data_types
        )
        if not validation["success"]:
            return validation

        db = SessionLocal()
        try:
            # 检查是否有正在运行的任务
            running_task = db.query(DataDownloadRecord).filter(
                and_(
                    DataDownloadRecord.user_id == user_id,
                    DataDownloadRecord.status == 'running'
                )
            ).first()

            if running_task:
                return {
                    "success": False,
                    "error": "您已有数据下载任务正在运行，请等待完成后再试"
                }

            # 验证账号权限
            accounts = DataDownloadService.get_user_accounts(db, user_id, account_ids)
            if not accounts:
                return {
                    "success": False,
                    "error": "没有找到可用的已登录账号"
                }

            # 创建下载记录
            record = DataDownloadService.create_download_record(
                db, user_id, validation["start_date"], validation["end_date"],
                account_ids, data_types
            )

            # 启动异步任务
            asyncio.create_task(
                DataDownloadService._execute_download_task(
                    record.id, start_date, end_date, account_ids, data_types
                )
            )

            return {
                "success": True,
                "task_id": record.id,
                "total_files": len(account_ids) * len(data_types),
                "message": "数据下载任务已启动"
            }

        except Exception as e:
            logger.error(f"启动数据下载任务失败: {str(e)}")
            return {"success": False, "error": f"启动任务失败: {str(e)}"}
        finally:
            db.close()

    @staticmethod
    async def _execute_download_task(record_id: int, start_date: str, end_date: str,
                                   account_ids: List[int], data_types: Optional[List[str]] = None):
        """执行数据下载任务（内部方法）

        Args:
            record_id: 下载记录ID
            start_date: 开始日期
            end_date: 结束日期
            account_ids: 账号ID列表
            data_types: 数据类型列表
        """
        db = SessionLocal()
        try:
            logger.info(f"开始执行数据下载任务 {record_id}")

            # 创建下载目录
            download_dir = DataDownloadService.create_download_directory(record_id)

            DataDownloadService.update_record_status(
                db, record_id,
                download_path=download_dir,
                current_step="创建下载目录"
            )

            # 获取账号信息
            accounts = db.query(PlatformAccount).filter(
                PlatformAccount.id.in_(account_ids)
            ).all()

            completed_count = 0
            failed_files = []

            # 逐个处理账号
            for account in accounts:
                DataDownloadService.update_record_status(
                    db, record_id,
                    current_account_name=account.name,
                    current_step=f"下载账号 {account.name} 的数据"
                )

                # 根据账号平台类型自动选择数据类型
                account_data_types = DataDownloadService.get_platform_data_types(account.platform)
                if not account_data_types:
                    logger.warning(f"账号 {account.name} 的平台类型 {account.platform} 不支持数据下载")
                    continue

                # 下载该账号的所有数据类型
                result = await DataDownloadService.download_account_data(
                    account.id, account_data_types, start_date, end_date, download_dir, record_id
                )

                if result["success"]:
                    # 更新成功下载的文件数
                    completed_count += result["total_downloaded"]

                    # 记录失败的文件
                    for failed_file in result["failed_files"]:
                        failed_files.append({
                            "account_name": account.name,
                            "data_type": failed_file["data_type"],
                            "error": failed_file["error"]
                        })

                    logger.info(f"账号 {account.name} 下载完成: 成功 {result['total_downloaded']} 个，失败 {result['total_failed']} 个")
                else:
                    # 整个账号下载失败
                    for data_type in account_data_types:
                        failed_files.append({
                            "account_name": account.name,
                            "data_type": data_type,
                            "error": result.get("error", "未知错误")
                        })
                    logger.error(f"账号 {account.name} 下载失败: {result.get('error')}")

                # 更新进度
                DataDownloadService.update_record_status(
                    db, record_id, completed_files=completed_count
                )

                # 账号间添加延迟，避免请求过于频繁
                await asyncio.sleep(2)

            # 创建ZIP文件
            DataDownloadService.update_record_status(
                db, record_id, current_step="创建压缩文件"
            )

            zip_path = DataDownloadService.create_zip_file(download_dir, record_id)

            # 完成任务
            if failed_files:
                failed_info = ', '.join([f"{f['account_name']}-{f['data_type']}({f['error']})" for f in failed_files])
                error_summary = f"部分文件下载失败: {failed_info}"
                DataDownloadService.update_record_status(
                    db, record_id,
                    status='completed',
                    current_step="完成（部分失败）",
                    zip_file_path=zip_path,
                    error_message=error_summary
                )
                logger.warning(f"任务 {record_id} 完成，但有 {len(failed_files)} 个文件失败")
            else:
                DataDownloadService.update_record_status(
                    db, record_id,
                    status='completed',
                    current_step="全部完成",
                    zip_file_path=zip_path
                )
                logger.info(f"任务 {record_id} 全部完成")

            # 清理临时目录
            try:
                shutil.rmtree(download_dir)
                logger.info(f"已清理临时目录: {download_dir}")
            except Exception as e:
                logger.warning(f"清理临时目录失败: {e}")

        except Exception as e:
            logger.error(f"执行下载任务 {record_id} 时发生错误: {str(e)}")
            DataDownloadService.update_record_status(
                db, record_id,
                status='failed',
                error_message=f"任务执行失败: {str(e)}"
            )
        finally:
            db.close()

    @staticmethod
    def get_download_status(db: Session, task_id: int, user_id: int) -> Dict[str, Any]:
        """获取下载任务状态

        Args:
            db: 数据库会话
            task_id: 任务ID
            user_id: 用户ID

        Returns:
            任务状态信息
        """
        try:
            record = db.query(DataDownloadRecord).filter(
                and_(
                    DataDownloadRecord.id == task_id,
                    DataDownloadRecord.user_id == user_id
                )
            ).first()

            if not record:
                return {"success": False, "error": "任务不存在或无权限访问"}

            return {
                "success": True,
                "task_id": record.id,
                "start_date": record.start_date.isoformat(),
                "end_date": record.end_date.isoformat(),
                "selected_accounts": record.selected_accounts,
                "selected_data_types": record.selected_data_types,
                "status": record.status,
                "total_files": record.total_files,
                "completed_files": record.completed_files,
                "current_account_name": record.current_account_name,
                "current_step": record.current_step,
                "error_message": record.error_message,
                "created_at": record.created_at.isoformat(),
                "updated_at": record.updated_at.isoformat(),
                "completed_at": record.completed_at.isoformat() if record.completed_at else None,
                "progress_percent": round((record.completed_files / record.total_files) * 100, 1) if record.total_files > 0 else 0,
                "zip_file_path": record.zip_file_path
            }

        except Exception as e:
            logger.error(f"获取任务状态失败: {str(e)}")
            return {"success": False, "error": f"获取任务状态失败: {str(e)}"}

    @staticmethod
    def generate_user_source_pie_chart(excel_file_path: str, account_name: str,
                                     start_date: str, end_date: str, output_dir: str) -> Optional[str]:
        """基于用户来源明细Excel文件生成饼图

        Args:
            excel_file_path: Excel文件路径
            account_name: 账号名称
            start_date: 开始日期
            end_date: 结束日期
            output_dir: 输出目录

        Returns:
            生成的图片文件路径，失败时返回None
        """
        try:
            import pandas as pd
            import matplotlib.pyplot as plt
            import numpy as np
            from collections import defaultdict
            from app.utils.font_config import configure_chinese_fonts

            # 配置中文字体
            configure_chinese_fonts()

            # 读取Excel文件
            df = pd.read_excel(excel_file_path)

            if df.empty:
                logger.warning("Excel文件为空")
                return None

            # 检查必要的列是否存在
            required_columns = ['用户来源', '新增用户']
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                logger.error(f"Excel文件缺少必要的列: {missing_columns}")
                return None

            # 按用户来源聚合数据，排除99999999（全部统计项）
            source_data = defaultdict(int)

            for _, row in df.iterrows():
                user_source = row['用户来源']
                new_user = row['新增用户']

                # 处理数字类型
                if pd.isna(new_user):
                    new_user = 0
                else:
                    try:
                        new_user = int(float(new_user))
                    except (ValueError, TypeError):
                        new_user = 0

                # 处理用户来源字段
                if pd.isna(user_source):
                    continue

                try:
                    user_source_code = int(float(user_source))
                    # 排除99999999（全部统计项）
                    if user_source_code == 99999999:
                        continue

                    # 使用映射表转换用户来源名称
                    source_name = DataDownloadService.USER_SOURCE_MAPPING.get(user_source_code, f"未知来源({user_source_code})")
                    source_data[source_name] += new_user

                except (ValueError, TypeError):
                    # 如果不是数字，直接使用原值
                    if str(user_source).strip() and str(user_source).strip() != 'nan':
                        source_data[str(user_source).strip()] += new_user

            if not source_data:
                logger.warning("没有找到有效的用户来源数据")
                return None

            # 计算总新增用户数
            total_new_users = sum(source_data.values())

            if total_new_users == 0:
                logger.warning("总新增用户数为0，无法生成饼图")
                return None

            # 准备饼图数据
            sources = list(source_data.keys())
            values = list(source_data.values())
            percentages = [v / total_new_users * 100 for v in values]

            # 创建图形，增大尺寸为连接线和标签留出空间
            _, ax = plt.subplots(figsize=(12, 10))

            # 定义颜色方案（参考样例图片）
            colors = ['#2ECC71', '#1ABC9C', '#3498DB', '#9B59B6', '#E74C3C', '#F39C12', '#34495E', '#95A5A6', '#16A085', '#27AE60']

            # 创建饼图，不显示标签（稍后手动添加带连接线的标签）
            wedges, _, _ = ax.pie(
                values,
                labels=None,  # 不显示自动标签
                colors=colors[:len(sources)],
                autopct='',  # 不显示自动百分比
                startangle=90,
                wedgeprops=dict(width=0.3),  # 创建环形图
                pctdistance=0.85
            )

            # 智能标签布局算法 - 增强版防重叠算法
            def calculate_smart_label_positions(wedges, sources, values, percentages):
                """计算智能标签位置，彻底避免重叠"""

                # 环形图的半径配置
                outer_radius = 1.0  # 外圆半径（连接线起点）
                min_label_radius = 1.4  # 最小标签距离（增大）
                max_label_radius = 2.2  # 最大标签距离（增大）
                min_label_spacing = 0.25  # 标签间最小间距（增大）
                label_height = 0.08  # 估算标签高度

                # 收集所有楔形的信息
                wedge_info = []
                for i, (wedge, source, value, pct) in enumerate(zip(wedges, sources, values, percentages)):
                    angle = (wedge.theta2 + wedge.theta1) / 2
                    # 标准化角度到 0-360 范围
                    if angle < 0:
                        angle += 360
                    
                    wedge_info.append({
                        'index': i,
                        'angle': angle,
                        'source': source,
                        'value': value,
                        'pct': pct,
                        'wedge': wedge,
                        'size': pct,
                        'label_width': len(str(source)) * 0.01 + 0.12,  # 估算标签宽度（稍大因为用户来源名称可能较长）
                        'label_height': label_height
                    })

                def check_label_overlap(x1, y1, w1, h1, x2, y2, w2, h2, padding=0.05):
                    """检查两个矩形标签是否重叠（考虑标签的实际尺寸）"""
                    # 扩展边界框，增加padding
                    left1, right1 = x1 - w1/2 - padding, x1 + w1/2 + padding
                    top1, bottom1 = y1 + h1/2 + padding, y1 - h1/2 - padding
                    
                    left2, right2 = x2 - w2/2 - padding, x2 + w2/2 + padding
                    top2, bottom2 = y2 + h2/2 + padding, y2 - h2/2 - padding
                    
                    # 检查是否重叠
                    return not (right1 < left2 or right2 < left1 or bottom1 > top2 or bottom2 > top1)

                def find_best_position_for_label(info, existing_labels):
                    """为标签找到最佳位置，彻底避免重叠"""
                    angle = info['angle']
                    original_angle_rad = np.radians(angle)
                    
                    # 连接线起点
                    x1 = outer_radius * np.cos(original_angle_rad)
                    y1 = outer_radius * np.sin(original_angle_rad)
                    
                    best_position = None
                    min_distance_from_original = float('inf')
                    
                    # 尝试不同的位置组合
                    radius_steps = [1.4, 1.5, 1.6, 1.7, 1.8, 1.9, 2.0, 2.1, 2.2]
                    angle_offsets = [0, 10, -10, 15, -15, 20, -20, 25, -25, 30, -30, 
                                   35, -35, 40, -40, 45, -45, 50, -50, 60, -60]
                    
                    for radius in radius_steps:
                        for angle_offset in angle_offsets:
                            test_angle = angle + angle_offset
                            test_angle_rad = np.radians(test_angle)
                            
                            x_test = radius * np.cos(test_angle_rad)
                            y_test = radius * np.sin(test_angle_rad)
                            
                            # 检查是否与现有标签重叠
                            has_overlap = False
                            for existing in existing_labels:
                                if check_label_overlap(
                                    x_test, y_test, info['label_width'], info['label_height'],
                                    existing['x_label'], existing['y_label'], 
                                    existing['label_width'], existing['label_height']
                                ):
                                    has_overlap = True
                                    break
                            
                            if not has_overlap:
                                # 计算与原始位置的距离
                                original_x = min_label_radius * np.cos(original_angle_rad)
                                original_y = min_label_radius * np.sin(original_angle_rad)
                                distance = np.sqrt((x_test - original_x)**2 + (y_test - original_y)**2)
                                
                                if distance < min_distance_from_original:
                                    min_distance_from_original = distance
                                    best_position = {
                                        'x_label': x_test,
                                        'y_label': y_test,
                                        'radius': radius,
                                        'angle_offset': angle_offset
                                    }
                    
                    # 如果找不到无重叠位置，使用强制分离策略
                    if best_position is None:
                        logger.warning(f"无法为标签 {info['source']} 找到无重叠位置，使用强制分离")
                        # 使用最大半径和较大角度偏移
                        forced_angle = angle + (len(existing_labels) * 25 % 360)
                        forced_angle_rad = np.radians(forced_angle)
                        best_position = {
                            'x_label': max_label_radius * np.cos(forced_angle_rad),
                            'y_label': max_label_radius * np.sin(forced_angle_rad),
                            'radius': max_label_radius,
                            'angle_offset': forced_angle - angle
                        }
                    
                    return best_position

                # 按大小排序，大的扇形优先获得最佳位置
                wedge_info.sort(key=lambda x: -x['size'])
                
                positioned_labels = []
                
                for info in wedge_info:
                    # 为当前标签找到最佳位置
                    best_pos = find_best_position_for_label(info, positioned_labels)
                    
                    # 计算连接线起点
                    angle_rad = np.radians(info['angle'])
                    x1 = outer_radius * np.cos(angle_rad)
                    y1 = outer_radius * np.sin(angle_rad)
                    
                    # 计算连接线中间点（使用贝塞尔曲线控制点）
                    control_factor = 0.3
                    x_mid = x1 + (best_pos['x_label'] - x1) * control_factor
                    y_mid = y1 + (best_pos['y_label'] - y1) * control_factor
                    
                    # 确定文本对齐方式
                    ha = 'left' if best_pos['x_label'] > 0 else 'right'
                    
                    positioned_labels.append({
                        **info,
                        'x1': x1, 'y1': y1,  # 连接线起点
                        'x_mid': x_mid, 'y_mid': y_mid,  # 中间点
                        'x_label': best_pos['x_label'], 
                        'y_label': best_pos['y_label'],  # 标签位置
                        'ha': ha,
                        'final_radius': best_pos['radius'],
                        'angle_offset': best_pos['angle_offset']
                    })
                
                # 最后按原始索引排序返回
                positioned_labels.sort(key=lambda x: x['index'])
                
                # 输出调试信息
                logger.info("用户来源标签布局结果:")
                for label in positioned_labels:
                    logger.info(f"  {label['source']}: 半径={label['final_radius']:.2f}, "
                              f"角度偏移={label['angle_offset']:.1f}°, "
                              f"位置=({label['x_label']:.2f}, {label['y_label']:.2f})")
                
                return positioned_labels

            # 获取智能标签位置
            positioned_labels = calculate_smart_label_positions(wedges, sources, values, percentages)

            # 绘制连接线和标签
            bbox_props = dict(boxstyle="round,pad=0.4", facecolor='white', alpha=0.95, 
                            edgecolor='#999999', linewidth=0.8)

            for label_info in positioned_labels:
                # 绘制更优雅的连接线 - 使用曲线
                x1, y1 = label_info['x1'], label_info['y1']
                x_label, y_label = label_info['x_label'], label_info['y_label']
                
                # 计算控制点创建平滑曲线
                mid_x = (x1 + x_label) * 0.5
                mid_y = (y1 + y_label) * 0.5
                
                # 绘制分段连接线
                ax.plot([x1, mid_x], [y1, mid_y],
                       color='#888888', linewidth=1.5, alpha=0.8)
                ax.plot([mid_x, x_label], [mid_y, y_label],
                       color='#888888', linewidth=1.5, alpha=0.8)

                # 在连接线末端添加小圆点作为指示器
                ax.plot(x_label, y_label, 'o', color='#666666', markersize=3, alpha=0.8)

                # 添加标签 - 调整字体大小防止过于拥挤
                label_text = f"{label_info['source']}\n{label_info['value']}人\n({label_info['pct']:.1f}%)"

                # 根据标签长度调整字体大小
                font_size = 9 if len(label_info['source']) <= 6 else 8

                ax.text(x_label, y_label, label_text,
                       fontsize=font_size, ha=label_info['ha'], va='center',
                       bbox=bbox_props, zorder=10,
                       linespacing=1.2)  # 增加行间距

            # 在中心添加总人数文本
            ax.text(0, 0.05, '新增关注', ha='center', va='center', fontsize=12, color='#999999')
            ax.text(0, -0.05, f'{total_new_users}人', ha='center', va='center', fontsize=14, fontweight='bold', color='#333333')

            # 设置标题
            title = f'{account_name} 用户渠道构成\n{start_date} 至 {end_date}'
            ax.set_title(title, fontsize=16, fontweight='bold', pad=20)

            # 确保图形是圆形
            ax.axis('equal')

            # 生成文件名
            safe_account_name = "".join(c for c in account_name if c.isalnum() or c in (' ', '-', '_')).strip()
            filename = f"{safe_account_name}_用户渠道构成_{start_date}_to_{end_date}.png"
            output_path = os.path.join(output_dir, filename)

            # 保存图片
            plt.tight_layout()
            plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()

            logger.info(f"成功生成用户来源饼图: {output_path}")
            return output_path

        except Exception as e:
            logger.error(f"生成用户来源饼图失败: {str(e)}")
            return None
