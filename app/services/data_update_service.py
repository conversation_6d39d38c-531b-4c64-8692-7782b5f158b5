import asyncio
import uuid
from datetime import datetime, date, timedelta
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_, func

from app.models import (
    DataUpdateRecord, PlatformAccount, DataUpdateTaskItem,
    WeChatMPContentTrend, WeChatMPContentSource,
    WeChatMPContentDetail, WeChatMPUserChannel, WeChatMPUserSource
)
from app.services.wechat_service import WeChatMPService
from app.database import SessionLocal
from app.exceptions import LoginExpiredException
from app.services.platform_service_base import PlatformServiceFactory, TaskItemStatus
import logging

logger = logging.getLogger(__name__)


class DataUpdateService:
    """数据更新服务类"""
    
    # 需要清空的数据表
    DATA_TABLES = [
        WeChatMPContentTrend,
        WeChatMPContentSource, 
        WeChatMPContentDetail,
        WeChatMPUserChannel,
        WeChatMPUserSource
    ]
    
    # 数据类型映射
    WECHAT_MP_DATA_TYPES = ['content_trend', 'content_source', 'content_detail', 'user_channel']
    WECHAT_CHANNELS_DATA_TYPES = ['single_video']
    XIAOHONGSHU_DATA_TYPES = ['note_data']

    # 兼容性保持
    DATA_TYPES = WECHAT_MP_DATA_TYPES
    
    @staticmethod
    def validate_date_range(start_date: str, end_date: str) -> Dict[str, Any]:
        """验证日期范围
        
        Args:
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            
        Returns:
            验证结果
        """
        try:
            start = datetime.strptime(start_date, '%Y-%m-%d').date()
            end = datetime.strptime(end_date, '%Y-%m-%d').date()
            
            if start > end:
                return {"success": False, "error": "开始日期不能晚于结束日期"}
            
            if (end - start).days > 30:
                return {"success": False, "error": "日期范围不能超过30天"}
            
            return {"success": True, "start_date": start, "end_date": end}
            
        except ValueError as e:
            return {"success": False, "error": f"日期格式错误: {str(e)}"}
    
    @staticmethod
    def clear_data_tables(db: Session) -> Dict[str, Any]:
        """清空数据表
        
        Args:
            db: 数据库会话
            
        Returns:
            清空结果
        """
        try:
            cleared_counts = {}
            
            # 按依赖关系顺序清空表
            for table_class in DataUpdateService.DATA_TABLES:
                count = db.query(table_class).count()
                db.query(table_class).delete()
                cleared_counts[table_class.__tablename__] = count
                
            db.commit()
            
            return {
                "success": True, 
                "cleared_counts": cleared_counts,
                "total_cleared": sum(cleared_counts.values())
            }
            
        except Exception as e:
            db.rollback()
            logger.error(f"清空数据表失败: {str(e)}")
            return {"success": False, "error": f"清空数据表失败: {str(e)}"}
    
    @staticmethod
    def get_logged_accounts(db: Session) -> List[PlatformAccount]:
        """获取所有已登录的微信公众号账号
        
        Args:
            db: 数据库会话
            
        Returns:
            已登录的账号列表
        """
        return db.query(PlatformAccount).filter(
            and_(
                PlatformAccount.platform.in_(["wechat_mp", "wechat_service", "wechat_channels", "xiaohongshu"]),
                PlatformAccount.login_status == True
            )
        ).all()
    
    @staticmethod
    def create_update_record(db: Session, start_date: date, end_date: date, total_accounts: int) -> DataUpdateRecord:
        """创建更新记录
        
        Args:
            db: 数据库会话
            start_date: 开始日期
            end_date: 结束日期
            total_accounts: 总账号数
            
        Returns:
            更新记录对象
        """
        record = DataUpdateRecord(
            start_date=start_date,
            end_date=end_date,
            status='running',
            total_accounts=total_accounts,
            completed_accounts=0
        )
        db.add(record)
        db.commit()
        db.refresh(record)
        return record
    
    @staticmethod
    def update_record_status(db: Session, record_id: int, **kwargs) -> bool:
        """更新记录状态
        
        Args:
            db: 数据库会话
            record_id: 记录ID
            **kwargs: 要更新的字段
            
        Returns:
            更新是否成功
        """
        try:
            record = db.query(DataUpdateRecord).filter(DataUpdateRecord.id == record_id).first()
            if not record:
                return False
                
            for key, value in kwargs.items():
                if hasattr(record, key):
                    setattr(record, key, value)
            
            record.updated_at = datetime.utcnow()
            
            if kwargs.get('status') == 'completed':
                record.completed_at = datetime.utcnow()
                
            db.commit()
            return True
            
        except Exception as e:
            logger.error(f"更新记录状态失败: {str(e)}")
            db.rollback()
            return False

    @staticmethod
    def create_task_items(record_id: int, accounts: List[PlatformAccount], db: Session) -> List[int]:
        """为数据更新记录创建所有任务项"""
        task_items = []

        for account in accounts:
            # 获取平台支持的数据类型
            data_types = PlatformServiceFactory.get_platform_data_types(account.platform)

            for data_type, display_name in data_types:
                task_item = DataUpdateTaskItem(
                    update_record_id=record_id,
                    account_id=account.id,
                    account_name=account.name,
                    platform=account.platform,
                    data_type=data_type,
                    data_type_display=display_name,
                    status=TaskItemStatus.PENDING
                )
                db.add(task_item)
                task_items.append(task_item)

        db.commit()
        return [item.id for item in task_items]

    @staticmethod
    def update_task_item_status(item_id: int, status: str, error_message: str = None, db: Session = None) -> bool:
        """更新任务项状态"""
        if db is None:
            db = SessionLocal()
            should_close = True
        else:
            should_close = False

        try:
            task_item = db.query(DataUpdateTaskItem).filter(
                DataUpdateTaskItem.id == item_id
            ).first()

            if not task_item:
                return False

            task_item.status = status
            task_item.updated_at = datetime.utcnow()

            if error_message:
                task_item.error_message = error_message

            if status == TaskItemStatus.RUNNING:
                task_item.started_at = datetime.utcnow()
            elif status in [TaskItemStatus.COMPLETED, TaskItemStatus.FAILED]:
                task_item.completed_at = datetime.utcnow()

            db.commit()
            return True

        except Exception as e:
            logger.error(f"更新任务项状态失败: {str(e)}")
            db.rollback()
            return False
        finally:
            if should_close:
                db.close()

    @staticmethod
    def get_task_items(record_id: int, page: int = 1, page_size: int = 20,
                      status: Optional[str] = None) -> Dict[str, Any]:
        """获取任务明细列表"""
        db = SessionLocal()
        try:
            query = db.query(DataUpdateTaskItem).filter(
                DataUpdateTaskItem.update_record_id == record_id
            )

            if status:
                query = query.filter(DataUpdateTaskItem.status == status)

            # 总数
            total = query.count()

            # 按状态优先级排序：进行中 > 重试中 > 待处理 > 失败 > 已取消 > 已完成
            # 使用CASE WHEN进行自定义排序
            from sqlalchemy import case

            status_priority = case(
                (DataUpdateTaskItem.status == TaskItemStatus.RUNNING, 1),
                (DataUpdateTaskItem.status == TaskItemStatus.RETRYING, 2),
                (DataUpdateTaskItem.status == TaskItemStatus.PENDING, 3),
                (DataUpdateTaskItem.status == TaskItemStatus.FAILED, 4),
                (DataUpdateTaskItem.status == TaskItemStatus.CANCELLED, 5),
                (DataUpdateTaskItem.status == TaskItemStatus.COMPLETED, 6),
                else_=7
            )

            # 分页查询，按状态优先级和ID排序
            offset = (page - 1) * page_size
            items = query.order_by(status_priority, DataUpdateTaskItem.id).offset(offset).limit(page_size).all()

            # 状态统计
            status_stats = db.query(
                DataUpdateTaskItem.status,
                func.count(DataUpdateTaskItem.id)
            ).filter(
                DataUpdateTaskItem.update_record_id == record_id
            ).group_by(DataUpdateTaskItem.status).all()

            stats = {status: count for status, count in status_stats}

            return {
                "items": [
                    {
                        "id": item.id,
                        "account_name": item.account_name,
                        "platform": item.platform,
                        "data_type": item.data_type,
                        "data_type_display": item.data_type_display,
                        "status": item.status,
                        "error_message": item.error_message,
                        "started_at": item.started_at,
                        "completed_at": item.completed_at,
                        "can_retry": item.status == TaskItemStatus.FAILED
                    }
                    for item in items
                ],
                "total": total,
                "page": page,
                "page_size": page_size,
                "stats": stats
            }

        except Exception as e:
            logger.error(f"获取任务明细失败: {str(e)}")
            return {"items": [], "total": 0, "page": page, "page_size": page_size, "stats": {}}
        finally:
            db.close()

    @staticmethod
    async def process_account_data_new(account_id: int, start_date: str, end_date: str,
                                 record_id: int) -> Dict[str, Any]:
        """处理单个账号的数据更新

        Args:
            account_id: 账号ID
            start_date: 开始日期
            end_date: 结束日期
            record_id: 更新记录ID

        Returns:
            处理结果
        """
        db = SessionLocal()
        try:
            # 在当前会话中查询账号对象
            account = db.query(PlatformAccount).filter(PlatformAccount.id == account_id).first()
            if not account:
                return {"success": False, "error": f"账号ID {account_id} 不存在"}

            # 获取该账号的所有任务明细项
            task_items = db.query(DataUpdateTaskItem).filter(
                and_(
                    DataUpdateTaskItem.update_record_id == record_id,
                    DataUpdateTaskItem.account_id == account_id
                )
            ).all()

            if not task_items:
                return {"success": False, "error": f"账号 {account.name} 没有找到任务明细项"}

            # 更新状态：开始处理账号
            DataUpdateService.update_record_status(
                db, record_id,
                current_account_name=account.name,
                current_step="开始处理账号数据"
            )

            # 使用平台服务工厂创建服务实例
            try:
                service = PlatformServiceFactory.create_service(account.platform, account_id)
            except ValueError as e:
                error_msg = f"账号 {account.name} 平台类型 {account.platform} 暂不支持数据更新: {str(e)}"
                logger.error(error_msg)

                # 将所有任务项标记为失败
                for task_item in task_items:
                    DataUpdateService.update_task_item_status(
                        task_item.id, TaskItemStatus.FAILED, error_msg, db
                    )

                return {"success": False, "error": error_msg}

            # 尝试加载已保存的登录状态
            if not await service.load_login_state():
                error_msg = f"账号 {account.name} 登录状态恢复失败，请重新登录"
                logger.error(error_msg)

                # 更新数据库中的登录状态为失效
                try:
                    account.login_status = False
                    db.commit()
                    logger.info(f"已更新账号 {account.name} 登录状态为失效")
                except Exception as db_e:
                    logger.warning(f"更新账号 {account.name} 登录状态失败: {db_e}")
                    db.rollback()

                # 将所有任务项标记为失败
                for task_item in task_items:
                    DataUpdateService.update_task_item_status(
                        task_item.id, TaskItemStatus.FAILED, error_msg, db
                    )

                # 释放浏览器资源
                try:
                    if hasattr(service, 'close'):
                        await service.close()
                        logger.info(f"账号 {account.name} 浏览器资源已关闭")
                except Exception as close_e:
                    logger.warning(f"账号 {account.name} 关闭浏览器资源时出错: {close_e}")

                return {"success": False, "error": error_msg, "login_expired": True}

            # 逐个处理任务明细项
            successful_items = 0
            failed_items = 0

            for task_item in task_items:
                try:
                    logger.info(f"开始处理任务项: {account.name} - {task_item.data_type_display}")

                    # 更新任务项状态为运行中
                    DataUpdateService.update_task_item_status(
                        task_item.id, TaskItemStatus.RUNNING, db=db
                    )

                    # 更新记录状态
                    DataUpdateService.update_record_status(
                        db, record_id,
                        current_step=f"处理 {task_item.data_type_display}"
                    )

                    # 调用统一的下载接口
                    result = await service.download_single_data_type(
                        data_type=task_item.data_type,
                        start_date=start_date,
                        end_date=end_date,
                        auto_import=True
                    )

                    # 更新任务项状态
                    if result.success:
                        DataUpdateService.update_task_item_status(
                            task_item.id, TaskItemStatus.COMPLETED, db=db
                        )
                        successful_items += 1
                        logger.info(f"任务项完成: {account.name} - {task_item.data_type_display}")
                    else:
                        DataUpdateService.update_task_item_status(
                            task_item.id, TaskItemStatus.FAILED, result.error_message, db
                        )
                        failed_items += 1
                        logger.error(f"任务项失败: {account.name} - {task_item.data_type_display}: {result.error_message}")

                except LoginExpiredException as e:
                    # 登录过期，立即返回，不再继续后续操作
                    error_msg = f"账号 {account.name} 登录状态已过期: {e.message}"
                    logger.error(error_msg)

                    # 将当前和剩余的任务项都标记为失败
                    remaining_items = [item for item in task_items if item.status == TaskItemStatus.PENDING]
                    for item in remaining_items:
                        DataUpdateService.update_task_item_status(
                            item.id, TaskItemStatus.FAILED, error_msg, db
                        )

                    # 更新数据库中的登录状态
                    try:
                        account.login_status = False
                        db.commit()
                        logger.info(f"已更新账号 {account.name} 登录状态为失效")
                    except Exception as db_e:
                        logger.warning(f"更新账号 {account.name} 登录状态失败: {db_e}")
                        db.rollback()

                    return {"success": False, "error": error_msg, "login_expired": True}

                except Exception as e:
                    # 其他异常，标记当前任务项为失败，继续处理下一个
                    error_msg = f"处理任务项异常: {str(e)}"
                    DataUpdateService.update_task_item_status(
                        task_item.id, TaskItemStatus.FAILED, error_msg, db
                    )
                    failed_items += 1
                    logger.error(f"任务项异常: {account.name} - {task_item.data_type_display}: {error_msg}")

            # 关闭服务资源
            try:
                if hasattr(service, 'close'):
                    await service.close()
                    logger.info(f"账号 {account.name} 浏览器资源已关闭")
            except Exception as close_e:
                logger.warning(f"账号 {account.name} 关闭浏览器资源时出错: {close_e}")

            # 返回处理结果
            if successful_items > 0:
                return {
                    "success": True,
                    "account_name": account.name,
                    "successful_items": successful_items,
                    "failed_items": failed_items,
                    "total_items": len(task_items)
                }
            else:
                return {
                    "success": False,
                    "error": f"账号 {account.name} 所有任务项都失败了",
                    "failed_items": failed_items,
                    "total_items": len(task_items)
                }

        except Exception as e:
            error_msg = f"处理账号时发生错误: {str(e)}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
        finally:
            db.close()
    @staticmethod
    async def process_account_data(account_id: int, start_date: str, end_date: str,
                                 record_id: int) -> Dict[str, Any]:
        """处理单个账号的数据更新

        Args:
            account_id: 账号ID
            start_date: 开始日期
            end_date: 结束日期
            record_id: 更新记录ID

        Returns:
            处理结果
        """
        db = SessionLocal()
        try:
            # 在当前会话中查询账号对象
            account = db.query(PlatformAccount).filter(PlatformAccount.id == account_id).first()
            if not account:
                return {"success": False, "error": f"账号ID {account_id} 不存在"}

            # 获取该账号的所有任务明细项
            task_items = db.query(DataUpdateTaskItem).filter(
                and_(
                    DataUpdateTaskItem.update_record_id == record_id,
                    DataUpdateTaskItem.account_id == account_id
                )
            ).all()

            if not task_items:
                return {"success": False, "error": f"账号 {account.name} 没有找到任务明细项"}

            # 更新状态：开始处理账号
            DataUpdateService.update_record_status(
                db, record_id,
                current_account_name=account.name,
                current_step="开始处理账号数据"
            )

            # 使用平台服务工厂创建服务实例
            try:
                service = PlatformServiceFactory.create_service(account.platform, account_id)
            except ValueError as e:
                error_msg = f"账号 {account.name} 平台类型 {account.platform} 暂不支持数据更新: {str(e)}"
                logger.error(error_msg)

                # 将所有任务项标记为失败
                for task_item in task_items:
                    DataUpdateService.update_task_item_status(
                        task_item.id, TaskItemStatus.FAILED, error_msg, db
                    )

                return {"success": False, "error": error_msg}

            # 尝试加载已保存的登录状态
            if not await service.load_login_state():
                error_msg = f"账号 {account.name} 登录状态恢复失败，请重新登录"
                logger.error(error_msg)

                # 更新数据库中的登录状态为失效
                try:
                    account.login_status = False
                    db.commit()
                    logger.info(f"已更新账号 {account.name} 登录状态为失效")
                except Exception as db_e:
                    logger.warning(f"更新账号 {account.name} 登录状态失败: {db_e}")
                    db.rollback()

                # 将所有任务项标记为失败
                for task_item in task_items:
                    DataUpdateService.update_task_item_status(
                        task_item.id, TaskItemStatus.FAILED, error_msg, db
                    )

                # 释放浏览器资源
                try:
                    if hasattr(service, 'close'):
                        await service.close()
                        logger.info(f"账号 {account.name} 浏览器资源已关闭")
                except Exception as close_e:
                    logger.warning(f"账号 {account.name} 关闭浏览器资源时出错: {close_e}")

                return {"success": False, "error": error_msg, "login_expired": True}

            # 逐个处理任务明细项
            successful_items = 0
            failed_items = 0

            for task_item in task_items:
                try:
                    logger.info(f"开始处理任务项: {account.name} - {task_item.data_type_display}")

                    # 更新任务项状态为运行中
                    DataUpdateService.update_task_item_status(
                        task_item.id, TaskItemStatus.RUNNING, db=db
                    )

                    # 更新记录状态
                    DataUpdateService.update_record_status(
                        db, record_id,
                        current_step=f"处理 {task_item.data_type_display}"
                    )

                    # 调用统一的下载接口
                    result = await service.download_single_data_type(
                        data_type=task_item.data_type,
                        start_date=start_date,
                        end_date=end_date,
                        auto_import=True
                    )

                    # 更新任务项状态
                    if result.success:
                        DataUpdateService.update_task_item_status(
                            task_item.id, TaskItemStatus.COMPLETED, db=db
                        )
                        successful_items += 1
                        logger.info(f"任务项完成: {account.name} - {task_item.data_type_display}")
                    else:
                        DataUpdateService.update_task_item_status(
                            task_item.id, TaskItemStatus.FAILED, result.error_message, db
                        )
                        failed_items += 1
                        logger.error(f"任务项失败: {account.name} - {task_item.data_type_display}: {result.error_message}")

                except LoginExpiredException as e:
                    # 登录过期，立即返回，不再继续后续操作
                    error_msg = f"账号 {account.name} 登录状态已过期: {e.message}"
                    logger.error(error_msg)

                    # 将当前和剩余的任务项都标记为失败
                    remaining_items = [item for item in task_items if item.status == TaskItemStatus.PENDING]
                    for item in remaining_items:
                        DataUpdateService.update_task_item_status(
                            item.id, TaskItemStatus.FAILED, error_msg, db
                        )

                    # 更新数据库中的登录状态
                    try:
                        account.login_status = False
                        db.commit()
                        logger.info(f"已更新账号 {account.name} 登录状态为失效")
                    except Exception as db_e:
                        logger.warning(f"更新账号 {account.name} 登录状态失败: {db_e}")
                        db.rollback()

                    return {"success": False, "error": error_msg, "login_expired": True}

                except Exception as e:
                    # 其他异常，标记当前任务项为失败，继续处理下一个
                    error_msg = f"处理任务项异常: {str(e)}"
                    DataUpdateService.update_task_item_status(
                        task_item.id, TaskItemStatus.FAILED, error_msg, db
                    )
                    failed_items += 1
                    logger.error(f"任务项异常: {account.name} - {task_item.data_type_display}: {error_msg}")

            # 关闭服务资源
            try:
                if hasattr(service, 'close'):
                    await service.close()
                    logger.info(f"账号 {account.name} 浏览器资源已关闭")
            except Exception as close_e:
                logger.warning(f"账号 {account.name} 关闭浏览器资源时出错: {close_e}")

            # 返回处理结果
            if successful_items > 0:
                return {
                    "success": True,
                    "account_name": account.name,
                    "successful_items": successful_items,
                    "failed_items": failed_items,
                    "total_items": len(task_items)
                }
            else:
                return {
                    "success": False,
                    "error": f"账号 {account.name} 所有任务项都失败了",
                    "failed_items": failed_items,
                    "total_items": len(task_items)
                }

        except Exception as e:
            error_msg = f"处理账号时发生错误: {str(e)}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
        finally:
            db.close()

    @staticmethod
    def get_current_data_range(db: Session) -> Dict[str, Any]:
        """获取当前数据范围（基于最新的更新记录）

        Args:
            db: 数据库会话

        Returns:
            数据范围信息
        """
        try:
            # 获取最新的已完成更新记录
            latest_record = db.query(DataUpdateRecord).filter(
                DataUpdateRecord.status.in_(['completed', 'failed'])
            ).order_by(DataUpdateRecord.created_at.desc()).first()

            if latest_record:
                # 计算总记录数
                total_records = 0
                for table_class in DataUpdateService.DATA_TABLES:
                    count = db.query(table_class).count()
                    total_records += count

                return {
                    "success": True,
                    "min_date": latest_record.start_date.isoformat(),
                    "max_date": latest_record.end_date.isoformat(),
                    "total_records": total_records,
                    "last_update_time": latest_record.completed_at.isoformat() if latest_record.completed_at else latest_record.updated_at.isoformat(),
                    "last_update_status": latest_record.status
                }
            else:
                # 没有更新记录，返回空数据范围
                return {
                    "success": True,
                    "min_date": None,
                    "max_date": None,
                    "total_records": 0,
                    "last_update_time": None,
                    "last_update_status": None
                }

        except Exception as e:
            logger.error(f"获取数据范围失败: {str(e)}")
            return {"success": False, "error": f"获取数据范围失败: {str(e)}"}

    @staticmethod
    async def start_data_update(start_date: str, end_date: str, skip_clear_data: bool = False) -> Dict[str, Any]:
        """启动数据更新任务

        Args:
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            skip_clear_data: 是否跳过清空数据步骤

        Returns:
            任务启动结果，包含任务ID
        """
        # 验证日期范围
        date_validation = DataUpdateService.validate_date_range(start_date, end_date)
        if not date_validation["success"]:
            return date_validation

        db = SessionLocal()
        try:
            # 检查是否有正在运行的任务
            running_task = db.query(DataUpdateRecord).filter(
                DataUpdateRecord.status == 'running'
            ).first()

            if running_task:
                return {
                    "success": False,
                    "error": "已有数据更新任务正在运行，请等待完成后再试"
                }

            # 获取已登录账号
            accounts = DataUpdateService.get_logged_accounts(db)
            if not accounts:
                return {
                    "success": False,
                    "error": "没有找到已登录的微信公众号账号"
                }

            # 创建更新记录
            record = DataUpdateService.create_update_record(
                db,
                date_validation["start_date"],
                date_validation["end_date"],
                len(accounts)
            )

            # 创建任务明细项
            task_item_ids = DataUpdateService.create_task_items(record.id, accounts, db)
            logger.info(f"为更新记录 {record.id} 创建了 {len(task_item_ids)} 个任务项")

            # 启动异步任务
            # 传递账号ID列表而不是账号对象，避免跨会话问题
            account_ids = [account.id for account in accounts]
            asyncio.create_task(
                DataUpdateService._execute_update_task(
                    record.id, start_date, end_date, account_ids, skip_clear_data
                )
            )

            return {
                "success": True,
                "task_id": record.id,
                "total_accounts": len(accounts),
                "message": "数据更新任务已启动"
            }

        except Exception as e:
            logger.error(f"启动数据更新任务失败: {str(e)}")
            return {"success": False, "error": f"启动任务失败: {str(e)}"}
        finally:
            db.close()

    @staticmethod
    async def _execute_update_task(record_id: int, start_date: str, end_date: str,
                                 account_ids: List[int], skip_clear_data: bool = False):
        """执行数据更新任务（内部方法）

        Args:
            record_id: 更新记录ID
            start_date: 开始日期
            end_date: 结束日期
            account_ids: 账号ID列表
            skip_clear_data: 是否跳过清空数据步骤
        """
        db = SessionLocal()
        try:
            logger.info(f"开始执行数据更新任务 {record_id}")

            # 重新查询账号对象，确保在当前会话中
            accounts = db.query(PlatformAccount).filter(
                PlatformAccount.id.in_(account_ids)
            ).all()

            # 1. 清空数据表（可选）
            if not skip_clear_data:
                DataUpdateService.update_record_status(
                    db, record_id, current_step="清空现有数据"
                )

                clear_result = DataUpdateService.clear_data_tables(db)
                if not clear_result["success"]:
                    DataUpdateService.update_record_status(
                        db, record_id,
                        status='failed',
                        error_message=f"清空数据失败: {clear_result['error']}"
                    )
                    return

                logger.info(f"已清空 {clear_result['total_cleared']} 条数据")
            else:
                logger.info("跳过清空数据步骤")

            # 2. 逐个处理账号
            completed_count = 0
            failed_accounts = []

            for i, account in enumerate(accounts, 1):
                logger.info(f"处理账号 {i}/{len(accounts)}: {account.name}")

                # 处理账号数据（注意会话隔离）
                result = await DataUpdateService.process_account_data(
                    account.id, start_date, end_date, record_id
                )

                if result["success"]:
                    completed_count += 1
                    logger.info(f"账号 {account.name} 处理成功")
                else:
                    failed_accounts.append({
                        "name": account.name,
                        "error": result.get("error", "未知错误")
                    })
                    logger.error(f"账号 {account.name} 处理失败: {result.get('error')}")

                # 更新进度
                DataUpdateService.update_record_status(
                    db, record_id, completed_accounts=completed_count
                )

                # 账号间添加延迟，避免请求过于频繁
                if i < len(accounts):
                    await asyncio.sleep(2)

            # 3. 完成任务
            if failed_accounts:
                failed_info = ', '.join([f"{acc['name']}({acc['error']})" for acc in failed_accounts])
                error_summary = f"部分账号处理失败: {failed_info}"
                DataUpdateService.update_record_status(
                    db, record_id,
                    status='completed',
                    current_step="完成（部分失败）",
                    error_message=error_summary
                )
                logger.warning(f"任务 {record_id} 完成，但有 {len(failed_accounts)} 个账号失败")
            else:
                DataUpdateService.update_record_status(
                    db, record_id,
                    status='completed',
                    current_step="全部完成"
                )
                logger.info(f"任务 {record_id} 全部完成")

        except Exception as e:
            logger.error(f"执行更新任务 {record_id} 时发生错误: {str(e)}")
            DataUpdateService.update_record_status(
                db, record_id,
                status='failed',
                error_message=f"任务执行失败: {str(e)}"
            )
        finally:
            db.close()

    @staticmethod
    def get_update_status(db: Session, task_id: int) -> Dict[str, Any]:
        """获取更新任务状态

        Args:
            db: 数据库会话
            task_id: 任务ID

        Returns:
            任务状态信息
        """
        try:
            record = db.query(DataUpdateRecord).filter(DataUpdateRecord.id == task_id).first()
            if not record:
                return {"success": False, "error": "任务不存在"}

            # 计算实际进度：基于已完成的任务项（包括成功和失败）
            total_items = db.query(DataUpdateTaskItem).filter(
                DataUpdateTaskItem.update_record_id == task_id
            ).count()

            completed_items = db.query(DataUpdateTaskItem).filter(
                DataUpdateTaskItem.update_record_id == task_id,
                DataUpdateTaskItem.status.in_([TaskItemStatus.COMPLETED, TaskItemStatus.FAILED])
            ).count()

            # 计算进度百分比
            progress_percent = round((completed_items / total_items) * 100, 1) if total_items > 0 else 0

            return {
                "success": True,
                "task_id": record.id,
                "start_date": record.start_date.isoformat(),
                "end_date": record.end_date.isoformat(),
                "status": record.status,
                "total_accounts": record.total_accounts,
                "completed_accounts": record.completed_accounts,
                "current_account_name": record.current_account_name,
                "current_step": record.current_step,
                "error_message": record.error_message,
                "created_at": record.created_at.isoformat(),
                "updated_at": record.updated_at.isoformat(),
                "completed_at": record.completed_at.isoformat() if record.completed_at else None,
                "progress_percent": progress_percent
            }

        except Exception as e:
            logger.error(f"获取任务状态失败: {str(e)}")
            return {"success": False, "error": f"获取任务状态失败: {str(e)}"}

    @staticmethod
    async def retry_task_item(item_id: int, user_id: int) -> Dict[str, Any]:
        """重试单个任务项"""
        db = SessionLocal()
        try:
            # 1. 获取任务项信息
            task_item = db.query(DataUpdateTaskItem).filter(
                DataUpdateTaskItem.id == item_id
            ).first()

            if not task_item:
                return {"success": False, "error": "任务项不存在"}

            # 2. 验证权限（检查任务是否属于当前用户）
            update_record = db.query(DataUpdateRecord).filter(
                DataUpdateRecord.id == task_item.update_record_id
            ).first()

            if not update_record:
                return {"success": False, "error": "更新记录不存在"}

            # 3. 检查任务状态（只能重试失败的任务）
            if task_item.status not in [TaskItemStatus.FAILED]:
                return {"success": False, "error": "只能重试失败的任务"}

            # 4. 更新状态为重试中
            task_item.status = TaskItemStatus.RETRYING
            task_item.started_at = datetime.utcnow()
            task_item.error_message = None
            db.commit()

            logger.info(f"开始重试任务项 {item_id}: {task_item.account_name} - {task_item.data_type_display}")

            # 5. 创建平台服务实例
            service = PlatformServiceFactory.create_service(
                task_item.platform,
                task_item.account_id
            )

            # 6. 加载登录状态
            if not await service.load_login_state():
                task_item.status = TaskItemStatus.FAILED
                task_item.error_message = "登录状态无效，请重新登录"
                task_item.completed_at = datetime.utcnow()
                db.commit()
                await service.close()
                return {"success": False, "error": "登录状态无效"}

            # 7. 执行数据下载
            result = await service.download_single_data_type(
                data_type=task_item.data_type,
                start_date=update_record.start_date.strftime('%Y-%m-%d'),
                end_date=update_record.end_date.strftime('%Y-%m-%d'),
                auto_import=True
            )

            # 8. 更新任务状态
            task_item.status = TaskItemStatus.COMPLETED if result.success else TaskItemStatus.FAILED
            task_item.error_message = result.error_message
            task_item.completed_at = datetime.utcnow()
            db.commit()

            # 9. 关闭服务
            await service.close()

            logger.info(f"任务项重试完成 {item_id}: {'成功' if result.success else '失败'}")

            return {
                "success": result.success,
                "error": result.error_message,
                "task_item_id": item_id,
                "account_name": task_item.account_name,
                "data_type_display": task_item.data_type_display
            }

        except Exception as e:
            # 更新任务状态为失败
            if 'task_item' in locals():
                task_item.status = TaskItemStatus.FAILED
                task_item.error_message = f"重试异常: {str(e)}"
                task_item.completed_at = datetime.utcnow()
                db.commit()

            logger.error(f"重试任务项失败 {item_id}: {str(e)}")
            return {"success": False, "error": f"重试失败: {str(e)}"}
        finally:
            db.close()

    @staticmethod
    async def stop_data_update_task(record_id: int) -> Dict[str, Any]:
        """停止正在运行的数据更新任务"""
        db = SessionLocal()
        try:
            # 查找任务记录
            record = db.query(DataUpdateRecord).filter(DataUpdateRecord.id == record_id).first()
            if not record:
                return {"success": False, "error": f"任务记录 {record_id} 不存在"}

            # 检查任务状态
            if record.status != 'running':
                return {"success": False, "error": f"任务状态为 {record.status}，无法停止"}

            # 更新任务状态为已取消
            record.status = 'cancelled'
            record.updated_at = datetime.utcnow()
            record.completed_at = datetime.utcnow()

            # 将所有运行中和待处理的任务项标记为已取消
            task_items = db.query(DataUpdateTaskItem).filter(
                and_(
                    DataUpdateTaskItem.update_record_id == record_id,
                    DataUpdateTaskItem.status.in_([TaskItemStatus.PENDING, TaskItemStatus.RUNNING, TaskItemStatus.RETRYING])
                )
            ).all()

            for item in task_items:
                item.status = TaskItemStatus.CANCELLED
                item.error_message = "任务被用户手动停止"
                item.completed_at = datetime.utcnow()

            db.commit()
            logger.info(f"任务 {record_id} 已被停止，共取消 {len(task_items)} 个任务项")

            return {
                "success": True,
                "message": f"任务已停止，共取消 {len(task_items)} 个任务项"
            }

        except Exception as e:
            db.rollback()
            logger.error(f"停止任务失败 {record_id}: {str(e)}")
            return {"success": False, "error": f"停止任务失败: {str(e)}"}
        finally:
            db.close()

    @staticmethod
    async def delete_data_update_task(record_id: int) -> Dict[str, Any]:
        """删除数据更新任务记录"""
        db = SessionLocal()
        try:
            # 查找任务记录
            record = db.query(DataUpdateRecord).filter(DataUpdateRecord.id == record_id).first()
            if not record:
                return {"success": False, "error": f"任务记录 {record_id} 不存在"}

            # 检查任务状态，运行中的任务不能删除
            if record.status == 'running':
                return {"success": False, "error": "正在运行的任务不能删除，请先停止任务"}

            # 删除相关的任务明细项
            task_items_count = db.query(DataUpdateTaskItem).filter(
                DataUpdateTaskItem.update_record_id == record_id
            ).count()

            db.query(DataUpdateTaskItem).filter(
                DataUpdateTaskItem.update_record_id == record_id
            ).delete()

            # 删除任务记录
            db.delete(record)
            db.commit()

            logger.info(f"任务记录 {record_id} 及其 {task_items_count} 个任务项已被删除")

            return {
                "success": True,
                "message": f"任务记录已删除，包含 {task_items_count} 个任务项"
            }

        except Exception as e:
            db.rollback()
            logger.error(f"删除任务失败 {record_id}: {str(e)}")
            return {"success": False, "error": f"删除任务失败: {str(e)}"}
        finally:
            db.close()
