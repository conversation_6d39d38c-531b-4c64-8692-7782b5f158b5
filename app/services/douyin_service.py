"""
抖音创作者平台服务
"""
import asyncio
import json
import logging
import os
import re
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from playwright.async_api import Page, Browser, BrowserContext
from app.services.platform_service_base import PlatformServiceBase
from app.services.browser_manager import BrowserManager
from app.database import get_db
from app.models import PlatformAccount
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)


class DouyinService(PlatformServiceBase):
    """抖音创作者平台服务类"""
    
    def __init__(self, account_id: int, headless: bool = True):
        super().__init__(account_id)
        self.platform = "douyin"
        self.base_url = "https://creator.douyin.com/"
        self.login_url = "https://creator.douyin.com/"

        # 浏览器相关属性
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        self.playwright = None
        self.headless = headless
        self.user_data_dir = self._get_user_data_dir()

        # 数据类型映射
        self.DATA_TYPES = {
            'video_data': '视频数据',
            'account_overview': '账号概览',
            'fans_data': '粉丝数据'
        }

    def _get_user_data_dir(self) -> str:
        """获取用户数据目录"""
        if self.account_id:
            return f"user_data/douyin_account_{self.account_id}"
        return "user_data/douyin_temp"

    async def _init_browser(self):
        """保持兼容的空实现：已改为使用全局 BrowserManager"""
        return

    async def _create_persistent_context(self):
        """创建持久化上下文（通过全局 BrowserManager）"""
        from app.services.browser_manager import browser_manager
        return await browser_manager.create_context(
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
            viewport={"width": 1920, "height": 1080},
            accept_downloads=True,
        )

    async def _start_browser(self):
        """启动浏览器并创建页面"""
        try:
            # 初始化浏览器
            await self._init_browser()

            # 创建上下文和页面
            if not self.context:
                self.context = await self._create_persistent_context()

            if not self.page:
                from app.services.browser_manager import browser_manager
                self.page = await browser_manager.open_page(self.context)

        except Exception as e:
            logger.error(f"启动浏览器失败: {e}")
            raise
    
    async def get_login_qr_code(self) -> Optional[str]:
        """获取登录二维码"""
        try:
            print("正在获取抖音登录二维码...")
            
            # 启动浏览器
            await self._start_browser()
            
            # 访问登录页面
            await self.page.goto(self.login_url, wait_until='networkidle')
            print(f"已访问登录页面: {self.login_url}")
            
            # 等待二维码容器加载
            await self.page.wait_for_selector('#douyin_login_comp_scan_code', timeout=10000)
            print("二维码容器已加载")
            
            # 查找二维码图片
            qr_container = await self.page.query_selector('#douyin_login_comp_scan_code')
            if not qr_container:
                print("未找到二维码容器")
                return None
            
            # 查找动画二维码容器
            animate_container = await qr_container.query_selector('#animate_qrcode_container')
            if not animate_container:
                print("未找到动画二维码容器")
                return None
            
            # 查找二维码div（qrcode-[随机字母数字]）
            qr_divs = await animate_container.query_selector_all('div[class*="qrcode-"]')
            if not qr_divs:
                print("未找到二维码div")
                return None
            
            # 在第一个匹配的div中查找img标签
            qr_img = await qr_divs[0].query_selector('img')
            if not qr_img:
                print("未找到二维码图片")
                return None
            
            # 获取二维码图片的src
            qr_src = await qr_img.get_attribute('src')
            if not qr_src:
                print("二维码图片src为空")
                return None
            
            print(f"成功获取二维码: {qr_src[:100]}...")
            return qr_src
            
        except Exception as e:
            print(f"获取抖音登录二维码失败: {e}")
            return None
    
    async def check_login_status(self) -> bool:
        """检查登录状态"""
        try:
            if not self.page:
                return False
            
            # 检查当前URL是否已跳转到创作者平台主页
            current_url = self.page.url
            print(f"当前URL: {current_url}")
            
            # 如果URL包含创作者平台的特征，说明登录成功
            if 'creator.douyin.com' in current_url and ('home' in current_url or 'dashboard' in current_url):
                print("✅ 检测到抖音登录成功")
                return True
            
            # 检查是否存在用户信息元素
            try:
                user_info = await self.page.query_selector('.user-info, .avatar, [class*="user"], [class*="avatar"]', timeout=2000)
                if user_info:
                    print("✅ 检测到用户信息元素，登录成功")
                    return True
            except:
                pass
            
            return False
            
        except Exception as e:
            print(f"检查抖音登录状态失败: {e}")
            return False
    
    async def save_login_state(self) -> bool:
        """保存登录状态"""
        try:
            if not self.context:
                return False
            
            # 获取cookies
            cookies = await self.context.cookies()
            
            # 保存到数据库
            db = next(get_db())
            try:
                account = db.query(PlatformAccount).filter(PlatformAccount.id == self.account_id).first()
                if account:
                    account.cookies = json.dumps(cookies)
                    account.login_status = True
                    account.last_login_time = datetime.utcnow()
                    db.commit()
                    print("✅ 抖音登录状态已保存到数据库")
                    return True
            finally:
                db.close()
            
            return False
            
        except Exception as e:
            print(f"保存抖音登录状态失败: {e}")
            return False
    
    async def load_login_state(self) -> bool:
        """加载登录状态"""
        try:
            db = next(get_db())
            try:
                account = db.query(PlatformAccount).filter(PlatformAccount.id == self.account_id).first()
                if not account or not account.cookies:
                    return False
                
                cookies = json.loads(account.cookies)
                
                # 启动浏览器
                await self._start_browser()
                
                # 添加cookies
                await self.context.add_cookies(cookies)
                
                # 访问主页验证登录状态
                await self.page.goto(self.base_url, wait_until='networkidle')
                
                # 检查是否登录成功
                if await self.check_login_status():
                    print("✅ 抖音登录状态加载成功")
                    return True
                else:
                    print("❌ 抖音登录状态已过期")
                    # 清除过期的登录状态
                    account.login_status = False
                    account.cookies = None
                    db.commit()
                    return False
                    
            finally:
                db.close()
                
        except Exception as e:
            print(f"加载抖音登录状态失败: {e}")
            return False
    
    async def logout(self) -> bool:
        """注销登录"""
        try:
            # 清除数据库中的登录状态
            db = next(get_db())
            try:
                account = db.query(PlatformAccount).filter(PlatformAccount.id == self.account_id).first()
                if account:
                    account.login_status = False
                    account.cookies = None
                    account.last_login_time = None
                    db.commit()
                    print("✅ 抖音账号已注销")
                    return True
            finally:
                db.close()
            
            return False
            
        except Exception as e:
            print(f"注销抖音账号失败: {e}")
            return False
    
    async def download_single_data_type(self, data_type: str, start_date: str, end_date: str, auto_import: bool = True) -> Optional[dict]:
        """下载单个数据类型的数据"""
        try:
            print(f"开始下载抖音 {data_type} 数据: {start_date} 到 {end_date}")
            
            if data_type == 'video_data':
                return await self.download_video_data(start_date, end_date, auto_import)
            elif data_type == 'account_overview':
                return await self.download_account_overview_data(auto_import)
            elif data_type == 'fans_data':
                return await self.download_fans_data(auto_import)
            else:
                print(f"不支持的数据类型: {data_type}")
                return None
                
        except Exception as e:
            print(f"下载抖音 {data_type} 数据失败: {e}")
            return None
    
    async def download_video_data(self, start_date: str, end_date: str, auto_import: bool = True) -> Optional[dict]:
        """下载视频数据"""
        try:
            print(f"开始下载抖音视频数据: {start_date} 到 {end_date}")
            
            # 加载登录状态
            if not await self.load_login_state():
                print("❌ 抖音未登录，无法下载数据")
                return None
            
            # TODO: 实现具体的视频数据下载逻辑
            print("⚠️ 抖音视频数据下载功能待实现")
            return {"message": "抖音视频数据下载功能待实现"}
            
        except Exception as e:
            print(f"下载抖音视频数据失败: {e}")
            return None
    
    async def download_account_overview_data(self, auto_import: bool = True) -> Optional[dict]:
        """下载账号概览数据"""
        try:
            print("开始下载抖音账号概览数据")
            
            # 加载登录状态
            if not await self.load_login_state():
                print("❌ 抖音未登录，无法下载数据")
                return None
            
            # TODO: 实现具体的账号概览数据下载逻辑
            print("⚠️ 抖音账号概览数据下载功能待实现")
            return {"message": "抖音账号概览数据下载功能待实现"}
            
        except Exception as e:
            print(f"下载抖音账号概览数据失败: {e}")
            return None
    
    async def download_fans_data(self, auto_import: bool = True) -> Optional[dict]:
        """下载粉丝数据"""
        try:
            print("开始下载抖音粉丝数据")
            
            # 加载登录状态
            if not await self.load_login_state():
                print("❌ 抖音未登录，无法下载数据")
                return None
            
            # TODO: 实现具体的粉丝数据下载逻辑
            print("⚠️ 抖音粉丝数据下载功能待实现")
            return {"message": "抖音粉丝数据下载功能待实现"}
            
        except Exception as e:
            print(f"下载抖音粉丝数据失败: {e}")
            return None

    async def close(self):
        """关闭浏览器和相关资源"""
        try:
            if self.page:
                await self.page.close()
                self.page = None

            if self.context:
                from app.services.browser_manager import browser_manager
                await browser_manager.close_context(self.context)
                self.context = None

        except Exception as e:
            print(f"关闭抖音服务资源失败: {e}")
    
    def get_supported_data_types(self) -> List[tuple]:
        """获取支持的数据类型列表"""
        return [
            ('video_data', '视频数据'),
            ('account_overview', '账号概览'),
            ('fans_data', '粉丝数据')
        ]

    async def get_login_qrcode(self) -> str:
        """获取登录二维码"""
        try:
            await self._start_browser()

            # 导航到抖音创作者平台
            await self.page.goto("https://creator.douyin.com/", wait_until="networkidle")

            # 等待登录组件加载
            await self.page.wait_for_selector("#douyin_login_comp_scan_code", timeout=10000)

            # 查找二维码容器
            qr_container = await self.page.query_selector("#douyin_login_comp_scan_code")
            if not qr_container:
                raise Exception("未找到登录二维码容器")

            # 查找动画二维码容器
            animate_container = await qr_container.query_selector("#animate_qrcode_container")
            if not animate_container:
                raise Exception("未找到动画二维码容器")

            # 查找二维码div（qrcode-[随机字母数字]）
            qr_divs = await animate_container.query_selector_all('div[class*="qrcode-"]')
            if not qr_divs:
                raise Exception("未找到二维码div")

            # 在第一个匹配的div中查找img标签
            qr_img = await qr_divs[0].query_selector('img')
            if not qr_img:
                raise Exception("未找到二维码图片")

            # 获取二维码图片的src属性
            qr_src = await qr_img.get_attribute('src')
            if not qr_src:
                raise Exception("二维码图片没有src属性")

            return qr_src

        except Exception as e:
            logger.error(f"获取抖音登录二维码失败: {e}")
            raise Exception(f"获取登录二维码失败: {e}")
        finally:
            # 不关闭浏览器，保持登录状态检查
            pass

    async def close(self):
        """关闭服务，释放资源"""
        await super().close()
