"""
登录状态维持服务
"""
import asyncio
import random
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
from sqlalchemy.orm import Session

from app.database import SessionLocal
from app.models import PlatformAccount, LoginKeeperRecord
from app.services.wechat_service import WeChatMPService
from app.services.xiaohongshu_service import XiaohongshuService
from app.services.wechat_channels_service import WeChatChannelsService
from app.config.keeper_config import (
    get_config, is_keeper_enabled
)
from app.services.platform_pages_config import PlatformPagesConfig

# 配置日志
logger = logging.getLogger(__name__)

class LoginKeeperService:
    """登录状态维持服务"""
    
    def __init__(self):
        self.config = get_config()
        self.is_running = False
        self.last_run_time = None
        self.stats = {
            "total_runs": 0,
            "successful_maintains": 0,
            "failed_maintains": 0,
            "accounts_processed": 0
        }
    
    async def maintain_all_login_states(self) -> Dict:
        """维持所有已登录账号的登录状态"""
        if not is_keeper_enabled():
            logger.info("登录状态维持服务已禁用")
            return {"success": False, "message": "服务已禁用"}

        if self.is_running:
            logger.warning("登录状态维持任务已在运行中，跳过本次执行")
            return {"success": False, "message": "任务已在运行中"}

        logger.info("开始维持登录状态任务")
        self.is_running = True
        self.last_run_time = datetime.now()
        self.stats["total_runs"] += 1
        
        results = {
            "success": True,
            "start_time": self.last_run_time.isoformat(),
            "accounts_processed": 0,
            "successful_accounts": 0,
            "failed_accounts": 0,
            "details": []
        }
        
        try:
            # 检查是否被取消
            if asyncio.current_task().cancelled():
                raise asyncio.CancelledError("任务被取消")

            # 获取所有已登录的账号
            accounts = self._get_logged_accounts()
            if not accounts:
                logger.info("没有找到已登录的账号")
                results["message"] = "没有找到已登录的账号"
                return results

            logger.info(f"找到 {len(accounts)} 个已登录账号")
            
            # 按平台分组处理
            platform_groups = self._group_accounts_by_platform(accounts)
            
            for platform, platform_accounts in platform_groups.items():
                # 标准化平台类型进行启用检查
                normalized_platform = self._normalize_platform_type(platform)
                if normalized_platform not in self.config["enabled_platforms"]:
                    logger.info(f"平台 {platform} (标准化为 {normalized_platform}) 未启用，跳过")
                    continue
                
                logger.info(f"处理平台 {platform} 的 {len(platform_accounts)} 个账号")
                
                # 处理该平台的所有账号
                for account in platform_accounts:
                    # 检查是否被取消
                    try:
                        if asyncio.current_task().cancelled():
                            raise asyncio.CancelledError("任务被取消")
                    except RuntimeError:
                        # 如果没有当前任务，继续执行
                        pass

                    account_result = await self._maintain_account_login(account)
                    results["details"].append(account_result)
                    results["accounts_processed"] += 1

                    if account_result["success"]:
                        results["successful_accounts"] += 1
                        self.stats["successful_maintains"] += 1
                    else:
                        results["failed_accounts"] += 1
                        self.stats["failed_maintains"] += 1

                    self.stats["accounts_processed"] += 1

                    # 账号间添加延迟
                    if len(platform_accounts) > 1:
                        delay = random.randint(*self.config["account_delay_seconds"])
                        logger.info(f"账号间延迟 {delay} 秒")
                        await asyncio.sleep(delay)
            
            results["end_time"] = datetime.now().isoformat()
            logger.info(f"登录状态维持任务完成，处理了 {results['accounts_processed']} 个账号")

        except asyncio.CancelledError:
            logger.warning("登录状态维持任务被取消")
            results["success"] = False
            results["error"] = "任务被取消"
            results["end_time"] = datetime.now().isoformat()
            raise  # 重新抛出CancelledError
        except Exception as e:
            logger.error(f"维持登录状态任务失败: {e}")
            results["success"] = False
            results["error"] = str(e)
            results["end_time"] = datetime.now().isoformat()
        
        finally:
            self.is_running = False
        
        return results
    
    async def _maintain_account_login(self, account: PlatformAccount) -> Dict:
        """维持单个账号的登录状态（带重试机制）"""
        logger.info(f"维持账号 {account.name} ({account.platform}) 的登录状态")

        result = {
            "account_id": account.id,
            "account_name": account.name,
            "platform": account.platform,
            "success": False,
            "message": "",
            "visited_page": "",
            "login_status_updated": False,
            "retry_count": 0
        }

        # 重试机制
        max_retries = self.config["max_retries"]
        for attempt in range(max_retries + 1):
            if attempt > 0:
                logger.info(f"账号 {account.name} 第 {attempt} 次重试")
                result["retry_count"] = attempt
                # 重试前等待一段时间
                await asyncio.sleep(random.randint(5, 15))

            attempt_result = await self._maintain_account_login_attempt(account)

            # 如果成功或者是登录失效（不需要重试），直接返回
            if (attempt_result["success"] or
                "登录状态已失效" in attempt_result["message"] or
                "登录状态文件不存在或已过期" in attempt_result["message"]):
                result.update(attempt_result)
                break

            # 如果是最后一次尝试，记录失败结果
            if attempt == max_retries:
                result.update(attempt_result)
                result["message"] = f"重试 {max_retries} 次后仍然失败: {attempt_result['message']}"

        # 记录维持操作日志
        await self._log_keeper_record(account, result)

        return result

    async def _maintain_account_login_attempt(self, account: PlatformAccount) -> Dict:
        """单次维持账号登录状态的尝试"""
        result = {
            "account_id": account.id,
            "account_name": account.name,
            "platform": account.platform,
            "success": False,
            "message": "",
            "visited_page": "",
            "login_status_updated": False,
            "page_title": "",
            "response_time": 0.0
        }

        service = None
        start_time = datetime.now()

        try:
            # 创建对应平台的服务实例
            service = await self._create_platform_service(account)
            if not service:
                result["message"] = f"不支持的平台: {account.platform}"
                return result

            # 尝试加载登录状态
            if not await service.load_login_state():
                result["message"] = "登录状态文件不存在或已过期"
                logger.warning(f"账号 {account.name} 登录状态文件不存在或已过期，标记为未登录")
                await self._update_account_login_status(account.id, False)
                result["login_status_updated"] = True
                return result

            # 随机选择页面进行访问
            page_url = self._get_random_page(account.platform)
            if not page_url:
                result["message"] = f"平台 {account.platform} 没有配置页面"
                return result

            result["visited_page"] = page_url

            # 访问页面并验证登录状态
            page_info = await self._visit_and_validate_page(service, page_url, account.platform)

            result["page_title"] = page_info.get("title", "")
            is_logged_in = page_info.get("is_logged_in", False)

            if is_logged_in:
                # 保存最新的登录状态
                await service.save_login_state()
                await self._update_account_login_status(account.id, True)
                result["success"] = True
                result["message"] = "登录状态维持成功"
                result["login_status_updated"] = True
                logger.info(f"账号 {account.name} 登录状态维持成功")
            else:
                logger.warning(f"账号 {account.name} 登录状态验证失败，标记为未登录")
                await self._update_account_login_status(account.id, False)
                result["message"] = "检测到登录状态已失效"
                result["login_status_updated"] = True
                logger.warning(f"账号 {account.name} 登录状态已失效，已更新数据库")

        except Exception as e:
            logger.error(f"维持账号 {account.name} 登录状态失败: {e}")
            result["message"] = f"维持失败: {str(e)}"

        finally:
            # 计算响应时间
            result["response_time"] = (datetime.now() - start_time).total_seconds()

            # 清理资源
            if service:
                try:
                    await service.close()
                except Exception as e:
                    logger.error(f"关闭服务失败: {e}")

        return result
    
    def _get_logged_accounts(self) -> List[PlatformAccount]:
        """获取所有已登录的账号"""
        db = SessionLocal()
        try:
            accounts = db.query(PlatformAccount).filter(
                PlatformAccount.login_status == True
            ).all()
            return accounts
        finally:
            db.close()
    
    def _normalize_platform_type(self, platform: str) -> str:
        """标准化平台类型"""
        # 处理平台类型映射，兼容旧的平台类型
        if platform == "wechat_service":
            return "wechat_mp"
        return platform

    def _group_accounts_by_platform(self, accounts: List[PlatformAccount]) -> Dict[str, List[PlatformAccount]]:
        """按平台分组账号"""
        groups = {}
        for account in accounts:
            if account.platform not in groups:
                groups[account.platform] = []
            groups[account.platform].append(account)
        return groups
    
    async def _create_platform_service(self, account: PlatformAccount):
        """创建平台服务实例"""
        platform = self._normalize_platform_type(account.platform)

        if platform == "wechat_mp":
            return WeChatMPService(account_id=account.id, headless=True)
        elif platform == "xiaohongshu":
            return XiaohongshuService(account_id=account.id, headless=True)
        elif platform == "wechat_channels":
            return WeChatChannelsService(account_id=account.id, headless=True)
        else:
            logger.warning(f"不支持的平台类型: {account.platform} (标准化为: {platform})")
            return None
    
    def _get_random_page(self, platform: str) -> Optional[str]:
        """随机获取平台页面"""
        mapped_platform = self._normalize_platform_type(platform)

        if not PlatformPagesConfig.is_platform_supported(mapped_platform):
            logger.warning(f"平台 {platform} (标准化为 {mapped_platform}) 不支持页面访问")
            return None
        return PlatformPagesConfig.get_weighted_random_page(mapped_platform)
    
    async def _visit_and_validate_page(self, service, page_url: str, platform: str) -> Dict:
        """访问页面并验证登录状态"""
        page = None
        result = {
            "is_logged_in": False,
            "title": "",
            "current_url": "",
            "error": None
        }

        try:
            # 确保服务已初始化
            if not service.context:
                logger.error("服务上下文未初始化")
                result["error"] = "服务上下文未初始化"
                return result

            # 访问页面
            page = await service.context.new_page()
            logger.info(f"正在访问页面: {page_url}")

            # 设置页面超时
            page.set_default_timeout(self.config["browser_timeout"] * 1000)

            # 访问页面
            response = await page.goto(page_url, wait_until="domcontentloaded")

            if not response or response.status >= 400:
                error_msg = f"页面访问失败，状态码: {response.status if response else 'None'}"
                logger.warning(error_msg)
                result["error"] = error_msg
                return result

            # 等待页面加载完成
            try:
                await page.wait_for_load_state("domcontentloaded", timeout=15000)
            except Exception as e:
                logger.warning(f"等待页面加载超时: {e}")
                # 继续执行，不一定是错误

            # 随机停留一段时间，模拟真实用户行为
            stay_time = random.randint(*self.config["page_stay_seconds"])
            logger.info(f"页面停留 {stay_time} 秒")
            await asyncio.sleep(stay_time)

            # 获取页面标题和URL进行基本验证
            title = await page.title()
            current_url = page.url
            logger.info(f"页面标题: {title}, 当前URL: {current_url}")

            result["title"] = title
            result["current_url"] = current_url

            # 获取页面内容进行验证
            content = await page.content()

            # 验证登录状态
            mapped_platform = self._normalize_platform_type(platform)
            validation_config = PlatformPagesConfig.get_validation_rules(mapped_platform)
            is_logged_in = self._validate_login_status(content, validation_config, title, current_url)

            result["is_logged_in"] = is_logged_in
            logger.info(f"登录状态验证结果: {is_logged_in}")

            return result

        except Exception as e:
            error_msg = f"访问页面 {page_url} 失败: {e}"
            logger.error(error_msg)
            result["error"] = error_msg
            return result
        finally:
            if page:
                try:
                    await page.close()
                except Exception as e:
                    logger.error(f"关闭页面失败: {e}")
    
    def _validate_login_status(self, content: str, validation_config: Dict, title: str = "", current_url: str = "") -> bool:
        """验证登录状态"""
        if not validation_config:
            logger.info("没有验证配置，默认认为已登录")
            return True

        # 合并所有文本内容进行检查
        all_text = f"{content} {title} {current_url}".lower()
        # logger.info(f"验证登录状态，内容长度: {all_text}")
        logger.info(f"验证登录状态")

        # 检查登录失效指示器
        login_required_indicators = validation_config.get("login_required_indicators", [])
        for indicator in login_required_indicators:
            if indicator.lower() in all_text:
                logger.warning(f"检测到登录失效指示器: {indicator}")
                return False

        # 检查成功指示器
        success_indicators = validation_config.get("success_indicators", [])
        if success_indicators:
            found_success = False
            for indicator in success_indicators:
                if indicator.lower() in all_text:
                    logger.info(f"检测到成功指示器: {indicator}")
                    found_success = True
                    break

            if not found_success:
                logger.warning("未找到任何成功指示器，可能登录已失效")
                return False

        logger.info("登录状态验证通过")
        return True

    async def _log_keeper_record(self, account: PlatformAccount, result: Dict):
        """记录登录状态维持操作日志"""
        db = SessionLocal()
        try:
            # 确定状态
            if result["success"]:
                status = "success"
            elif "登录状态已失效" in result["message"]:
                status = "login_expired"
            else:
                status = "failed"

            # 创建日志记录
            record = LoginKeeperRecord(
                account_id=account.id,
                platform=account.platform,
                visited_page=result.get("visited_page", ""),
                page_title=result.get("page_title", ""),
                status=status,
                error_message=result["message"] if not result["success"] else None,
                response_time=result.get("response_time", 0.0)
            )

            db.add(record)
            db.commit()
            logger.info(f"已记录账号 {account.name} 的维持操作日志")

        except Exception as e:
            logger.error(f"记录维持操作日志失败: {e}")
            db.rollback()
        finally:
            db.close()
    
    async def _update_account_login_status(self, account_id: int, is_logged_in: bool):
        """更新账号登录状态"""
        db = SessionLocal()
        try:
            account = db.query(PlatformAccount).filter(PlatformAccount.id == account_id).first()
            if not account:
                logger.error(f"未找到账号 ID: {account_id}")
                return False

            old_status = account.login_status
            account.login_status = is_logged_in

            if is_logged_in:
                account.last_login_time = datetime.now()
                # 如果状态从失效变为有效，可以更新cookies等信息
                logger.info(f"账号 {account.name} 登录状态恢复")
            else:
                # 登录状态失效时，可以清理相关信息
                if old_status:  # 之前是登录状态
                    logger.warning(f"账号 {account.name} 登录状态失效")

            db.commit()
            logger.info(f"更新账号 {account.name} (ID: {account_id}) 登录状态: {old_status} -> {is_logged_in}")
            return True

        except Exception as e:
            logger.error(f"更新账号登录状态失败: {e}")
            db.rollback()
            return False
        finally:
            db.close()
    
    def get_status(self) -> Dict:
        """获取服务状态"""
        return {
            "is_running": self.is_running,
            "last_run_time": self.last_run_time.isoformat() if self.last_run_time else None,
            "config": self.config,
            "stats": self.stats,
            "enabled": is_keeper_enabled()
        }

    def get_recent_records(self, limit: int = 50) -> List[Dict]:
        """获取最近的维持记录"""
        db = SessionLocal()
        try:
            records = db.query(LoginKeeperRecord).join(PlatformAccount).order_by(
                LoginKeeperRecord.created_at.desc()
            ).limit(limit).all()

            result = []
            for record in records:
                result.append({
                    "id": record.id,
                    "account_name": record.account.name,
                    "platform": record.platform,
                    "visited_page": record.visited_page,
                    "page_title": record.page_title,
                    "status": record.status,
                    "error_message": record.error_message,
                    "response_time": record.response_time,
                    "created_at": record.created_at.isoformat()
                })

            return result

        except Exception as e:
            logger.error(f"获取维持记录失败: {e}")
            return []
        finally:
            db.close()

    def get_account_stats(self, account_id: int, days: int = 7) -> Dict:
        """获取指定账号的维持统计"""
        db = SessionLocal()
        try:
            from sqlalchemy import func

            # 计算时间范围
            start_date = datetime.now() - timedelta(days=days)

            # 查询统计数据
            from sqlalchemy import case

            stats = db.query(
                func.count(LoginKeeperRecord.id).label('total_attempts'),
                func.sum(case((LoginKeeperRecord.status == 'success', 1), else_=0)).label('successful'),
                func.sum(case((LoginKeeperRecord.status == 'failed', 1), else_=0)).label('failed'),
                func.sum(case((LoginKeeperRecord.status == 'login_expired', 1), else_=0)).label('expired'),
                func.avg(LoginKeeperRecord.response_time).label('avg_response_time')
            ).filter(
                LoginKeeperRecord.account_id == account_id,
                LoginKeeperRecord.created_at >= start_date
            ).first()

            return {
                "account_id": account_id,
                "days": days,
                "total_attempts": stats.total_attempts or 0,
                "successful": stats.successful or 0,
                "failed": stats.failed or 0,
                "expired": stats.expired or 0,
                "success_rate": (stats.successful / stats.total_attempts * 100) if stats.total_attempts else 0,
                "avg_response_time": float(stats.avg_response_time or 0)
            }

        except Exception as e:
            logger.error(f"获取账号统计失败: {e}")
            return {}
        finally:
            db.close()

    def get_platform_stats(self, days: int = 7) -> Dict:
        """获取各平台的维持统计"""
        db = SessionLocal()
        try:
            from sqlalchemy import func, case

            # 计算时间范围
            start_date = datetime.now() - timedelta(days=days)

            # 按平台统计
            platform_stats = db.query(
                LoginKeeperRecord.platform,
                func.count(LoginKeeperRecord.id).label('total_attempts'),
                func.sum(case((LoginKeeperRecord.status == 'success', 1), else_=0)).label('successful'),
                func.sum(case((LoginKeeperRecord.status == 'failed', 1), else_=0)).label('failed'),
                func.sum(case((LoginKeeperRecord.status == 'login_expired', 1), else_=0)).label('expired'),
                func.avg(LoginKeeperRecord.response_time).label('avg_response_time')
            ).filter(
                LoginKeeperRecord.created_at >= start_date
            ).group_by(LoginKeeperRecord.platform).all()

            result = {}
            for stat in platform_stats:
                result[stat.platform] = {
                    "total_attempts": stat.total_attempts or 0,
                    "successful": stat.successful or 0,
                    "failed": stat.failed or 0,
                    "expired": stat.expired or 0,
                    "success_rate": (stat.successful / stat.total_attempts * 100) if stat.total_attempts else 0,
                    "avg_response_time": float(stat.avg_response_time or 0)
                }

            return result

        except Exception as e:
            logger.error(f"获取平台统计失败: {e}")
            return {}
        finally:
            db.close()
