"""
平台页面配置
定义各个平台可以访问的页面列表和验证规则
"""
from typing import Dict, List

class PlatformPagesConfig:
    """平台页面配置类"""
    
    # 微信公众号页面配置
    WECHAT_MP_PAGES = [
        {
            "url": "https://mp.weixin.qq.com/",
            "name": "首页",
            "weight": 3  # 权重，数字越大被选中的概率越高
        }
    ]
    
    # 小红书页面配置
    XIAOHONGSHU_PAGES = [
        {
            "url": "https://creator.xiaohongshu.com/",
            "name": "创作中心首页",
            "weight": 3
        }
    ]
    
    # 微信视频号页面配置
    WECHAT_CHANNELS_PAGES = [
        {
            "url": "https://channels.weixin.qq.com/",
            "name": "视频号首页",
            "weight": 3
        }
    ]
    
    # 页面验证规则
    VALIDATION_RULES = {
        "wechat_mp": {
            "success_indicators": [
                "内容管理",
                "互动管理",
                "数据分析",
                "广告与服务"
            ],
            "login_required_indicators": [
                "公众平台账号登录",
                "使用账号登录",
                "登录超时， 请重新登录"
            ]
        },

        "wechat_service": {
            "success_indicators": [
                "内容管理",
                "互动管理",
                "数据分析",
                "广告与服务"
            ],
            "login_required_indicators": [
                "公众平台账号登录",
                "使用账号登录",
                "登录超时， 请重新登录"
            ]
        },
        
        "xiaohongshu": {
            "success_indicators": [
                "笔记管理",
                "数据看板",
                "活动中心"
            ],
            "login_required_indicators": [
                "短信登录",
                "扫码登录",
                "收不到验证码",
                "APP扫一扫登录"
            ]
        },
        
        "wechat_channels": {
            "success_indicators": [
                "通知中心",
                "带货中心",
                "关注者数据"
            ],
            "login_required_indicators": [
                "微信扫码登录 视频号助手"
            ]
        }
    }
    
    @classmethod
    def get_platform_pages(cls, platform: str) -> List[Dict]:
        """获取平台页面配置"""
        if platform == "wechat_mp" or platform == "wechat_service":
            return cls.WECHAT_MP_PAGES
        elif platform == "xiaohongshu":
            return cls.XIAOHONGSHU_PAGES
        elif platform == "wechat_channels":
            return cls.WECHAT_CHANNELS_PAGES
        else:
            return []
    
    @classmethod
    def get_validation_rules(cls, platform: str) -> Dict:
        """获取平台验证规则"""
        return cls.VALIDATION_RULES.get(platform, {})
    
    @classmethod
    def get_weighted_random_page(cls, platform: str) -> str:
        """根据权重随机选择页面"""
        import random
        
        pages = cls.get_platform_pages(platform)
        if not pages:
            return ""
        
        # 创建权重列表
        weights = [page["weight"] for page in pages]
        
        # 根据权重随机选择
        selected_page = random.choices(pages, weights=weights, k=1)[0]
        return selected_page["url"]
    
    @classmethod
    def get_all_supported_platforms(cls) -> List[str]:
        """获取所有支持的平台"""
        return ["wechat_mp", "xiaohongshu", "wechat_channels"]
    
    @classmethod
    def is_platform_supported(cls, platform: str) -> bool:
        """检查平台是否支持"""
        return platform in cls.get_all_supported_platforms()
    
    @classmethod
    def get_platform_info(cls, platform: str) -> Dict:
        """获取平台信息"""
        platform_info = {
            "wechat_mp": {
                "name": "微信公众号",
                "description": "微信公众平台管理后台",
                "base_url": "https://mp.weixin.qq.com"
            },
            "xiaohongshu": {
                "name": "小红书",
                "description": "小红书创作服务平台",
                "base_url": "https://creator.xiaohongshu.com"
            },
            "wechat_channels": {
                "name": "微信视频号",
                "description": "微信视频号助手平台",
                "base_url": "https://channels.weixin.qq.com"
            }
        }
        
        return platform_info.get(platform, {})
