"""
平台服务基础类和统一数据结构
"""

from dataclasses import dataclass
from typing import Optional, Any, List, Tuple
from abc import ABC, abstractmethod


@dataclass
class DataDownloadResult:
    """数据下载结果统一格式"""
    success: bool
    data: Optional[Any] = None
    error_message: Optional[str] = None
    file_path: Optional[str] = None
    records_count: int = 0


class TaskItemStatus:
    """任务项状态常量"""
    PENDING = 'pending'      # 等待执行
    RUNNING = 'running'      # 正在执行
    COMPLETED = 'completed'  # 执行成功
    FAILED = 'failed'        # 执行失败
    RETRYING = 'retrying'    # 重试中
    CANCELLED = 'cancelled'  # 已取消


class PlatformServiceBase(ABC):
    """平台服务抽象基类"""
    
    def __init__(self, account_id: int):
        self.account_id = account_id
    
    @abstractmethod
    async def download_single_data_type(
        self, 
        data_type: str, 
        start_date: Optional[str] = None, 
        end_date: Optional[str] = None,
        auto_import: bool = True,
        **kwargs
    ) -> DataDownloadResult:
        """根据数据类型下载对应数据的统一入口"""
        pass
    
    @abstractmethod
    def get_supported_data_types(self) -> List[Tuple[str, str]]:
        """获取支持的数据类型列表，返回 (data_type, display_name) 元组列表"""
        pass
    
    @abstractmethod
    async def load_login_state(self) -> bool:
        """加载登录状态"""
        pass
    
    @abstractmethod
    async def close(self):
        """关闭服务，释放资源"""
        pass


class PlatformServiceFactory:
    """平台服务工厂类"""
    
    # 平台数据类型映射
    PLATFORM_DATA_TYPES = {
        'wechat_channels': [
            ('single_video', '单篇视频数据'),
            ('follower_data', '关注者数据')
        ],
        'xiaohongshu': [
            ('note_data', '笔记数据'),
            ('account_overview', '账号概览'),
            ('fans_data', '粉丝数据')
        ],
        'wechat_mp': [
            ('content_trend', '内容趋势'),
            ('content_source', '内容来源'),
            ('content_detail', '内容详情'),
            ('user_channel', '用户渠道'),
            ('user_source', '用户来源')
        ],
        'wechat_service': [
            ('content_trend', '内容趋势'),
            ('content_source', '内容来源'),
            ('content_detail', '内容详情'),
            ('user_channel', '用户渠道'),
            ('user_source', '用户来源')
        ],
        'douyin': [
            ('video_data', '视频数据'),
            ('account_overview', '账号概览'),
            ('fans_data', '粉丝数据')
        ]
    }
    
    @staticmethod
    def create_service(platform: str, account_id: int) -> PlatformServiceBase:
        """根据平台类型创建对应的服务实例"""
        if platform == 'wechat_channels':
            from app.services.wechat_channels_service import WeChatChannelsService
            return WeChatChannelsService(account_id=account_id)
        elif platform == 'xiaohongshu':
            from app.services.xiaohongshu_service import XiaohongshuService
            return XiaohongshuService(account_id=account_id)
        elif platform == 'wechat_mp':
            from app.services.wechat_service import WeChatMPService
            return WeChatMPService(account_id=account_id)
        elif platform == 'wechat_service':
            from app.services.wechat_service import WeChatMPService
            return WeChatMPService(account_id=account_id)
        elif platform == 'douyin':
            from app.services.douyin_service import DouyinService
            return DouyinService(account_id=account_id)
        else:
            raise ValueError(f"不支持的平台类型: {platform}")
    
    @staticmethod
    def get_platform_data_types(platform: str) -> List[Tuple[str, str]]:
        """获取平台支持的数据类型"""
        return PlatformServiceFactory.PLATFORM_DATA_TYPES.get(platform, [])
    
    @staticmethod
    def get_all_platforms() -> List[str]:
        """获取所有支持的平台"""
        return list(PlatformServiceFactory.PLATFORM_DATA_TYPES.keys())
    
    @staticmethod
    def validate_data_type(platform: str, data_type: str) -> bool:
        """验证数据类型是否被平台支持"""
        supported_types = [dt[0] for dt in PlatformServiceFactory.get_platform_data_types(platform)]
        return data_type in supported_types
    
    @staticmethod
    def get_data_type_display_name(platform: str, data_type: str) -> Optional[str]:
        """获取数据类型的显示名称"""
        data_types = PlatformServiceFactory.get_platform_data_types(platform)
        for dt, display_name in data_types:
            if dt == data_type:
                return display_name
        return None
