import asyncio
import base64
import json
import os
import sys
import time
import csv
import tempfile
from datetime import datetime, date
from playwright.async_api import async_playwright, <PERSON>, Browser, BrowserContext
from typing import Optional, Dict, Any, List, Tuple
from app.config.wechat_channels_config import WeChatChannelsConfig
from app.exceptions import LoginExpiredException
from app.services.platform_service_base import PlatformServiceBase, DataDownloadResult

from app.services.browser_manager import browser_manager

class WeChatChannelsService(PlatformServiceBase):
    """微信视频号服务类"""

    # 数据下载模板配置
    DOWNLOAD_TEMPLATES = {
        'single_video': {
            'name': '单篇视频数据',
            'data_start_row': 1,  # CSV文件数据从第1行开始（第0行是标题）
            'fields': [
                ('视频描述', 1),      # 多行文本
                ('视频ID', 1),        # 文本
                ('发布时间', 1),      # 文本/日期
                ('完播率', 1),        # 文本/百分比
                ('平均播放时长', 2),   # 数字
                ('播放量', 2),        # 数字
                ('推荐', 2),          # 数字
                ('喜欢', 2),          # 数字
                ('评论量', 2),        # 数字
                ('分享量', 2),        # 数字
                ('关注量', 2),        # 数字
                ('转发聊天和朋友圈', 2), # 数字
                ('设为铃声', 2),      # 数字
                ('设为状态', 2),      # 数字
                ('设为朋友圈封面', 2), # 数字
            ]
        }
    }

    def __init__(self, account_id: Optional[int] = None, headless: bool = True):
        super().__init__(account_id)
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        self.playwright = None
        self.headless = headless  # 添加headless参数
        self.user_data_dir = self._get_user_data_dir()

        # 登录二维码缓存（减少重复创建上下文 & 支持重复点击立即返回）
        self._last_qr_base64: Optional[str] = None
        self._last_qr_timestamp: Optional[float] = None

    def _get_user_data_dir(self) -> str:
        """获取用户数据目录路径"""
        if self.account_id:
            # 为每个账号创建独立的用户数据目录
            base_dir = os.path.join(os.getcwd(), "user_data")
            user_dir = os.path.join(base_dir, f"wechat_channels_account_{self.account_id}")
        else:
            # 默认目录
            user_dir = os.path.join(os.getcwd(), "user_data", "wechat_channels_default")

        # 确保目录存在
        os.makedirs(user_dir, exist_ok=True)
        return user_dir

    def _get_downloads_dir(self) -> str:
        """获取稳定的下载目录（按账号隔离）"""
        base = self.user_data_dir if hasattr(self, 'user_data_dir') else os.path.join(os.getcwd(), "user_data")
        downloads_dir = os.path.join(base, "downloads")
        os.makedirs(downloads_dir, exist_ok=True)
        return downloads_dir


    def _get_download_config(self, data_type: str) -> Optional[Dict[str, Any]]:
        """获取下载配置

        Args:
            data_type: 数据类型

        Returns:
            下载配置字典
        """
        return self.DOWNLOAD_TEMPLATES.get(data_type)

    def _can_use_gui(self) -> bool:
        """检测当前环境是否支持GUI显示"""
        try:
            # 检测是否在SSH会话中
            if os.environ.get('SSH_CLIENT') or os.environ.get('SSH_TTY'):
                print("🔍 检测到SSH会话，不支持GUI模式")
                return False

            # 检测是否在CI/CD环境中
            ci_indicators = ['CI', 'CONTINUOUS_INTEGRATION', 'GITHUB_ACTIONS', 'GITLAB_CI']
            if any(os.environ.get(indicator) for indicator in ci_indicators):
                print("🔍 检测到CI/CD环境，不支持GUI模式")
                return False

            # 检查DISPLAY环境变量（Linux/Unix系统）
            if sys.platform.startswith('linux'):
                if not os.environ.get('DISPLAY'):
                    print("🔍 未设置DISPLAY环境变量，可能在无GUI环境中")
                    return False

            # 检查是否通过SSH连接且无GUI转发
            if os.environ.get('SSH_CLIENT') and not os.environ.get('DISPLAY'):
                print("🔍 SSH连接且无DISPLAY环境变量，不支持GUI模式")
                return False

            print("✅ 环境支持GUI模式")
            return True

        except Exception as e:
            print(f"⚠️ 检测GUI环境时出错: {e}，默认不支持GUI")
            return False



    def _check_chromium_compatibility(self) -> bool:
        """检查Chromium兼容性"""
        try:
            import platform
            system_info = platform.uname()
            print(f"🔍 系统信息: {system_info.system} {system_info.release} {system_info.machine}")

            # 基本兼容性检查通过
            print("✅ Chromium兼容性检查通过")

            return True

        except Exception as e:
            print(f"⚠️ 检查Chromium兼容性时出错: {e}")
            return True  # 默认假设兼容

    async def _init_browser(self):
        """初始化浏览器"""
        if self.playwright is None:
            self.playwright = await async_playwright().start()

        if self.browser is None:
            # 检查系统兼容性
            # self._check_chromium_compatibility()

            # 检查是否请求可视模式但环境不支持
            actual_headless = self.headless
            if not self.headless and not self._can_use_gui():
                print("⚠️ 请求可视模式但环境不支持GUI，自动切换到headless模式")
                actual_headless = True

            # 直接使用Playwright管理的Chromium（Docker环境中已安装）

            # 使用统一的浏览器启动参数（移除环境特殊处理）
            launch_args = WeChatChannelsConfig.get_browser_args(False, actual_headless)

            if WeChatChannelsConfig.is_debug_mode():
                print(f"浏览器启动参数: {launch_args}")

            try:
                print(f"🚀 启动Playwright管理的Chromium (headless={actual_headless})")

                # 使用配置的浏览器启动超时时间
                browser_timeout = WeChatChannelsConfig.get_timeout('browser_launch', False)
                print(f"浏览器启动超时设置: {browser_timeout}ms")

                # 直接使用Playwright管理的Chromium
                self.browser = await self.playwright.chromium.launch(
                    headless=actual_headless,
                    args=launch_args,
                    slow_mo=500 if not actual_headless else 0,
                    timeout=browser_timeout,  # 使用配置的超时时间
                    ignore_default_args=['--enable-automation', '--enable-blink-features=IdleDetection']
                )
                print("✅ 浏览器启动成功")

            except Exception as e:
                print(f"❌ Chromium启动失败: {e}")
                if not actual_headless:
                    # 如果可视模式失败，尝试headless模式
                    print("🔄 可视模式启动失败，尝试headless模式...")
                    # 为headless模式调整参数
                    headless_args = [arg for arg in launch_args if arg != '--disable-extensions']
                    headless_args.append('--single-process')

                    try:
                        # 使用Playwright管理的Chromium（headless模式）
                        self.browser = await self.playwright.chromium.launch(
                            headless=True,
                            args=headless_args,
                            timeout=30000,
                            ignore_default_args=['--enable-automation', '--enable-blink-features=IdleDetection']
                        )
                        print("✅ 浏览器以headless模式启动成功")
                    except Exception as headless_error:
                        print(f"❌ Headless模式也失败: {headless_error}")
                        raise e

    async def _create_persistent_context(self) -> BrowserContext:
        """创建持久化的浏览器上下文"""
        if not self.browser:
            raise Exception("浏览器未初始化")

        # 使用持久化上下文
        self.context = await self.browser.new_context(
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
            viewport={"width": 1920, "height": 1080},
            accept_downloads=True,
        )

        return self.context

    async def get_login_qrcode(self) -> Optional[str]:
        """获取视频号登录二维码"""
        print("🎬 开始获取视频号登录二维码...")
        start_time = time.time()

        # 若最近30秒内已有二维码缓存，直接返回，避免重复创建上下文
        if getattr(self, "_last_qr_base64", None) and getattr(self, "_last_qr_timestamp", None):
            if time.time() - self._last_qr_timestamp < 30:
                print("返回最近缓存的二维码（30秒内）")
                return f"data:image/png;base64,{self._last_qr_base64}"

        max_retries = 2
        for attempt in range(max_retries):
            try:
                print(f"尝试获取视频号二维码 (第 {attempt + 1}/{max_retries} 次)")

                # 首先尝试加载已保存的登录状态
                if attempt == 0 and await self.load_login_state():
                    # 检查是否已经登录
                    if await self.check_existing_login():
                        print("检测到已有有效的登录状态")
                        # 更新数据库中的登录状态
                        await self._update_database_login_status(True)
                        return "already_logged_in"

                # 如果没有有效的登录状态，清理并重新开始
                await self.close()

                # 使用全局浏览器管理器创建上下文与页面
                print("🔧 正在创建浏览器上下文...")
                context_start = time.time()
                self.context = await browser_manager.create_context(
                    accept_downloads=True,
                    user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
                    viewport={"width": 1920, "height": 1080},
                )
                print(f"✅ 上下文创建完成，耗时: {time.time() - context_start:.2f}秒")

                print("📄 正在创建新页面...")
                self.page = await browser_manager.open_page(self.context)

                # 设置页面事件监听器
                self.page.on("close", lambda: print("⚠️ 页面被关闭"))
                self.page.on("crash", lambda: print("💥 页面崩溃"))

                # 检查浏览器是否仍然连接
                if self.browser and not self.browser.is_connected():
                    print("⚠️ 浏览器连接已断开")
                    if attempt < max_retries - 1:
                        continue
                    else:
                        raise Exception("浏览器连接断开")

                print("正在访问视频号登录页面...")
                # 访问视频号登录页面，增加重试和错误处理
                try:
                    nav_start = time.time()
                    await self.page.goto("https://channels.weixin.qq.com/login.html",
                                       wait_until="domcontentloaded", timeout=60000)
                    print(f"✅ 页面导航完成，耗时: {time.time() - nav_start:.2f}秒")
                except Exception as nav_error:
                    print(f"页面导航失败: {nav_error}")
                    if attempt < max_retries - 1:
                        print("等待3秒后重试...")
                        await asyncio.sleep(3)
                        continue
                    else:
                        raise nav_error

                # 等待页面完全加载
                # await asyncio.sleep(3)

                # 打印页面标题和URL用于调试
                page_title = await self.page.title()
                current_url = self.page.url
                print(f"页面标题: {page_title}")
                print(f"当前URL: {current_url}")

                # 首先等待iframe加载
                print("等待iframe加载...")
                try:
                    iframe_selector = 'iframe[src*="login-for-iframe"]'
                    await self.page.wait_for_selector(iframe_selector, state="visible", timeout=15000)
                    print("✅ 找到登录iframe")

                    # 获取iframe元素
                    iframe_element = await self.page.query_selector(iframe_selector)
                    if not iframe_element:
                        raise Exception("无法获取iframe元素")

                    # 获取iframe的内容框架
                    iframe_content = await iframe_element.content_frame()
                    if not iframe_content:
                        raise Exception("无法获取iframe内容框架")

                    print("✅ 成功切换到iframe内部")

                    # 在iframe内部查找二维码
                    print("在iframe内部等待二维码加载...")
                    qr_selectors = [
                        ".login-qrcode-wrap .qrcode-wrap .qrcode",                  # 简单选择器
                        # ".qrcode-area img.qrcode",  # 原始预期选择器
                        # ".qrcode-area img",         # 更宽泛的选择器
                        # "img[src*='qr']",          # 包含qr的图片
                        # ".login-qrcode img",       # 登录二维码图片
                        # ".weui-desktop-login__qrcode", # 微信桌面登录二维码
                        # "canvas",                   # 可能是canvas绘制的二维码
                        # ".qr-code img",            # 另一种可能的类名
                        # "[class*='qrcode'] img",   # 包含qrcode类名的元素下的图片
                        # "[class*='qr-code'] img",  # 包含qr-code类名的元素下的图片
                        # "img",                     # 最宽泛的图片选择器
                        # ".qr_code",                # 下划线版本
                        # "#qrcode"                  # ID选择器
                    ]

                    qr_element = None
                    for selector in qr_selectors:
                        try:
                            print(f"在iframe中尝试选择器: {selector}")
                            await iframe_content.wait_for_selector(selector, state="visible", timeout=5000)
                            qr_element = await iframe_content.query_selector(selector)
                            if qr_element:
                                print(f"✅ 在iframe中找到二维码元素: {selector}")
                                break
                        except Exception as e:
                            print(f"iframe中选择器 {selector} 失败: {e}")
                            continue

                    if not qr_element:
                        print("在iframe中未找到二维码元素")
                        if attempt < max_retries - 1:
                            print("等待3秒后重试...")
                            await asyncio.sleep(3)
                            continue
                        else:
                            print("返回iframe截图用于调试")
                            # 截取iframe内容用于调试
                            iframe_screenshot = await iframe_content.screenshot()
                            iframe_base64 = base64.b64encode(iframe_screenshot).decode()
                            return f"data:image/png;base64,{iframe_base64}"

                except Exception as iframe_error:
                    print(f"处理iframe时出错: {iframe_error}")
                    if attempt < max_retries - 1:
                        print("等待3秒后重试...")
                        await asyncio.sleep(3)
                        continue
                    else:
                        print("返回整个页面截图用于调试")
                        page_screenshot = await self.page.screenshot()
                        page_base64 = base64.b64encode(page_screenshot).decode()
                        return f"data:image/png;base64,{page_base64}"

                # 等待二维码图片完全加载
                await asyncio.sleep(1)

                # 检查元素是否可见
                is_visible = await qr_element.is_visible()
                if not is_visible:
                    print("二维码元素不可见")
                    if attempt < max_retries - 1:
                        print("等待3秒后重试...")
                        await asyncio.sleep(3)
                        continue
                    else:
                        return None

                # 获取二维码图片
                qr_screenshot = await qr_element.screenshot()

                # 检查截图是否为空
                if len(qr_screenshot) == 0:
                    print("二维码截图为空")
                    if attempt < max_retries - 1:
                        print("等待3秒后重试...")
                        await asyncio.sleep(3)
                        continue
                    else:
                        return None

                # 转换为base64并缓存
                qr_base64 = base64.b64encode(qr_screenshot).decode()
                self._last_qr_base64 = qr_base64
                self._last_qr_timestamp = time.time()

                total_time = time.time() - start_time
                print(f"✅ 成功获取视频号二维码，大小: {len(qr_screenshot)} bytes，总耗时: {total_time:.2f}秒")

                # 重要：不要关闭浏览器，保持页面状态用于后续的登录状态检查
                print("保持页面状态，等待用户扫码...")

                return f"data:image/png;base64,{qr_base64}"
            except Exception as e:
                print(f"第 {attempt + 1} 次尝试失败: {e}")
                # 只有在所有重试都失败时才关闭浏览器
                if attempt == max_retries - 1:
                    await self.close()

                if attempt < max_retries - 1:
                    print(f"等待5秒后进行第 {attempt + 2} 次尝试...")
                    await asyncio.sleep(5)
                else:
                    print("所有尝试都失败了")
                    return None

    async def get_cached_or_rescreenshot_qrcode(self) -> Optional[str]:
        """返回最近缓存的二维码；若无缓存尝试在现有页面中重新截取二维码。
        不会创建新的上下文/页面，避免并发积压。
        """
        # 先返回非过期缓存（30秒）
        if getattr(self, "_last_qr_base64", None) and getattr(self, "_last_qr_timestamp", None):
            if time.time() - self._last_qr_timestamp < 30:
                return f"data:image/png;base64,{self._last_qr_base64}"

        # 若有现有页面，尝试重新截取
        if self.page:
            try:
                # 进入iframe（与主流程保持一致）
                await self.page.wait_for_load_state("domcontentloaded", timeout=15000)
                frames = self.page.frames
                login_frame = None
                for f in frames:
                    try:
                        url = f.url
                        if "login" in url or "qrcode" in url:
                            login_frame = f
                            break
                    except Exception:
                        continue
                if not login_frame:
                    login_frame = self.page.main_frame

                selectors = [
                    ".qrcode",
                    ".qrcode-area img.qrcode",
                    ".qrcode-area img",
                    "img[src*='qr']",
                    ".login-qrcode img",
                    ".weui-desktop-login__qrcode",
                    "canvas",
                    ".qr-code img",
                    "[class*='qrcode'] img",
                    "[class*='qr-code'] img",
                    "img",
                    ".qr_code",
                    "#qrcode",
                ]
                el = None
                for sel in selectors:
                    try:
                        el = await login_frame.wait_for_selector(sel, timeout=2000)
                        if el:
                            break
                    except Exception:
                        continue
                if not el:
                    return None
                img_bytes = await el.screenshot()
                if not img_bytes:
                    return None
                b64 = base64.b64encode(img_bytes).decode()
                self._last_qr_base64 = b64
                self._last_qr_timestamp = time.time()
                return f"data:image/png;base64,{b64}"
            except Exception:
                return None
        return None

    async def _check_network_connectivity(self) -> bool:
        """检查网络连接性"""
        try:
            print("检查网络连接...")
            timeout = WeChatChannelsConfig.get_timeout('network_check', False)

            # 首先检查微信视频号域名的连通性
            try:
                test_response = await self.page.goto(
                    "https://channels.weixin.qq.com/",
                    wait_until="domcontentloaded",  # 使用更快的等待策略
                    timeout=min(timeout, 30000)  # 限制网络检查最大超时为30秒
                )
                if test_response and test_response.status == 200:
                    print("✅ 微信视频号网络连接正常")
                    return True
                else:
                    print(f"❌ 微信视频号连接异常，状态码: {test_response.status if test_response else 'None'}")
                    return False
            except Exception as wechat_e:
                print(f"❌ 微信视频号连接失败: {wechat_e}")

                # 备用检查：尝试访问百度
                try:
                    backup_response = await self.page.goto(
                        "https://www.baidu.com",
                        wait_until="domcontentloaded",
                        timeout=15000  # 15秒超时
                    )
                    if backup_response and backup_response.status == 200:
                        print("⚠️ 基础网络连接正常，但微信视频号可能有问题")
                        return False  # 虽然网络正常，但微信视频号不可达
                    else:
                        print("❌ 基础网络连接异常")
                        return False
                except Exception as backup_e:
                    print(f"❌ 备用网络检查也失败: {backup_e}")
                    return False

        except Exception as e:
            print(f"❌ 网络连接检查失败: {e}")
            return False

    async def check_existing_login(self) -> bool:
        """检查现有登录状态是否仍然有效"""
        try:
            if not self.page:
                return False

            print("检查登录状态...")

            # 使用更合适的超时时间
            timeout = WeChatChannelsConfig.get_timeout('navigation', False)
            if WeChatChannelsConfig.is_debug_mode():
                print(f"使用超时时间: {timeout}ms")

            # 尝试访问平台页面
            try:
                print("尝试访问平台页面验证登录状态...")
                await self.page.goto(
                    "https://channels.weixin.qq.com/platform",
                    wait_until="domcontentloaded",  # 使用更快的等待策略
                    timeout=min(timeout, 60000)  # 限制最大超时为60秒
                )
                await asyncio.sleep(2)

                # 检查是否已经登录（URL仍然是platform页面且没有跳转到登录页）
                current_url = self.page.url
                print(f"当前URL: {current_url}")

                if "channels.weixin.qq.com/platform" in current_url and "login" not in current_url:
                    print("✅ 检测到有效的视频号登录状态")
                    return True
                else:
                    print("❌ 视频号登录状态已失效")
                    return False

            except Exception as nav_e:
                print(f"导航到平台页面失败: {nav_e}")
                # 如果导航失败，检查是否是网络问题
                try:
                    # 简单检查当前页面状态
                    current_url = self.page.url
                    if "channels.weixin.qq.com/platform" in current_url and "login" not in current_url:
                        print("✅ 当前已在平台页面，登录状态有效")
                        return True
                    else:
                        print("❌ 当前不在平台页面，登录状态可能失效")
                        return False
                except Exception as url_e:
                    print(f"获取当前URL失败: {url_e}")
                    return False

        except Exception as e:
            print(f"检查登录状态失败: {e}")
            return False

    async def check_login_status(self, wait_for_redirect: bool = True, timeout: int = 30) -> bool:
        """检查登录状态"""
        try:
            if not self.page:
                print("页面未初始化")
                return False

            # 获取当前URL
            current_url = self.page.url
            print(f"检查登录状态，当前URL: {current_url}")

            # 如果已经跳转到平台页面，说明登录成功
            if "channels.weixin.qq.com/platform" in current_url and "login" not in current_url:
                print("✅ 检测到已登录视频号（URL已跳转到平台页面）")
                return True

            # 检查是否还在登录页面（更宽松的条件）
            if ("login" in current_url.lower() and "channels.weixin.qq.com" in current_url):
                # 首先尝试在主页面检查登录成功指示器
                success_indicators = [
                    ".scan-success",
                    ".login-success",
                    ".success",
                    "[class*='success']",
                    ".redirecting",
                    ".loading",
                    ".scan_success",
                    ".login_success",
                    "[class*='scan']",
                    "[class*='confirm']",
                    ".confirm",
                    ".confirmed"
                ]

                print(f"在主页面检查登录状态，URL: {current_url}")

                login_success_detected = False
                for selector in success_indicators:
                    try:
                        success_element = await self.page.query_selector(selector)
                        if success_element and await success_element.is_visible():
                            print(f"检测到登录成功指示器: {selector}")
                            login_success_detected = True
                            break
                    except:
                        continue

                # 如果主页面没有找到，尝试在iframe中检查
                # if not login_success_detected:
                #     try:
                #         # 尝试多个可能的iframe选择器
                #         iframe_selectors = [
                #             'iframe[src*="login-for-iframe"]',
                #             'iframe[src*="login"]',
                #             'iframe',  # 最宽泛的选择器
                #         ]

                #         iframe_element = None
                #         for iframe_selector in iframe_selectors:
                #             iframe_element = await self.page.query_selector(iframe_selector)
                #             if iframe_element:
                #                 print(f"找到iframe: {iframe_selector}")
                #                 break

                #         if iframe_element:
                #             iframe_content = await iframe_element.content_frame()
                #             if iframe_content:
                #                 print("在iframe中检查登录状态...")

                #                 # 首先检查iframe的URL是否发生变化
                #                 try:
                #                     iframe_url = iframe_content.url
                #                     print(f"iframe URL: {iframe_url}")
                #                     if ("platform" in iframe_url or
                #                         "success" in iframe_url or
                #                         ("channels.weixin.qq.com" in iframe_url and "login" not in iframe_url)):
                #                         print("✅ iframe URL显示登录成功")
                #                         login_success_detected = True
                #                 except Exception as url_e:
                #                     print(f"获取iframe URL失败: {url_e}")

                #                 # 如果URL没有变化，检查页面元素
                #                 if not login_success_detected:
                #                     for selector in success_indicators:
                #                         try:
                #                             success_element = await iframe_content.query_selector(selector)
                #                             if success_element and await success_element.is_visible():
                #                                 print(f"✅ 在iframe中检测到登录成功指示器: {selector}")
                #                                 login_success_detected = True
                #                                 break
                #                         except Exception as sel_e:
                #                             print(f"检查选择器 {selector} 失败: {sel_e}")
                #                             continue

                #                 # 检查iframe中是否有特定的文本内容
                #                 if not login_success_detected:
                #                     try:
                #                         success_texts = ["登录成功", "扫码成功", "确认登录", "success", "confirmed"]
                #                         for text in success_texts:
                #                             if await iframe_content.locator(f"text={text}").count() > 0:
                #                                 print(f"✅ 在iframe中发现登录成功文本: {text}")
                #                                 login_success_detected = True
                #                                 break
                #                     except Exception as text_e:
                #                         print(f"检查文本内容失败: {text_e}")
                #     except Exception as e:
                #         print(f"检查iframe登录状态时出错: {e}")

                # # 如果检测到登录成功指示器，等待页面跳转
                # if login_success_detected and wait_for_redirect:
                #     print("等待页面跳转到平台页面...")
                #     try:
                #         # 等待跳转到平台页面
                #         await self.page.wait_for_url("**/platform/**", timeout=timeout * 1000)
                #         print("成功跳转到视频号平台页面")
                #         return True
                #     except Exception as e:
                #         print(f"等待跳转超时: {e}")
                #         # 检查当前URL是否已经是平台页面
                #         try:
                #             current_url = self.page.url
                #             if "channels.weixin.qq.com/platform/" in current_url:
                #                 print("当前已在平台页面，登录成功")
                #                 return True
                #         except Exception as url_e:
                #             print(f"获取当前URL失败: {url_e}")

                #         # 如果已经检测到登录成功但跳转失败，仍然认为登录成功
                #         # 因为有些情况下页面可能被关闭但登录状态已保存
                #         print("虽然跳转失败，但已检测到登录成功指示器，认为登录成功")
                #         return True

                return login_success_detected

            return False

        except Exception as e:
            print(f"检查登录状态失败: {e}")
            return False

    async def save_login_state(self) -> bool:
        """保存登录状态到文件"""
        try:
            if not self.context:
                print("上下文未初始化，无法保存登录状态")
                return False

            # 获取存储状态
            storage_state = await self.context.storage_state()

            # 保存到文件
            state_file = os.path.join(self.user_data_dir, "login_state.json")
            state_data = {
                "storage_state": storage_state,
                "saved_at": datetime.now().isoformat(),
                "account_id": self.account_id,
                "platform": "wechat_channels"
            }

            with open(state_file, 'w', encoding='utf-8') as f:
                json.dump(state_data, f, ensure_ascii=False, indent=2)

            print(f"视频号登录状态已保存到: {state_file}")
            return True

        except Exception as e:
            print(f"保存视频号登录状态失败: {e}")
            return False

    async def load_login_state(self) -> bool:
        """从文件加载登录状态"""
        try:
            state_file = os.path.join(self.user_data_dir, "login_state.json")

            if not os.path.exists(state_file):
                print("未找到保存的视频号登录状态文件")
                return False

            with open(state_file, 'r', encoding='utf-8') as f:
                state_data = json.load(f)

            # 检查状态文件是否过期（7天）
            # saved_at = datetime.fromisoformat(state_data.get("saved_at", ""))
            # if (datetime.now() - saved_at).days > 7:
            #     print("视频号登录状态文件已过期")
            #     os.remove(state_file)
            #     return False

            # 使用全局浏览器管理器创建隔离上下文
            self.context = await browser_manager.create_context(
                storage_state=state_data["storage_state"],
                accept_downloads=True,
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
                viewport={"width": 1920, "height": 1080},
            )

            # 创建页面
            self.page = await browser_manager.open_page(self.context)

            print("视频号登录状态加载成功")
            return True

        except Exception as e:
            print(f"加载视频号登录状态失败: {e}")
            return False

    async def get_cookies(self) -> Optional[str]:
        """获取当前页面的cookies"""
        try:
            if not self.context:
                return None

            cookies = await self.context.cookies()
            return json.dumps(cookies)

        except Exception as e:
            print(f"获取视频号cookies失败: {e}")
            return None

    async def download_single_video_data(self, start_date: str, end_date: str, auto_import: bool = True) -> Optional[bytes]:
        """下载视频号单篇视频数据Excel文件

        Args:
            start_date: 开始日期，格式: YYYY-MM-DD
            end_date: 结束日期，格式: YYYY-MM-DD
            auto_import: 是否自动导入数据到数据库，默认True

        Returns:
            Excel文件的二进制数据，失败返回None
        """
        try:
            if not self.page:
                print("页面未初始化，无法下载数据")
                return None

            # 检查网络连接
            # if not await self._check_network_connectivity():
            #     print("网络连接异常，无法继续下载")
            #     return None

            # 检查登录状态
            if not await self.check_existing_login():
                print("视频号登录状态无效，无法下载数据")
                # 更新数据库中的登录状态
                await self._update_database_login_status(False)
                # 抛出登录过期异常，让上级调用者停止后续尝试
                raise LoginExpiredException(
                    platform="wechat_channels",
                    account_id=self.account_id,
                    message="视频号登录状态已过期，无法下载单篇视频数据"
                )

            print(f"开始下载视频号单篇视频数据: {start_date} 到 {end_date}")

            # 转换日期为unix时间戳
            start_timestamp = self._date_to_timestamp(start_date)
            end_timestamp = self._date_to_timestamp(end_date, end_of_day=True)

            if not start_timestamp or not end_timestamp:
                print("日期格式错误")
                return None

            # 设置请求拦截
            downloaded_data = None

            async def handle_request(route):
                nonlocal downloaded_data
                request = route.request
                # 只拦截特定的视频号数据下载API
                if ("/micro/statistic/cgi-bin/mmfinderassistant-bin/statistic/download_post_data" in request.url and
                    request.method == "POST"):
                    print(f"拦截到视频号数据下载请求: {request.url}")
                    try:
                        # 获取原始请求数据
                        original_data = request.post_data_json if request.post_data_json else {}
                        print(f"原始请求数据: {original_data}")

                        # 替换时间参数
                        modified_data = {
                            **original_data,
                            "startTime": start_timestamp,
                            "endTime": end_timestamp
                        }
                        print(f"修改后请求数据: {modified_data}")

                        # 继续修改后的请求
                        await route.continue_(post_data=json.dumps(modified_data))
                    except Exception as e:
                        print(f"处理请求拦截时出错: {e}")
                        await route.continue_()
                else:
                    await route.continue_()

            async def handle_response(response):
                nonlocal downloaded_data
                # 拦截视频号数据下载API的响应
                if ("/micro/statistic/cgi-bin/mmfinderassistant-bin/statistic/download_post_data" in response.url and
                    response.status == 200):
                    print(f"拦截到视频号数据响应: {response.url}")
                    try:
                        # 获取JSON响应数据
                        response_text = await response.text()
                        response_data = json.loads(response_text)

                        # 检查响应是否成功
                        if response_data.get("errCode") == 0:
                            print("✅ API请求成功，页面将自动生成Excel文件")
                            print("等待浏览器下载事件...")
                        else:
                            print(f"❌ API请求失败，错误码: {response_data.get('errCode')}")

                    except Exception as e:
                        print(f"处理响应拦截时出错: {e}")

            # 设置下载事件监听
            async def handle_download(download):
                nonlocal downloaded_data
                print(f"检测到下载事件: {download.suggested_filename}")
                try:
                    # 将下载保存到稳定目录
                    downloads_dir = self._get_downloads_dir()
                    ts = datetime.now().strftime("%Y%m%d_%H%M%S")
                    name, ext = os.path.splitext(download.suggested_filename or 'wechat_channels_single_video.xlsx')
                    target_path = os.path.join(downloads_dir, f"{name}_{ts}{ext}")

                    await download.save_as(target_path)
                    print(f"✅ 文件已保存到: {target_path}")

                    # 读取保存的文件
                    try:
                        with open(target_path, 'rb') as f:
                            downloaded_data = f.read()
                        print(f"✅ 成功获取下载文件，大小: {len(downloaded_data)} bytes")
                    except Exception as read_e:
                        print(f"读取保存文件失败: {read_e}")
                except Exception as e:
                    print(f"处理下载事件时出错: {e}")
                except Exception as e:
                    print(f"处理下载事件时出错: {e}")

            # 启用请求和响应拦截
            await self.page.route("**/*", handle_request)
            self.page.on("response", handle_response)

            # 启用下载事件监听
            self.page.on("download", handle_download)

            # 导航到数据统计页面并执行下载操作
            success = await self._navigate_and_download()

            if success and downloaded_data:
                print(f"视频号数据下载成功，文件大小: {len(downloaded_data)} bytes")

                # 自动导入数据到数据库
                if auto_import and self.account_id:
                    await self._import_excel_to_database(downloaded_data, 'single_video')

                return downloaded_data
            else:
                print("视频号数据下载失败")
                return None

        except Exception as e:
            print(f"下载视频号数据失败: {e}")
            return None
        finally:
            # 清理拦截器
            try:
                await self.page.unroute("**/*")
            except:
                pass

    async def _import_excel_to_database(self, excel_content: bytes, data_type: str):
        """将Excel数据导入到数据库"""
        try:
            # 导入数据明细服务
            from app.services.data_details_service import DataDetailsService
            from app.database import SessionLocal

            # 创建数据库会话
            db = SessionLocal()

            try:
                # 调用数据导入服务
                result = DataDetailsService.import_excel_data(
                    db=db,
                    account_id=self.account_id,
                    data_type=data_type,
                    excel_content=excel_content
                )

                if result["success"]:
                    print(f"视频号数据导入成功: 新增 {result['imported_count']} 条，更新 {result['updated_count']} 条")
                else:
                    print(f"视频号数据导入失败: {result['error']}")

            finally:
                db.close()

        except Exception as e:
            print(f"视频号数据导入过程中发生错误: {e}")



    def _date_to_timestamp(self, date_str: str, end_of_day: bool = False) -> Optional[int]:
        """将日期字符串转换为unix时间戳

        Args:
            date_str: 日期字符串，格式: YYYY-MM-DD
            end_of_day: 是否转换为当天结束时间

        Returns:
            unix时间戳（秒），失败返回None
        """
        try:
            from datetime import datetime
            dt = datetime.strptime(date_str, "%Y-%m-%d")
            if end_of_day:
                # 设置为当天23:59:59
                dt = dt.replace(hour=23, minute=59, second=59)
            # 转换为秒级时间戳
            return int(dt.timestamp())
        except Exception as e:
            print(f"日期转换失败: {e}")
            return None

    async def _navigate_and_download(self) -> bool:
        """导航到数据统计页面并执行下载操作

        Returns:
            操作是否成功
        """
        try:
            # 页面选择器定义
            selectors = {
                'menu_data_stats': '#menuBar > li:nth-of-type(7) > a > span',
                'submenu_item': 'li:nth-of-type(7) > ul > li:nth-of-type(2) > a > span',
                'single_video_tab': 'wujie-app li:nth-of-type(2) > a',
                'date_range_selector': 'wujie-app div.filter-wrap > div:nth-of-type(2) label:nth-of-type(3)',
                'download_button': 'wujie-app div.filter-extra > a'
            }

            print("导航到视频号平台页面...")
            timeout = WeChatChannelsConfig.get_timeout('navigation', False)

            try:
                await self.page.goto(
                    "https://channels.weixin.qq.com/platform",
                    wait_until="domcontentloaded",
                    timeout=timeout
                )
                await asyncio.sleep(3)
                print(f"✅ 成功导航到: {self.page.url}")
            except Exception as nav_error:
                print(f"❌ 导航失败: {nav_error}")
                # 尝试更宽松的导航，使用更长的超时时间
                backup_timeout = timeout + 30000  # 额外30秒
                try:
                    await self.page.goto(
                        "https://channels.weixin.qq.com/platform",
                        wait_until="load",
                        timeout=backup_timeout
                    )
                    await asyncio.sleep(5)
                    print(f"✅ 备用方式导航成功: {self.page.url}")
                except Exception as backup_error:
                    print(f"❌ 备用导航也失败: {backup_error}")
                    return False

            print("点击数据统计菜单...")
            await self._click_element_with_retry(selectors['menu_data_stats'], "数据统计菜单")
            await asyncio.sleep(1)

            print("点击子菜单项...")
            await self._click_element_with_retry(selectors['submenu_item'], "子菜单项")
            await asyncio.sleep(2)

            print("点击单篇视频选项卡...")
            await self._click_element_with_retry(selectors['single_video_tab'], "单篇视频选项卡")
            await asyncio.sleep(2)

            print("设置日期范围...")
            await self._click_element_with_retry(selectors['date_range_selector'], "日期范围选择器")
            await asyncio.sleep(1)

            print("点击下载表格按钮...")
            await self._click_element_with_retry(selectors['download_button'], "下载表格按钮")

            # 等待下载完成
            print("等待下载完成...")
            await asyncio.sleep(5)

            return True

        except Exception as e:
            print(f"导航和下载操作失败: {e}")
            return False

    async def _click_element_with_retry(self, selector: str, element_name: str, max_retries: int = 3) -> bool:
        """带重试的元素点击

        Args:
            selector: 元素选择器
            element_name: 元素名称（用于日志）
            max_retries: 最大重试次数

        Returns:
            点击是否成功
        """
        for attempt in range(max_retries):
            try:
                print(f"尝试点击{element_name} (第 {attempt + 1}/{max_retries} 次)")

                # 等待元素可见
                await self.page.wait_for_selector(selector, state="visible", timeout=10000)

                # 点击元素
                await self.page.click(selector)
                print(f"✅ 成功点击{element_name}")
                return True

            except Exception as e:
                print(f"❌ 点击{element_name}失败 (第 {attempt + 1} 次): {e}")
                if attempt < max_retries - 1:
                    print("等待2秒后重试...")
                    await asyncio.sleep(2)
                else:
                    print(f"所有重试都失败了，无法点击{element_name}")
                    return False

        return False

    async def close(self):
        """关闭浏览器资源"""
        # 分别处理每个资源的关闭，避免一个失败影响其他资源的清理

        # 关闭页面
        if self.page:
            try:
                # 先移除所有事件监听器，避免关闭时的错误
                try:
                    await self.page.remove_all_listeners()
                except Exception:
                    pass
                # 等待一下让正在进行的请求完成
                import asyncio
                await asyncio.sleep(0.5)
                await self.page.close()
                print("✅ 页面已关闭")
            except Exception as e:
                print(f"⚠️ 关闭页面时出错: {e}")
            finally:
                self.page = None

        # 关闭上下文（通过全局管理器以正确释放并发信号量）
        if self.context:
            try:
                await browser_manager.close_context(self.context)
                print("✅ 上下文已关闭")
            except Exception as e:
                print(f"⚠️ 关闭上下文时出错: {e}")
            finally:
                self.context = None

        # 不再在服务内关闭全局浏览器与Playwright，由 BrowserManager 统一管理

    async def _update_database_login_status(self, is_logged_in: bool):
        """更新数据库中的登录状态"""
        try:
            from app.database import SessionLocal
            from app.models import PlatformAccount
            from datetime import datetime

            db = SessionLocal()
            try:
                account = db.query(PlatformAccount).filter(PlatformAccount.id == self.account_id).first()
                if account:
                    account.login_status = is_logged_in
                    if is_logged_in:
                        account.last_login_time = datetime.now()
                        # 获取并保存cookies
                        try:
                            cookies = await self.get_cookies()
                            account.cookies = cookies
                        except Exception as e:
                            print(f"获取cookies失败: {e}")
                    else:
                        account.cookies = None

                    db.commit()
                    print(f"已更新账号 {self.account_id} 的数据库登录状态: {is_logged_in}")
                else:
                    print(f"未找到账号 {self.account_id}")
            finally:
                db.close()

        except Exception as e:
            print(f"更新数据库登录状态失败: {e}")

    async def download_follower_data(self, auto_import: bool = True) -> Optional[bytes]:
        """下载关注者数据（参考download_single_video_data实现）

        Args:
            auto_import: 是否自动导入数据到数据库，默认True

        Returns:
            CSV文件的二进制数据，失败返回None
        """
        try:
            # 使用全局浏览器管理器创建上下文与页面
            if not self.context:
                self.context = await browser_manager.create_context(
                    accept_downloads=True,
                    user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
                    viewport={"width": 1920, "height": 1080},
                )

            if not self.page:
                self.page = await browser_manager.open_page(self.context)

            print("正在访问关注者数据页面...")
            # 导航到关注者页面
            await self.page.goto(
                "https://channels.weixin.qq.com/platform/statistic/follower",
                wait_until="domcontentloaded",
                timeout=10000
            )

            # 检查登录状态
            if not await self.check_login_status():
                print("视频号登录状态无效，无法下载关注者数据")
                # 更新数据库中的登录状态
                await self._update_database_login_status(False)
                # 抛出登录过期异常，让上级调用者停止后续尝试
                raise LoginExpiredException(
                    platform="wechat_channels",
                    account_id=self.account_id,
                    message="视频号登录状态已过期，无法下载关注者数据"
                )

            print("✅ 成功访问关注者数据页面")

            # 等待页面加载
            await asyncio.sleep(5)

            # 设置下载监听器
            download_data = None
            download_event = asyncio.Event()

            async def handle_download(download):
                nonlocal download_data
                try:
                    print(f"检测到下载事件: {download.suggested_filename}")

                    # 等待下载完成，并保存到稳定目录
                    downloads_dir = self._get_downloads_dir()
                    # 加时间戳避免重名
                    ts = datetime.now().strftime("%Y%m%d_%H%M%S")
                    name, ext = os.path.splitext(download.suggested_filename or 'wechat_channels_follower.csv')
                    target_path = os.path.join(downloads_dir, f"{name}_{ts}{ext}")

                    await download.save_as(target_path)
                    print(f"✅ 文件已保存到: {target_path}")

                    # 读取文件内容（用于后续内存内处理，不会删除文件）
                    try:
                        with open(target_path, 'rb') as f:
                            download_data = f.read()
                        print(f"✅ 成功获取下载文件，大小: {len(download_data)} bytes")
                    except Exception as read_e:
                        print(f"读取保存文件失败: {read_e}")

                    # 如果需要自动导入，解析并导入数据
                    if auto_import and download_data:
                        await self._import_follower_from_bytes(download_data)

                    download_event.set()
                except Exception as e:
                    print(f"处理下载失败: {e}")
                    download_event.set()

            # 监听下载事件
            self.page.on("download", handle_download)

            # 点击近30天选项
            print("正在切换到近30天数据...")
            span_selector = "#container-wrap > div.container-center > div > div > div.follower-growth-wrap > div:nth-child(4) > div > div > div.card-body > div.filter-wrap > div > div.filter-content > div > div > div.weui-desktop-radio-group.radio-group > label:nth-child(2) > span"

            try:
                span_element = await self.page.wait_for_selector(span_selector, timeout=10000)
                if span_element:
                    await span_element.click()
                    print("✅ 成功切换到近30天数据")
                    await asyncio.sleep(3)  # 等待数据加载
                else:
                    print("❌ 未找到近30天选项")
                    return None
            except Exception as e:
                print(f"切换到近30天失败: {e}")
                return None

            # 点击下载按钮
            print("正在点击下载按钮...")
            download_selector = "#container-wrap > div.container-center > div > div > div.follower-growth-wrap > div:nth-child(4) > div > div > div.card-body > div.filter-wrap > div > div.filter-extra > a"

            try:
                download_button = await self.page.wait_for_selector(download_selector, timeout=10000)
                if download_button:
                    await download_button.click()
                    print("✅ 成功点击下载按钮")

                    # 等待下载完成
                    try:
                        await asyncio.wait_for(download_event.wait(), timeout=30.0)
                        if download_data:
                            print("✅ 关注者数据下载成功")
                            return download_data
                        else:
                            print("❌ 下载数据为空")
                            return None
                    except asyncio.TimeoutError:
                        print("❌ 下载超时")
                        return None
                else:
                    print("❌ 未找到下载按钮")
                    return None
            except Exception as e:
                print(f"点击下载按钮失败: {e}")
                return None

        except Exception as e:
            print(f"下载关注者数据失败: {e}")
            return None
        finally:
            try:
                if hasattr(self, 'page') and self.page:
                    self.page.remove_all_listeners("download")
            except:
                pass

    async def get_follower_data(self, auto_import: bool = True) -> Optional[bytes]:
        """获取关注者数据

        Args:
            auto_import: 是否自动导入数据到数据库，默认True

        Returns:
            CSV文件的二进制数据，失败返回None
        """
        if not self.page:
            print("页面未初始化，无法下载关注者数据")
            return None

        # 检查网络连接
        # if not await self._check_network_connectivity():
        #     print("网络连接异常，无法继续下载")
        #     return None

        # 检查登录状态
        if not await self.check_login_status():
            print("视频号登录状态无效，无法下载关注者数据")
            # 抛出登录过期异常，让上级调用者停止后续尝试
            raise LoginExpiredException(
                platform="wechat_channels",
                account_id=self.account_id,
                message="视频号登录状态已过期，无法获取关注者数据"
            )

        max_retries = 2
        for attempt in range(max_retries):
            try:
                print(f"开始获取关注者数据 (第 {attempt + 1}/{max_retries} 次)")

                # 使用全局浏览器管理器创建上下文与页面
                if not self.context:
                    self.context = await browser_manager.create_context(
                        accept_downloads=True,
                        user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
                        viewport={"width": 1920, "height": 1080},
                    )

                if not self.page:
                    self.page = await browser_manager.open_page(self.context)

                # # 检查网络连接
                # if not await self._check_network_connectivity():
                #     print("网络连接异常，无法继续获取关注者数据")
                #     if attempt < max_retries - 1:
                #         print("等待10秒后重试...")
                #         await asyncio.sleep(10)
                #         continue
                #     else:
                #         return None

                # 检查登录状态
                # print("检查登录状态...")
                # if not await self.check_login_status(wait_for_redirect=False, timeout=15):
                #     print("账号未登录，无法获取关注者数据")
                #     if attempt < max_retries - 1:
                #         print("等待5秒后重试...")
                #         await asyncio.sleep(5)
                #         continue
                #     else:
                #         return None

                print("正在访问关注者数据页面...")
                # 使用配置的导航超时时间，并添加重试机制
                navigation_timeout = WeChatChannelsConfig.get_timeout('navigation', False)
                print(f"使用导航超时设置: {navigation_timeout}ms")

                max_nav_retries = 1
                for nav_attempt in range(max_nav_retries):
                    try:
                        await self.page.goto(
                            "https://channels.weixin.qq.com/platform/statistic/follower",
                            wait_until="domcontentloaded",  # 改为更快的等待策略
                            timeout=navigation_timeout
                        )
                        print(f"✅ 成功导航到关注者数据页面: {self.page.url}")
                        break
                    except Exception as nav_error:
                        print(f"导航失败 (第 {nav_attempt + 1}/{max_nav_retries} 次): {nav_error}")
                        if nav_attempt < max_nav_retries - 1:
                            print("等待3秒后重试...")
                            await asyncio.sleep(3)
                        else:
                            print("导航到关注者页面失败，尝试备用方案...")
                            # 备用方案：使用更宽松的等待策略
                            try:
                                await self.page.goto(
                                    "https://channels.weixin.qq.com/platform/statistic/follower",
                                    wait_until="load",
                                    timeout=navigation_timeout
                                )
                                print("✅ 备用方案导航成功")
                            except Exception as backup_error:
                                print(f"备用方案也失败: {backup_error}")
                                raise nav_error

                # 等待页面加载完成
                await asyncio.sleep(3)

                print("正在监听API请求...")
                api_data = None

                # 设置请求监听器（只监听一次）
                api_data_received = False
                async def handle_response(response):
                    nonlocal api_data, api_data_received
                    if not api_data_received and "fans_trend" in response.url:
                        try:
                            json_data = await response.json()
                            if json_data.get('errCode') == 0:
                                api_data = json_data
                                api_data_received = True
                                print("✅ 成功获取API数据")
                        except Exception as e:
                            print(f"解析API响应失败: {e}")

                # 监听响应
                self.page.on("response", handle_response)

                print("正在切换到近30天数据...")

                # 点击近30天的单选按钮
                try:
                    print("正在尝试点击近30天选项...")

                    # 方法1: 使用精确的CSS选择器点击近30天选项
                    try:
                        # 使用精确的选择器定位近30天选项的span元素
                        span_selector = "#container-wrap > div.container-center > div > div > div.follower-growth-wrap > div:nth-child(4) > div > div > div.card-body > div.filter-wrap > div > div.filter-content > div > div > div.weui-desktop-radio-group.radio-group > label:nth-child(2) > span"
                        span_element = await self.page.wait_for_selector(span_selector, timeout=10000)

                        if span_element:
                            await span_element.click()
                            print("✅ 通过精确选择器成功切换到近30天数据")
                            await asyncio.sleep(3)  # 等待数据加载
                        else:
                            raise Exception("未找到近30天span元素")

                    except Exception as span_error:
                        print(f"精确选择器点击失败: {span_error}")

                        # 方法2: 使用备用选择器
                        try:
                            # 尝试其他可能的选择器
                            alternative_selectors = [
                                'span.weui-desktop-form__check-content:has-text("近30天")',
                                'span:has-text("近30天")',
                                '.weui-desktop-form__check-content:has-text("近30天")',
                                'text=近30天',
                                '.weui-desktop-radio-group label:nth-child(2) span'
                            ]

                            clicked = False
                            for selector in alternative_selectors:
                                try:
                                    element = await self.page.wait_for_selector(selector, timeout=2000)
                                    if element:
                                        await element.click()
                                        print(f"✅ 通过选择器 {selector} 成功点击")
                                        clicked = True
                                        break
                                except:
                                    continue

                            if not clicked:
                                raise Exception("所有选择器都失败了")
                            else:
                                await asyncio.sleep(3)

                        except Exception as alt_error:
                            print(f"备用选择器失败: {alt_error}")
                            if attempt < max_retries - 1:
                                continue
                            else:
                                return None

                except Exception as e:
                    print(f"切换到近30天数据失败: {e}")
                    if attempt < max_retries - 1:
                        continue
                    else:
                        return None

                # 等待API数据或触发刷新
                wait_time = 0
                while not api_data and wait_time < 30:
                    await asyncio.sleep(1)
                    wait_time += 1

                    # 如果等待10秒还没有数据，尝试刷新页面
                    if wait_time == 10:
                        print("尝试刷新页面获取数据...")
                        await self.page.reload(wait_until="domcontentloaded", timeout=30000)
                        await asyncio.sleep(3)

                if not api_data:
                    print("未能获取到API数据，尝试下载CSV文件...")

                # 下载CSV文件
                print("正在下载CSV文件...")
                csv_data = await self._download_follower_csv()

                if csv_data:
                    print(f"CSV数据获取成功，共 {len(csv_data)} 条记录")

                    # 自动导入数据到数据库
                    if auto_import:
                        await self._import_follower_to_database(csv_data)

                    return csv_data
                else:
                    print(f"第 {attempt + 1} 次尝试获取关注者数据失败")
                    if attempt < max_retries - 1:
                        print("等待10秒后重试...")
                        await asyncio.sleep(10)
                        continue
                    else:
                        print("所有尝试都失败，未能获取到关注者数据")
                        return None

            except Exception as e:
                print(f"获取关注者数据失败 (第 {attempt + 1} 次): {e}")
                if attempt < max_retries - 1:
                    print("等待10秒后重试...")
                    await asyncio.sleep(10)
                    continue
                else:
                    print("所有尝试都失败")
                    return None
            finally:
                try:
                    if hasattr(self, 'page') and self.page:
                        self.page.remove_all_listeners("response")
                except:
                    pass

        return None

    async def _download_follower_csv(self) -> Optional[List[Dict]]:
        """下载关注者CSV文件并解析"""
        try:
            # 查找下载按钮
            download_selector = "#container-wrap > div.container-center > div > div > div.follower-growth-wrap > div:nth-child(4) > div > div > div.card-body > div.filter-wrap > div > div.filter-extra > a"

            # 等待下载按钮出现
            download_button = await self.page.wait_for_selector(download_selector, timeout=10000)

            if not download_button:
                print("❌ 未找到下载按钮")
                return None

            print("找到下载按钮，准备下载...")

            # 使用稳定下载目录（账号隔离）
            download_dir = self._get_downloads_dir()

            # 设置下载监听器
            download_info = None

            async def handle_download(download):
                nonlocal download_info
                download_info = download
                print(f"开始下载文件: {download.suggested_filename}")

            self.page.on("download", handle_download)

            # 点击下载按钮
            await download_button.click()

            # 等待下载完成
            wait_time = 0
            while not download_info and wait_time < 30:
                await asyncio.sleep(1)
                wait_time += 1

            if not download_info:
                print("❌ 下载超时")
                return None

            # 保存文件（文件持久保留，不删除）
            ts = datetime.now().strftime("%Y%m%d_%H%M%S")
            name, ext = os.path.splitext(download_info.suggested_filename or 'wechat_channels_follower.csv')
            file_path = os.path.join(download_dir, f"{name}_{ts}{ext}")
            await download_info.save_as(file_path)
            print(f"✅ 文件下载完成: {file_path}")

            # 解析CSV文件
            csv_data = await self._parse_follower_csv(file_path)

            # 不再删除文件，保留在downloads目录
            return csv_data

        except Exception as e:
            print(f"下载CSV文件失败: {e}")
            return None
        finally:
            try:
                if hasattr(self, 'page') and self.page:
                    self.page.remove_all_listeners("download")
            except:
                pass

    async def _parse_follower_csv(self, file_path: str) -> Optional[List[Dict]]:
        """解析关注者CSV文件"""
        try:
            data = []

            # 保存文件到temp_downloads目录
            await self._save_csv_to_downloads(file_path)

            # 尝试不同的编码
            encodings = ['utf-8-sig', 'utf-8', 'gbk', 'gb2312']

            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as file:
                        # 跳过前2行标题和说明
                        for _ in range(2):
                            next(file, None)

                        # 读取剩余内容
                        csv_reader = csv.reader(file)

                        # 获取表头（第3行）
                        headers = next(csv_reader, None)
                        if not headers:
                            print("CSV文件没有表头")
                            continue

                        print(f"CSV表头: {headers}")

                        # 验证表头是否正确
                        if len(headers) < 5 or '时间' not in headers[0]:
                            print(f"表头格式不正确: {headers}")
                            continue

                        # 解析数据行
                        for row_index, row in enumerate(csv_reader):
                            if len(row) < len(headers):
                                continue  # 跳过不完整的行

                            # 创建字典
                            row_dict = dict(zip(headers, row))
                            parsed_row = {}

                            # 根据实际CSV列名进行映射
                            for key, value in row_dict.items():
                                if not value:
                                    continue

                                key = key.strip()
                                value = str(value).strip()

                                # 精确匹配CSV列名
                                if key == '时间':
                                    parsed_row['date'] = value
                                elif key == '净增关注':
                                    parsed_row['net_follower_increase'] = self._parse_number(value)
                                elif key == '新增关注':
                                    parsed_row['new_followers'] = self._parse_number(value)
                                elif key == '取消关注':
                                    parsed_row['unfollowers'] = self._parse_number(value)
                                elif key == '关注者总数':
                                    parsed_row['total_followers'] = self._parse_number(value)

                            if parsed_row.get('date'):
                                data.append(parsed_row)
                                print(f"解析数据行 {row_index + 1}: {parsed_row}")

                        break  # 成功解析，跳出编码循环

                except UnicodeDecodeError:
                    continue  # 尝试下一个编码
                except Exception as e:
                    print(f"使用编码 {encoding} 解析失败: {e}")
                    continue

            print(f"CSV解析完成，共 {len(data)} 条记录")
            return data if data else None

        except Exception as e:
            print(f"解析CSV文件失败: {e}")
            return None

    async def _save_csv_to_downloads(self, file_path: str):
        """保存CSV文件到temp_downloads目录"""
        try:
            import shutil

            # 使用稳定下载目录
            downloads_dir = self._get_downloads_dir()

            # 生成目标文件名
            filename = os.path.basename(file_path)
            if not filename:
                filename = "wechat_channels_follower_data.csv"

            # 添加时间戳避免重复
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            name, ext = os.path.splitext(filename)
            target_filename = f"{name}_{timestamp}{ext}"

            target_path = os.path.join(downloads_dir, target_filename)

            # 复制文件
            shutil.copy2(file_path, target_path)
            print(f"✅ CSV文件已保存到: {target_path}")

        except Exception as e:
            print(f"保存CSV文件到downloads目录失败: {e}")

    async def _save_csv_to_downloads_from_bytes(self, data: bytes, filename: str):
        """从字节数据保存CSV文件到temp_downloads目录"""
        try:
            import shutil

            # 使用稳定下载目录
            downloads_dir = self._get_downloads_dir()

            # 生成目标文件名
            if not filename:
                filename = "wechat_channels_follower_data.csv"

            # 添加时间戳避免重复
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            name, ext = os.path.splitext(filename)
            target_filename = f"{name}_{timestamp}{ext}"

            target_path = os.path.join(downloads_dir, target_filename)

            # 写入文件
            with open(target_path, 'wb') as f:
                f.write(data)
            print(f"✅ CSV文件已保存到: {target_path}")

        except Exception as e:
            print(f"保存CSV文件到downloads目录失败: {e}")

    async def _import_follower_from_bytes(self, data: bytes):
        """从字节数据导入关注者数据到数据库"""
        try:
            import tempfile

            # 创建临时文件
            with tempfile.NamedTemporaryFile(mode='wb', suffix='.csv', delete=False) as temp_file:
                temp_file.write(data)
                temp_path = temp_file.name

            # 解析CSV并导入数据库
            csv_data = await self._parse_follower_csv(temp_path)
            if csv_data:
                await self._import_follower_to_database(csv_data)

            # 清理临时文件
            try:
                os.remove(temp_path)
            except:
                pass

        except Exception as e:
            print(f"从字节数据导入关注者数据失败: {e}")

    def _parse_number(self, value: str) -> int:
        """解析数字字符串"""
        try:
            # 移除逗号和其他非数字字符
            cleaned = ''.join(c for c in str(value) if c.isdigit() or c == '-')
            return int(cleaned) if cleaned else 0
        except:
            return 0

    async def _import_follower_to_database(self, follower_data: List[Dict]):
        """将关注者数据导入到数据库"""
        try:
            from app.database import SessionLocal
            from app.models import WeChatChannelsFollowerData
            from datetime import datetime, timezone, date

            # 创建数据库会话
            db = SessionLocal()

            try:
                imported_count = 0
                updated_count = 0

                for item in follower_data:
                    # 解析日期
                    date_str = item.get('date', '').strip()
                    if not date_str:
                        continue

                    try:
                        # 尝试不同的日期格式
                        if '-' in date_str:
                            item_date = datetime.strptime(date_str, '%Y-%m-%d').date()
                        elif '/' in date_str:
                            item_date = datetime.strptime(date_str, '%Y/%m/%d').date()
                        elif '年' in date_str and '月' in date_str and '日' in date_str:
                            # 处理中文日期格式，如：2024年1月1日
                            date_str = date_str.replace('年', '-').replace('月', '-').replace('日', '')
                            item_date = datetime.strptime(date_str, '%Y-%m-%d').date()
                        else:
                            continue  # 跳过无法解析的日期
                    except Exception as e:
                        print(f"日期解析失败: {date_str}, 错误: {e}")
                        continue

                    # 检查是否已存在
                    existing = db.query(WeChatChannelsFollowerData).filter(
                        WeChatChannelsFollowerData.account_id == self.account_id,
                        WeChatChannelsFollowerData.date == item_date
                    ).first()

                    if existing:
                        # 更新现有记录
                        existing.net_follower_increase = item.get('net_follower_increase', 0)
                        existing.new_followers = item.get('new_followers', 0)
                        existing.unfollowers = item.get('unfollowers', 0)
                        existing.total_followers = item.get('total_followers', 0)
                        existing.updated_at = datetime.now(timezone.utc)
                        updated_count += 1
                    else:
                        # 创建新记录
                        record = WeChatChannelsFollowerData(
                            account_id=self.account_id,
                            date=item_date,
                            net_follower_increase=item.get('net_follower_increase', 0),
                            new_followers=item.get('new_followers', 0),
                            unfollowers=item.get('unfollowers', 0),
                            total_followers=item.get('total_followers', 0)
                        )
                        db.add(record)
                        imported_count += 1

                db.commit()
                print(f"关注者数据导入成功: 新增 {imported_count} 条，更新 {updated_count} 条")

            finally:
                db.close()

        except Exception as e:
            print(f"关注者数据导入失败: {e}")

    # 统一接口方法
    async def download_single_data_type(
        self,
        data_type: str,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        auto_import: bool = True,
        **kwargs
    ) -> DataDownloadResult:
        """视频号统一数据下载入口"""
        try:
            if data_type == 'single_video':
                if not start_date or not end_date:
                    return DataDownloadResult(
                        success=False,
                        error_message="单篇视频数据下载需要提供开始和结束日期"
                    )

                result = await self.download_single_video_data(
                    start_date=start_date,
                    end_date=end_date,
                    auto_import=auto_import
                )
                return DataDownloadResult(
                    success=result is not None,
                    data=result,
                    error_message=None if result else "下载失败"
                )

            elif data_type == 'follower_data':
                result = await self.download_follower_data(
                    auto_import=auto_import
                )
                return DataDownloadResult(
                    success=result is not None,
                    data=result,
                    error_message=None if result else "下载失败"
                )

            else:
                return DataDownloadResult(
                    success=False,
                    error_message=f"不支持的数据类型: {data_type}"
                )

        except LoginExpiredException as e:
            return DataDownloadResult(
                success=False,
                error_message=f"登录已过期: {e.message}"
            )
        except Exception as e:
            return DataDownloadResult(
                success=False,
                error_message=f"下载失败: {str(e)}"
            )

    def get_supported_data_types(self) -> List[Tuple[str, str]]:
        """获取支持的数据类型"""
        return [
            ('single_video', '单篇视频数据'),
            ('follower_data', '关注者数据')
        ]
