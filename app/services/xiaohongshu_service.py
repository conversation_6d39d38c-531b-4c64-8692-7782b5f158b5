import asyncio
import base64
import json
import re
import os
import time
from datetime import datetime, timed<PERSON>ta
from playwright.async_api import async_playwright, <PERSON>, Browser, BrowserContext
from app.services.browser_manager import browser_manager
from typing import Optional, Dict, Any, List, Tuple
from app.services.platform_service_base import PlatformServiceBase, DataDownloadResult

class XiaohongshuService(PlatformServiceBase):
    """小红书创作者平台服务类"""

    # 数据下载模板配置
    DOWNLOAD_TEMPLATES = {
        'note_data': {
            'name': '笔记数据',
            'data_start_row': 3,  # Excel文件数据从第3行开始（第1行是说明，第2行是表头）
            'fields': [
                ('笔记标题', 1),          # 文本
                ('首次发布时间', 1),      # 文本/日期
                ('体裁', 1),              # 文本
                ('观看量', 2),            # 数字
                ('点赞', 2),              # 数字
                ('评论', 2),              # 数字
                ('收藏', 2),              # 数字
                ('涨粉', 2),              # 数字
                ('分享', 2),              # 数字
                ('人均观看时长', 2),      # 数字（秒）
                ('弹幕', 2),              # 数字
            ]
        }
    }

    def __init__(self, account_id: Optional[int] = None, headless: bool = True):
        super().__init__(account_id)
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        self.playwright = None
        self.headless = headless
        self.user_data_dir = self._get_user_data_dir()
        # 登录二维码缓存
        self._last_qr_base64: Optional[str] = None
        self._last_qr_timestamp: Optional[float] = None


    def _get_user_data_dir(self) -> str:
        """获取用户数据目录路径"""
        if self.account_id:
            # 为每个账号创建独立的用户数据目录
            base_dir = os.path.join(os.getcwd(), "user_data")
            user_dir = os.path.join(base_dir, f"xiaohongshu_account_{self.account_id}")
        else:
            # 默认目录
            user_dir = os.path.join(os.getcwd(), "user_data", "xiaohongshu_default")

        # 确保目录存在
        os.makedirs(user_dir, exist_ok=True)
        return user_dir

    def _get_download_config(self, data_type: str) -> Optional[Dict[str, Any]]:
        """获取下载配置

        Args:
            data_type: 数据类型

        Returns:
            下载配置字典
        """
        return self.DOWNLOAD_TEMPLATES.get(data_type)

    async def _init_browser(self):
        """保持兼容的空实现：已改为使用全局 BrowserManager"""
        return

    async def _create_persistent_context(self) -> BrowserContext:
        """创建持久化上下文（通过全局 BrowserManager）"""
        return await browser_manager.create_context(
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
            viewport={"width": 1920, "height": 1080},
            accept_downloads=True,
        )

    async def _start_browser(self):
        """启动浏览器并创建页面（复用已存在的上下文与页面，不在此处重复加载登录状态）"""
        try:
            print("_start_browser: 开始，context存在:" , bool(self.context), " page存在:", bool(self.page))
            # 初始化浏览器（兼容空实现）
            await self._init_browser()

            # 仅当不存在时创建上下文/页面
            if not self.context:
                self.context = await self._create_persistent_context()
                print("_start_browser: 已创建新context")

            if not self.page:
                self.page = await browser_manager.open_page(self.context)
                print("_start_browser: 已创建新page")

            print("_start_browser: 完成")
        except Exception as e:
            print(f"启动浏览器失败: {e}")
            raise

    async def get_login_qrcode(self) -> Optional[str]:
        """获取小红书登录二维码"""
        try:
            # 首先尝试加载已保存的登录状态
            if await self.load_login_state():
                # 检查是否已经登录
                if await self.check_existing_login():
                    print("检测到已有有效的登录状态")
                    await self._update_database_login_status(True)
                    return "already_logged_in"
            # 若最近30秒内已有二维码缓存，直接返回
            if getattr(self, "_last_qr_base64", None) and getattr(self, "_last_qr_timestamp", None):
                if time.time() - self._last_qr_timestamp < 30:
                    print("返回最近缓存的小红书二维码（30秒内）")
                    return f"data:image/png;base64,{self._last_qr_base64}"

            # 如果没有有效的登录状态，清理并重新开始
            await self.close()

            # 使用全局浏览器管理器创建上下文与页面
            self.context = await self._create_persistent_context()
            self.page = await browser_manager.open_page(self.context)

            print("正在访问小红书创作者平台...")
            # 访问小红书创作者平台，会自动跳转到登录页
            await self.page.goto("https://creator.xiaohongshu.com", wait_until="domcontentloaded", timeout=60000)

            # 等待页面完全加载
            await asyncio.sleep(3)

            # 检查是否跳转到登录页面
            current_url = self.page.url
            print(f"当前URL: {current_url}")

            if "login" not in current_url:
                print("未跳转到登录页面，可能已经登录")
                return "already_logged_in"

            print("正在切换到扫码登录模式...")
            # 点击切换到扫码登录的按钮
            switch_button_selector = "#page > div > div.content > div.con > div.login-box-container > div > div > div > div > img"

            try:
                await self.page.wait_for_selector(switch_button_selector, state="visible", timeout=10000)
                await self.page.click(switch_button_selector)
                print("成功点击切换按钮")
                await asyncio.sleep(2)
            except Exception as e:
                print(f"点击切换按钮失败: {e}")
                # 可能已经在扫码模式，继续执行

            print("正在查找二维码...")
            # 使用正则匹配css-开头的类名来查找二维码
            qr_element = None

            # 等待二维码容器出现
            await asyncio.sleep(3)

            # 使用更精确的选择器匹配指定路径下的二维码
            # 首先尝试精确路径匹配
            qr_selector = "#page > div > div.content > div.con > div.login-box-container > div > div > div > div > div > div[class*='css-'] > img"

            try:
                qr_element = await self.page.query_selector(qr_selector)
                if qr_element:
                    src = await qr_element.get_attribute("src")
                    if src and src.startswith("data:image/"):
                        parent = await qr_element.query_selector("xpath=..")
                        if parent:
                            parent_class = await parent.get_attribute("class")
                            print(f"找到二维码元素，父元素类名: {parent_class}")
                    else:
                        qr_element = None
            except Exception as e:
                print(f"精确选择器匹配失败: {e}")
                qr_element = None

            # 如果精确匹配失败，使用备用方法
            if not qr_element:
                print("尝试备用匹配方法...")
                # 查找login-box-container内的所有img元素
                container_imgs = await self.page.query_selector_all("#page > div > div.content > div.con > div.login-box-container img")

                for img in container_imgs:
                    try:
                        # 获取img的src属性
                        src = await img.get_attribute("src")
                        if src and src.startswith("data:image/"):
                            # 检查父元素是否包含css-开头的类名
                            parent = await img.query_selector("xpath=..")
                            if parent:
                                parent_class = await parent.get_attribute("class")
                                if parent_class and re.search(r'css-\w+', parent_class):
                                    # 验证是否为二维码：检查图片尺寸和内容特征
                                    if await self._is_qrcode_image(img):
                                        print(f"备用方法找到二维码元素，父元素类名: {parent_class}")
                                        qr_element = img
                                        break
                                    else:
                                        print(f"跳过非二维码图片，父元素类名: {parent_class}")
                    except Exception as e:
                        continue

            # 最后的备用方法：如果还是没找到，直接查找base64格式的图片
            if not qr_element:
                print("所有方法都失败，尝试最后的备用方法...")
                img_elements = await self.page.query_selector_all("img[src^='data:image/']")
                for img in img_elements:
                    try:
                        # 验证是否为二维码
                        if await self._is_qrcode_image(img):
                            qr_element = img
                            print("最后备用方法找到二维码")
                            break
                        else:
                            print("跳过非二维码的base64图片")
                    except Exception as e:
                        continue

            if not qr_element:
                print("无法找到二维码元素")
                return None

            # 获取二维码的src属性（优先使用 data:image，兜底截图）
            qr_src = await qr_element.get_attribute("src")
            if qr_src and qr_src.startswith("data:image/"):
                b64 = qr_src.split(",", 1)[1]
            else:
                shot = await qr_element.screenshot()
                b64 = base64.b64encode(shot).decode()

            # 缓存二维码
            self._last_qr_base64 = b64
            self._last_qr_timestamp = time.time()

            print("✅ 成功获取小红书二维码")
            print("保持页面状态，等待用户扫码...")
            return f"data:image/png;base64,{b64}"

        except Exception as e:
            print(f"获取小红书二维码失败: {e}")
            await self.close()
            return None

    async def get_cached_or_rescreenshot_qrcode(self) -> Optional[str]:
        """返回最近缓存的二维码；若无缓存且页面仍在，则在当前页面重新截图。
        避免重复创建上下文导致阻塞。
        """
        if getattr(self, "_last_qr_base64", None) and getattr(self, "_last_qr_timestamp", None):
            if time.time() - self._last_qr_timestamp < 30:
                return f"data:image/png;base64,{self._last_qr_base64}"
        if self.page:
            try:
                await self.page.wait_for_load_state("domcontentloaded", timeout=15000)
                # 小红书二维码通常是 img[src^='data:image']
                el = await self.page.query_selector("img[src^='data:image']")
                if not el:
                    return None
                src = await el.get_attribute("src")
                if not src:
                    return None
                if src.startswith("data:image/"):
                    b64 = src.split(",",1)[1]
                else:
                    shot = await self.page.screenshot()
                    b64 = base64.b64encode(shot).decode()
                self._last_qr_base64 = b64
                self._last_qr_timestamp = time.time()
                return f"data:image/png;base64,{b64}"
            except Exception:
                return None
        return None

    async def refresh_qrcode(self) -> Optional[str]:
        """刷新二维码（当二维码过期时使用）"""
        try:
            if not self.page:
                print("页面未初始化，重新获取二维码")
                return await self.get_login_qrcode()

            print("正在刷新页面并重新获取小红书二维码...")
            # 刷新页面
            await self.page.reload(wait_until="domcontentloaded", timeout=30000)
            await asyncio.sleep(2)

            # 清除缓存的二维码，强制重新获取
            self._last_qr_base64 = None
            self._last_qr_timestamp = None

            # 小红书二维码通常是 img[src^='data:image']
            el = await self.page.query_selector("img[src^='data:image']")
            if not el:
                print("刷新后仍未找到小红书二维码元素")
                return None

            src = await el.get_attribute("src")
            if not src:
                print("刷新后二维码元素无src属性")
                return None

            if src.startswith("data:image/"):
                b64 = src.split(",", 1)[1]
            else:
                shot = await el.screenshot()
                b64 = base64.b64encode(shot).decode()

            # 缓存新的二维码
            self._last_qr_base64 = b64
            self._last_qr_timestamp = time.time()

            print(f"成功刷新小红书二维码")
            return f"data:image/png;base64,{b64}"

        except Exception as e:
            print(f"刷新小红书二维码失败: {e}")
            return None


    async def check_existing_login(self) -> bool:
        """检查现有登录状态是否仍然有效"""
        try:
            if not self.page:
                return False

            # 访问小红书创作者平台首页
            await self.page.goto("https://creator.xiaohongshu.com", wait_until="domcontentloaded", timeout=15000)
            await asyncio.sleep(2)

            # 检查是否已经登录（没有跳转到登录页）
            current_url = self.page.url
            if "login" not in current_url and "creator.xiaohongshu.com" in current_url:
                print("检测到有效的小红书登录状态")
                return True
            else:
                print("小红书登录状态已失效")
                return False

        except Exception as e:
            print(f"检查现有登录状态失败: {e}")
            return False

    async def check_login_status(self, wait_for_redirect: bool = True, timeout: int = 30) -> bool:
        """检查登录状态"""
        try:
            if not self.page:
                print("页面未初始化")
                return False

            # 获取当前URL
            current_url = self.page.url
            print(f"检查登录状态，当前URL: {current_url}")

            # 如果已经跳转到创作者平台主页，说明登录成功
            if "creator.xiaohongshu.com" in current_url and "login" not in current_url:
                print("✅ 检测到已登录小红书（URL已跳转到创作者平台）")
                return True

            if not wait_for_redirect:
                return False

            print(f"等待登录跳转，超时时间: {timeout}秒")

            # 等待页面跳转
            try:
                await self.page.wait_for_url(
                    lambda url: "creator.xiaohongshu.com" in url and "login" not in url,
                    timeout=timeout * 1000
                )
                print("✅ 登录成功，页面已跳转")
                return True
            except Exception as e:
                print(f"等待登录跳转超时: {e}")
                return False

        except Exception as e:
            print(f"检查登录状态失败: {e}")
            return False

    async def save_login_state(self) -> bool:
        """保存登录状态到文件"""
        try:
            if not self.context:
                return False

            # 获取存储状态
            storage_state = await self.context.storage_state()

            # 保存到文件
            state_file = os.path.join(self.user_data_dir, "login_state.json")
            state_data = {
                "storage_state": storage_state,
                "saved_at": datetime.now().isoformat(),
                "platform": "xiaohongshu"
            }

            with open(state_file, 'w', encoding='utf-8') as f:
                json.dump(state_data, f, ensure_ascii=False, indent=2)

            print(f"小红书登录状态已保存到: {state_file}")
            return True

        except Exception as e:
            print(f"保存小红书登录状态失败: {e}")
            return False

    async def load_login_state(self) -> bool:
        """从文件加载登录状态"""
        try:
            state_file = os.path.join(self.user_data_dir, "login_state.json")

            if not os.path.exists(state_file):
                print("小红书登录状态文件不存在")
                return False

            with open(state_file, 'r', encoding='utf-8') as f:
                state_data = json.load(f)

            # 检查保存时间，如果超过7天则认为过期
            saved_at = datetime.fromisoformat(state_data["saved_at"])
            if datetime.now() - saved_at > timedelta(days=7):
                print("小红书登录状态已过期")
                return False

            # 使用全局浏览器管理器创建上下文与页面（仅当尚未存在时，避免重复占用信号量）
            if not self.context:
                self.context = await browser_manager.create_context(
                    storage_state=state_data["storage_state"],
                    user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
                    viewport={"width": 1920, "height": 1080},
                    accept_downloads=True,
                )

            # 创建页面（仅在不存在时创建）
            if not self.page:
                self.page = await browser_manager.open_page(self.context)

            print("小红书登录状态加载成功")
            return True

        except Exception as e:
            print(f"加载小红书登录状态失败: {e}")
            return False

    async def get_cookies(self) -> Optional[str]:
        """获取当前页面的cookies"""
        try:
            if not self.context:
                return None

            cookies = await self.context.cookies()
            return json.dumps(cookies)

        except Exception as e:
            print(f"获取小红书cookies失败: {e}")
            return None

    async def _is_qrcode_image(self, img_element) -> bool:
        """检测图片是否为二维码

        Args:
            img_element: 图片元素

        Returns:
            bool: 是否为二维码
        """
        try:
            # 获取图片的边界框信息
            bounding_box = await img_element.bounding_box()
            if not bounding_box:
                return False

            width = bounding_box['width']
            height = bounding_box['height']

            # 二维码通常是正方形或接近正方形，且有一定的最小尺寸
            if width < 100 or height < 100:  # 太小的图片不太可能是二维码
                return False

            # 检查宽高比，二维码应该接近1:1
            aspect_ratio = width / height
            if aspect_ratio < 0.8 or aspect_ratio > 1.25:  # 允许一定的误差
                return False

            # 检查图片的src属性，二维码通常是PNG格式
            src = await img_element.get_attribute("src")
            if src and "data:image/png" in src:
                # PNG格式的base64图片，很可能是二维码
                return True

            # 如果是其他格式但尺寸符合，也可能是二维码
            if width >= 100 and height >= 100 and 0.9 <= aspect_ratio <= 1.1:
                return True

            return False

        except Exception as e:
            print(f"检测二维码时出错: {e}")
            return False

    async def download_note_data_excel(self, begin_date: str, end_date: str, auto_import: bool = True) -> Optional[bytes]:
        """下载小红书笔记数据Excel文件

        Args:
            begin_date: 开始日期，格式: YYYY-MM-DD
            end_date: 结束日期，格式: YYYY-MM-DD
            auto_import: 是否自动导入数据到数据库，默认True

        Returns:
            Excel文件的二进制数据，失败返回None
        """
        try:
            if not self.page:
                print("页面未初始化，无法下载数据")
                return None

            # 检查当前URL，如果不在小红书创作者平台，先导航过去
            current_url = self.page.url
            print(f"当前URL: {current_url}")

            if "creator.xiaohongshu.com" not in current_url or "login" in current_url:
                print("当前不在小红书创作者平台，正在导航...")
                try:
                    await self.page.goto("https://creator.xiaohongshu.com",
                                       wait_until="domcontentloaded", timeout=15000)
                    await asyncio.sleep(3)

                    current_url = self.page.url
                    print(f"导航后的URL: {current_url}")

                    if "login" in current_url:
                        print("导航后仍在登录页面，可能登录已过期")
                        return None

                except Exception as nav_e:
                    print(f"导航到小红书创作者平台失败: {nav_e}")
                    return None

            # 转换日期格式为时间戳（毫秒）
            try:
                from datetime import datetime
                begin_dt = datetime.strptime(begin_date, "%Y-%m-%d")
                end_dt = datetime.strptime(end_date, "%Y-%m-%d")

                # 转换为毫秒时间戳
                begin_timestamp = int(begin_dt.timestamp() * 1000)
                end_timestamp = int(end_dt.timestamp() * 1000)

                print(f"时间范围: {begin_date} ({begin_timestamp}) 到 {end_date} ({end_timestamp})")

            except Exception as date_e:
                print(f"日期格式转换失败: {date_e}")
                return None

            # 导航到数据分析页面
            print("正在导航到数据分析页面...")
            try:
                await self.page.goto("https://creator.xiaohongshu.com/statistics/data-analysis",
                                   wait_until="domcontentloaded", timeout=15000)
                await asyncio.sleep(3)
                print("成功导航到数据分析页面")
            except Exception as nav_e:
                print(f"导航到数据分析页面失败: {nav_e}")
                return None

            # 设置请求拦截
            downloaded_data = None

            async def handle_request(route):
                nonlocal downloaded_data
                request = route.request

                # 拦截笔记数据下载请求
                if ("/api/galaxy/creator/datacenter/note/analyze/download" in request.url and
                    request.method == "GET"):
                    print(f"拦截到笔记数据下载请求: {request.url}")

                    try:
                        # 解析原始URL参数
                        from urllib.parse import urlparse, parse_qs, urlencode, urlunparse
                        parsed_url = urlparse(request.url)
                        query_params = parse_qs(parsed_url.query)

                        # 替换时间参数
                        query_params['post_begin_time'] = [str(begin_timestamp)]
                        query_params['post_end_time'] = [str(end_timestamp)]

                        # 重新构建URL
                        new_query = urlencode(query_params, doseq=True)
                        new_url = urlunparse((
                            parsed_url.scheme,
                            parsed_url.netloc,
                            parsed_url.path,
                            parsed_url.params,
                            new_query,
                            parsed_url.fragment
                        ))

                        print(f"修改后的URL: {new_url}")

                        # 发送修改后的请求
                        response = await route.fetch(url=new_url)

                        if response.status == 200:
                            downloaded_data = await response.body()
                            print(f"成功下载笔记数据，文件大小: {len(downloaded_data)} bytes")
                        else:
                            print(f"下载失败，状态码: {response.status}")

                        await route.fulfill(response=response)

                    except Exception as e:
                        print(f"处理请求拦截时出错: {e}")
                        await route.continue_()
                else:
                    await route.continue_()

            # 启用请求拦截
            await self.page.route("**/*", handle_request)

            try:
                # 点击下载按钮
                download_button_selector = "#pane-note-data > div > div > div.note-data-filter > button"
                print("正在查找下载按钮...")

                await self.page.wait_for_selector(download_button_selector, state="visible", timeout=10000)
                print("找到下载按钮，准备点击...")

                await self.page.click(download_button_selector)
                print("已点击下载按钮")

                # 等待下载完成
                max_wait_time = 30  # 最多等待30秒
                check_interval = 1   # 每秒检查一次

                for i in range(max_wait_time):
                    if downloaded_data:
                        print("下载完成！")
                        break
                    await asyncio.sleep(check_interval)
                    print(f"等待下载中... ({i+1}/{max_wait_time}秒)")

                if not downloaded_data:
                    print("下载超时")
                    return None

                print(f"小红书笔记数据下载成功，文件大小: {len(downloaded_data)} bytes")

                # 自动导入数据到数据库
                if auto_import and self.account_id:
                    await self._import_excel_to_database(downloaded_data, 'note_data')

                return downloaded_data

            except Exception as click_e:
                print(f"点击下载按钮失败: {click_e}")
                return None
            finally:
                # 移除请求拦截
                await self.page.unroute("**/*")

        except Exception as e:
            print(f"下载小红书笔记数据失败: {e}")
            return None

    async def close(self):
        """关闭浏览器和相关资源"""
        try:
            if self.page:
                # 先移除所有事件监听器，避免关闭时的错误
                try:
                    await self.page.remove_all_listeners()
                except Exception:
                    pass
                # 等待一下让正在进行的请求完成
                import asyncio
                await asyncio.sleep(0.5)
                await self.page.close()
                self.page = None

            if self.context:
                from app.services.browser_manager import browser_manager
                await browser_manager.close_context(self.context)
                self.context = None

            # 已改为使用全局 BrowserManager，无需自管浏览器/playwright
            # 保留变量重置以兼容现有代码
            self.browser = None
            self.playwright = None

        except Exception as e:
            print(f"关闭小红书服务时出错: {e}")

    async def _update_database_login_status(self, is_logged_in: bool):
        """更新数据库中的登录状态"""
        try:
            from app.database import SessionLocal
            from app.models import PlatformAccount
            from datetime import datetime

            db = SessionLocal()
            try:
                account = db.query(PlatformAccount).filter(PlatformAccount.id == self.account_id).first()
                if account:
                    account.login_status = is_logged_in
                    if is_logged_in:
                        account.last_login_time = datetime.now()
                        # 获取并保存cookies
                        try:
                            cookies = await self.get_cookies()
                            account.cookies = cookies
                        except Exception as e:
                            print(f"获取cookies失败: {e}")
                    else:
                        account.cookies = None

                    db.commit()
                    print(f"已更新账号 {self.account_id} 的数据库登录状态: {is_logged_in}")
                else:
                    print(f"未找到账号 {self.account_id}")
            finally:
                db.close()

        except Exception as e:
            print(f"更新数据库登录状态失败: {e}")

    async def _import_excel_to_database(self, excel_content: bytes, data_type: str):
        """将Excel数据导入到数据库"""
        try:
            # 导入数据明细服务
            from app.services.data_details_service import DataDetailsService
            from app.database import SessionLocal

            # 创建数据库会话
            db = SessionLocal()

            try:
                # 调用数据导入服务
                result = DataDetailsService.import_excel_data(
                    db=db,
                    account_id=self.account_id,
                    data_type=data_type,
                    excel_content=excel_content
                )

                if result["success"]:
                    print(f"小红书数据导入成功: 新增 {result['imported_count']} 条，更新 {result['updated_count']} 条")
                else:
                    print(f"小红书数据导入失败: {result['error']}")

            finally:
                db.close()

        except Exception as e:
            print(f"小红书数据导入过程中发生错误: {e}")

    async def get_account_overview_data(self, auto_import: bool = True) -> Optional[dict]:
        """获取账号概览数据

        Args:
            auto_import: 是否自动导入数据到数据库，默认True

        Returns:
            账号概览数据字典，失败返回None
        """
        if not self.account_id:
            print("错误: 未设置账号ID")
            return None

        try:
            # 启动浏览器
            await self._start_browser()

            # 检查登录状态
            if not await self.check_existing_login():
                print("账号未登录，无法获取概览数据")
                return None

            print("正在访问账号概览页面...")
            await self.page.goto("https://creator.xiaohongshu.com/statistics/account", wait_until="domcontentloaded")

            # 等待页面加载
            await asyncio.sleep(3)

            print("正在监听API请求...")
            overview_data = None

            # 设置请求监听器
            async def handle_response(response):
                nonlocal overview_data
                if "api/galaxy/creator/data/note_detail_new" in response.url:
                    try:
                        json_data = await response.json()
                        if json_data.get('success') and 'data' in json_data:
                            thirty_data = json_data['data'].get('thirty')
                            if thirty_data:
                                overview_data = thirty_data
                                print("✅ 成功获取账号概览数据")
                    except Exception as e:
                        print(f"解析API响应失败: {e}")

            # 监听响应
            self.page.on("response", handle_response)

            # 等待数据加载或触发刷新
            try:
                # 尝试点击刷新按钮或等待数据自动加载
                await asyncio.sleep(5)

                # 如果没有获取到数据，尝试刷新页面
                if not overview_data:
                    print("尝试刷新页面获取数据...")
                    await self.page.reload(wait_until="domcontentloaded")
                    await asyncio.sleep(5)

            except Exception as e:
                print(f"页面操作异常: {e}")

            # 等待最多30秒获取数据
            wait_time = 0
            while not overview_data and wait_time < 30:
                await asyncio.sleep(1)
                wait_time += 1

            if overview_data:
                print(f"账号概览数据获取成功")

                # 自动导入数据到数据库
                if auto_import:
                    await self._import_overview_to_database(overview_data)

                return overview_data
            else:
                print("未能获取到账号概览数据")
                return None

        except Exception as e:
            print(f"获取账号概览数据失败: {e}")
            return None
        finally:
            try:
                if hasattr(self, 'page') and self.page:
                    self.page.remove_all_listeners("response")
            except:
                pass

    async def _import_overview_to_database(self, overview_data: dict):
        """将账号概览数据导入到数据库"""
        try:
            from app.database import SessionLocal
            from app.models import XiaohongshuAccountOverview
            from datetime import datetime, date

            # 创建数据库会话
            db = SessionLocal()

            try:
                # 解析各种趋势数据
                trend_mappings = {
                    'view_list': 'view_count',
                    'view_time_list': 'view_time_count',
                    'home_view_list': 'home_view_count',
                    'like_list': 'like_count',
                    'collect_list': 'collect_count',
                    'comment_list': 'comment_count',
                    'danmaku_list': 'danmaku_count',
                    'rise_fans_list': 'rise_fans_count',
                    'share_list': 'share_count'
                }

                # 收集所有日期的数据
                date_data = {}

                for api_field, db_field in trend_mappings.items():
                    trend_list = overview_data.get(api_field, [])
                    for item in trend_list:
                        if 'date' in item and 'count' in item:
                            # 将毫秒级时间戳转换为日期
                            timestamp_ms = item['date']
                            timestamp_s = timestamp_ms / 1000
                            item_date = datetime.fromtimestamp(timestamp_s).date()

                            if item_date not in date_data:
                                date_data[item_date] = {}

                            date_data[item_date][db_field] = item['count']

                # 保存到数据库
                imported_count = 0
                updated_count = 0

                for item_date, counts in date_data.items():
                    # 检查是否已存在
                    existing = db.query(XiaohongshuAccountOverview).filter(
                        XiaohongshuAccountOverview.account_id == self.account_id,
                        XiaohongshuAccountOverview.date == item_date
                    ).first()

                    if existing:
                        # 更新现有记录
                        for field, value in counts.items():
                            setattr(existing, field, value)
                        existing.updated_at = datetime.utcnow()
                        updated_count += 1
                    else:
                        # 创建新记录
                        record_data = {
                            'account_id': self.account_id,
                            'date': item_date,
                            **counts
                        }
                        record = XiaohongshuAccountOverview(**record_data)
                        db.add(record)
                        imported_count += 1

                db.commit()
                print(f"账号概览数据导入成功: 新增 {imported_count} 条，更新 {updated_count} 条")

            finally:
                db.close()

        except Exception as e:
            print(f"账号概览数据导入失败: {e}")

    async def get_fans_data(self, auto_import: bool = True) -> Optional[dict]:
        """获取粉丝数据

        Args:
            auto_import: 是否自动导入数据到数据库，默认True

        Returns:
            粉丝数据字典，失败返回None
        """
        if not self.account_id:
            print("错误: 未设置账号ID")
            return None

        try:
            print("准备启动浏览器")
            # 启动浏览器
            await self._start_browser()

            # 检查登录状态
            if not await self.check_existing_login():
                print("账号未登录，无法获取粉丝数据")
                return None

            print("正在访问粉丝数据页面...")

            # 提前挂载监听器，避免错过初次加载的API响应
            fans_data = None

            async def handle_response(response):
                nonlocal fans_data
                if "api/galaxy/creator/data/fans/overall_new" in response.url:
                    try:
                        json_data = await response.json()
                        if json_data.get('success') and 'data' in json_data:
                            thirty_data = json_data['data'].get('thirty')
                            if thirty_data:
                                fans_data = thirty_data
                                print("✅ 成功获取粉丝数据")
                    except Exception as e:
                        print(f"解析粉丝数据API响应失败: {e}")

            # 注册监听
            self.page.on("response", handle_response)

            # 再进行导航
            await self.page.goto("https://creator.xiaohongshu.com/creator/fans", wait_until="domcontentloaded")

            # 等待页面加载与容器出现
            await asyncio.sleep(2)
            try:
                await self.page.wait_for_selector('#app > div > div.content > div.fans-data-container', timeout=10000)
                print("粉丝数据容器已加载")
            except Exception as e:
                print(f"等待粉丝数据容器失败: {e}")
                # 兜底等待一下
                await asyncio.sleep(2)

            # 尝试切换时间范围为近30天，触发数据刷新
            try:
                fans_container = await self.page.query_selector('#app > div > div.content > div.fans-data-container')
                if fans_container:
                    time_button = await fans_container.query_selector('button:has-text("近7天")')
                    if time_button:
                        await time_button.click()
                        await asyncio.sleep(0.8)
                        option_30 = await self.page.query_selector('text=近30天')
                        if option_30:
                            await option_30.click()
                            print("已切换粉丝数据时间范围至近30天")
                            await asyncio.sleep(2)
                    else:
                        print("未找到时间范围按钮，跳过切换")
                else:
                    print("未找到粉丝数据容器，跳过切换")
            except Exception as e:
                print(f"切换粉丝数据时间范围失败: {e}")

            # 快速等待可能的首次响应
            try:
                resp = await self.page.wait_for_response(
                    lambda r: "api/galaxy/creator/data/fans/overall_new" in r.url and r.status == 200,
                    timeout=8000
                )
                # 尝试解析一次，避免错过
                try:
                    jd = await resp.json()
                    if isinstance(jd, dict) and jd.get('success') and 'data' in jd and not fans_data:
                        data_obj = jd['data']
                        thirty_data = data_obj.get('thirty')
                        if thirty_data:
                            fans_data = thirty_data
                            print("✅ 成功获取粉丝数据(直接等待响应)")
                except Exception:
                    pass
            except Exception:
                # 不强制，继续后续流程
                pass

            # 等待数据加载或触发刷新
            try:
                # 尝试点击刷新按钮或等待数据自动加载
                await asyncio.sleep(5)

                # 如果没有获取到数据，尝试刷新页面
                if not fans_data:
                    print("尝试刷新页面获取粉丝数据...")
                    await self.page.reload(wait_until="domcontentloaded")
                    await asyncio.sleep(5)

            except Exception as e:
                print(f"页面操作异常: {e}")

            # 等待最多30秒获取数据
            wait_time = 0
            while not fans_data and wait_time < 30:
                await asyncio.sleep(1)
                wait_time += 1

            if fans_data:
                print(f"粉丝数据获取成功")

                # 自动导入数据到数据库
                if auto_import:
                    await self._import_fans_to_database(fans_data)

                return fans_data
            else:
                print("未能获取到粉丝数据")
                return None

        except Exception as e:
            print(f"获取粉丝数据失败: {e}")
            return None
        finally:
            try:
                if hasattr(self, 'page') and self.page:
                    self.page.remove_all_listeners("response")
            except:
                pass

    async def _import_fans_to_database(self, fans_data: dict):
        """将粉丝数据导入到数据库"""
        try:
            from app.database import SessionLocal
            from app.models import XiaohongshuFansData
            from datetime import datetime, date

            # 创建数据库会话
            db = SessionLocal()

            try:
                # 解析粉丝数据字段映射
                fans_mappings = {
                    'fans_list': 'total_fans_count',      # 总关注数
                    'leave_fans_list': 'new_fans_count',  # 新增关注数
                    'rise_fans_list': 'unfans_count'      # 取关数
                }

                # 收集所有日期的数据
                date_data = {}

                for api_field, db_field in fans_mappings.items():
                    fans_list = fans_data.get(api_field, [])
                    for item in fans_list:
                        if 'date' in item and 'count' in item:
                            # 将毫秒级时间戳转换为日期
                            timestamp_ms = item['date']
                            timestamp_s = timestamp_ms / 1000
                            item_date = datetime.fromtimestamp(timestamp_s).date()

                            if item_date not in date_data:
                                date_data[item_date] = {}

                            date_data[item_date][db_field] = item['count']

                # 保存到数据库
                imported_count = 0
                updated_count = 0

                for item_date, counts in date_data.items():
                    # 检查是否已存在
                    existing = db.query(XiaohongshuFansData).filter(
                        XiaohongshuFansData.account_id == self.account_id,
                        XiaohongshuFansData.date == item_date
                    ).first()

                    if existing:
                        # 更新现有记录
                        for field, value in counts.items():
                            setattr(existing, field, value)
                        existing.updated_at = datetime.utcnow()
                        updated_count += 1
                    else:
                        # 创建新记录
                        record_data = {
                            'account_id': self.account_id,
                            'date': item_date,
                            **counts
                        }
                        record = XiaohongshuFansData(**record_data)
                        db.add(record)
                        imported_count += 1

                db.commit()
                print(f"粉丝数据导入成功: 新增 {imported_count} 条，更新 {updated_count} 条")

            finally:
                db.close()

        except Exception as e:
            print(f"粉丝数据导入失败: {e}")

    async def screenshot_fans_overview(self, save_path: str) -> Optional[str]:
        """截图粉丝数据概览页面

        Args:
            save_path: 保存路径

        Returns:
            截图文件路径，失败返回None
        """
        if not self.account_id:
            print("错误: 未设置账号ID")
            return None

        try:
            # 启动浏览器
            await self._start_browser()

            # 检查登录状态
            if not await self.check_existing_login():
                print("账号未登录，无法截图粉丝数据概览")
                return None

            print("正在访问粉丝数据页面...")

            # 访问粉丝数据页面
            await self.page.goto('https://creator.xiaohongshu.com/creator/fans',
                                wait_until='domcontentloaded', timeout=30000)

            # 等待页面加载完成
            await asyncio.sleep(3)

            # 等待粉丝数据容器加载
            try:
                await self.page.wait_for_selector('#app > div > div.content > div.fans-data-container > div.block-line',
                                                timeout=10000)
                print("粉丝数据容器已加载")
            except Exception as e:
                print(f"等待粉丝数据容器失败: {e}")
                # 尝试等待更通用的选择器
                try:
                    await self.page.wait_for_selector('.fans-data-container', timeout=5000)
                    print("找到粉丝数据容器（通用选择器）")
                except:
                    print("未找到粉丝数据容器，继续截图")

            # 切换时间跨度从"近7天"到"近30天"
            try:
                print("正在切换时间跨度到近30天...")

                # 更精确地定位粉丝数据容器中的时间跨度按钮
                fans_container = await self.page.query_selector('#app > div > div.content > div.fans-data-container')
                if fans_container:
                    # 在粉丝数据容器中查找"近7天"按钮
                    time_button = await fans_container.query_selector('button:has-text("近7天")')
                    if time_button:
                        await time_button.click()
                        print("已点击粉丝数据页面的时间跨度按钮")

                        # 等待下拉菜单出现
                        await asyncio.sleep(1)

                        # 点击"近30天"选项
                        thirty_days_option = self.page.get_by_text("近30天").first
                        await thirty_days_option.click()
                        print("已切换到近30天")

                        # 等待数据刷新
                        await asyncio.sleep(3)
                    else:
                        print("未找到粉丝数据页面的时间跨度按钮")
                        raise Exception("未找到时间跨度按钮")
                else:
                    print("未找到粉丝数据容器")
                    raise Exception("未找到粉丝数据容器")

            except Exception as e:
                print(f"切换时间跨度失败: {e}")
                # 如果切换失败，继续截图默认的7天数据
                print("将使用默认的7天数据进行截图")

            # 确保保存目录存在
            os.makedirs(os.path.dirname(save_path), exist_ok=True)

            # 截图指定区域
            try:
                element = await self.page.query_selector('#app > div > div.content > div.fans-data-container > div.block-line')
                if element:
                    await element.screenshot(path=save_path)
                    print(f"粉丝数据概览截图已保存: {save_path}")
                    return save_path
                else:
                    # 如果找不到指定元素，尝试截图整个页面
                    print("未找到指定元素，截图整个页面")
                    await self.page.screenshot(path=save_path, full_page=True)
                    print(f"整页截图已保存: {save_path}")
                    return save_path
            except Exception as e:
                print(f"截图失败: {e}")
                return None

        except Exception as e:
            print(f"截图粉丝数据概览失败: {e}")
            return None

    async def download_account_overview_data(self, auto_import: bool = True) -> Optional[dict]:
        """下载账号概览数据（别名方法，调用get_account_overview_data）

        Args:
            auto_import: 是否自动导入数据到数据库，默认True

        Returns:
            账号概览数据字典，失败返回None
        """
        return await self.get_account_overview_data(auto_import=auto_import)

    async def download_fans_data(self, auto_import: bool = True) -> Optional[dict]:
        """下载粉丝数据（别名方法，调用get_fans_data）

        Args:
            auto_import: 是否自动导入数据到数据库，默认True

        Returns:
            粉丝数据字典，失败返回None
        """
        return await self.get_fans_data(auto_import=auto_import)

    async def download_account_overview_data(self, auto_import: bool = True) -> Optional[dict]:
        """下载账号概览数据（别名方法，调用get_account_overview_data）

        Args:
            auto_import: 是否自动导入数据到数据库，默认True

        Returns:
            账号概览数据字典，失败返回None
        """
        return await self.get_account_overview_data(auto_import=auto_import)

    async def screenshot_account_overview(self, save_path: str) -> Optional[str]:
        """截图账号总览页面

        Args:
            save_path: 保存路径

        Returns:
            截图文件路径，失败返回None
        """
        if not self.account_id:
            print("错误: 未设置账号ID")
            return None

        try:
            # 启动浏览器
            await self._start_browser()

            # 检查登录状态
            if not await self.check_existing_login():
                print("账号未登录，无法截图账号总览")
                return None

            print("正在访问账号总览页面...")

            # 访问账号总览页面
            await self.page.goto('https://creator.xiaohongshu.com/statistics/account',
                                wait_until='domcontentloaded', timeout=30000)

            # 等待页面加载完成
            await asyncio.sleep(3)

            # 切换到"近30日"
            try:
                print("正在切换到近30日...")

                # 更精确地定位账号总览页面中的时间选择器
                # 在指定的数据区域中查找时间选择按钮
                data_area = await self.page.query_selector('#content-area > main > div:nth-child(3) > div > div > div > div.data')
                if data_area:
                    # 直接查找"近30日"选项
                    thirty_days_option = await data_area.query_selector('*:has-text("近30日")')
                    if thirty_days_option:
                        await thirty_days_option.click()
                        print("已切换到近30日")
                        await asyncio.sleep(3)
                    else:
                        raise Exception("未找到时间选择选项")
                else:
                    print("未找到数据容器，使用备用方案")
                    # 备用方案：使用全局查找
                    thirty_days_option = self.page.get_by_text("近30日").first
                    await thirty_days_option.click()
                    print("已切换到近30日（备用方案）")
                    await asyncio.sleep(3)

            except Exception as e:
                print(f"切换到近30日失败: {e}")
                # 如果切换失败，继续截图默认数据
                print("将使用默认数据进行截图")

            # 等待数据区域加载
            try:
                await self.page.wait_for_selector('#content-area > main > div:nth-child(3) > div > div > div > div.data > div > div.d-tabs-pane > div > div.datas',
                                                timeout=10000)
                print("账号总览数据区域已加载")
            except Exception as e:
                print(f"等待账号总览数据区域失败: {e}")
                # 尝试等待更通用的选择器
                try:
                    await self.page.wait_for_selector('.datas', timeout=5000)
                    print("找到数据区域（通用选择器）")
                except:
                    print("未找到数据区域，继续截图")

            # 确保保存目录存在
            os.makedirs(os.path.dirname(save_path), exist_ok=True)

            # 截图指定区域
            try:
                element = await self.page.query_selector('#content-area > main > div:nth-child(3) > div > div > div > div.data > div > div.d-tabs-pane > div > div.datas')
                if element:
                    await element.screenshot(path=save_path)
                    print(f"账号总览截图已保存: {save_path}")
                    return save_path
                else:
                    # 如果找不到指定元素，尝试截图整个页面
                    print("未找到指定元素，截图整个页面")
                    await self.page.screenshot(path=save_path, full_page=True)
                    print(f"整页截图已保存: {save_path}")
                    return save_path
            except Exception as e:
                print(f"截图失败: {e}")
                return None

        except Exception as e:
            print(f"截图账号总览失败: {e}")
            return None

    # 统一接口方法
    async def download_single_data_type(
        self,
        data_type: str,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        auto_import: bool = True,
        **kwargs
    ) -> DataDownloadResult:
        """小红书统一数据下载入口"""
        try:
            if data_type == 'note_data':
                if not start_date or not end_date:
                    return DataDownloadResult(
                        success=False,
                        error_message="笔记数据下载需要提供开始和结束日期"
                    )

                result = await self.download_note_data_excel(
                    begin_date=start_date,
                    end_date=end_date,
                    auto_import=auto_import
                )
                return DataDownloadResult(
                    success=result is not None,
                    data=result,
                    error_message=None if result else "下载失败"
                )

            elif data_type == 'account_overview':
                result = await self.download_account_overview_data(
                    auto_import=auto_import
                )
                return DataDownloadResult(
                    success=result is not None,
                    data=result,
                    error_message=None if result else "下载失败"
                )

            elif data_type == 'fans_data':
                result = await self.download_fans_data(
                    auto_import=auto_import
                )
                return DataDownloadResult(
                    success=result is not None,
                    data=result,
                    error_message=None if result else "下载失败"
                )

            else:
                return DataDownloadResult(
                    success=False,
                    error_message=f"不支持的数据类型: {data_type}"
                )

        except Exception as e:
            return DataDownloadResult(
                success=False,
                error_message=f"下载失败: {str(e)}"
            )

    def get_supported_data_types(self) -> List[Tuple[str, str]]:
        """获取支持的数据类型"""
        return [
            ('note_data', '笔记数据'),
            ('account_overview', '账号概览'),
            ('fans_data', '粉丝数据')
        ]
