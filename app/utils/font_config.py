"""
字体配置工具模块
用于在不同环境中配置matplotlib的中文字体支持
"""

import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import os
import logging

logger = logging.getLogger(__name__)


def configure_chinese_fonts():
    """配置matplotlib的中文字体支持"""
    
    try:
        # 设置matplotlib使用非交互式后端
        plt.switch_backend('Agg')
        
        # 尝试不同的中文字体，按优先级排序
        font_candidates = [
            # Linux系统字体
            'WenQuanYi Zen Hei',
            'WenQuanYi Micro Hei', 
            'Noto Sans CJK SC',
            'Noto Sans CJK',
            # macOS系统字体
            'Arial Unicode MS',
            'PingFang SC',
            'Hiragino Sans GB',
            # Windows系统字体
            'SimHei',
            'Microsoft YaHei',
            'SimSun',
            # 通用fallback
            'DejaVu Sans',
            'sans-serif'
        ]
        
        # 获取系统可用字体
        available_fonts = [f.name for f in fm.fontManager.ttflist]
        logger.info(f"系统可用字体数量: {len(available_fonts)}")
        
        # 查找可用的中文字体
        selected_font = None
        for font_name in font_candidates:
            if font_name in available_fonts:
                selected_font = font_name
                logger.info(f"选择字体: {font_name}")
                break
        
        if not selected_font:
            # 如果没有找到预定义字体，尝试查找任何包含"CJK"或中文名称的字体
            cjk_fonts = [f for f in available_fonts if 'CJK' in f or 'Zen Hei' in f or 'Micro Hei' in f]
            if cjk_fonts:
                selected_font = cjk_fonts[0]
                logger.info(f"找到CJK字体: {selected_font}")
            else:
                selected_font = 'DejaVu Sans'
                logger.warning("未找到中文字体，使用默认字体")
        
        # 配置matplotlib
        plt.rcParams['font.sans-serif'] = [selected_font] + font_candidates
        plt.rcParams['axes.unicode_minus'] = False
        
        # 清除字体缓存
        plt.rcParams['font.family'] = 'sans-serif'
        
        logger.info(f"matplotlib中文字体配置完成，使用字体: {selected_font}")
        return selected_font
        
    except Exception as e:
        logger.error(f"配置中文字体失败: {str(e)}")
        # 使用基本配置作为fallback
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'sans-serif']
        plt.rcParams['axes.unicode_minus'] = False
        return 'DejaVu Sans'


def test_chinese_font():
    """测试中文字体显示"""
    
    try:
        import matplotlib.pyplot as plt
        import tempfile
        import os
        
        # 配置字体
        font_name = configure_chinese_fonts()
        
        # 创建测试图表
        fig, ax = plt.subplots(figsize=(8, 6))
        
        # 测试中文文本
        test_texts = [
            '传播渠道',
            '阅读次数',
            '公众号消息',
            '朋友圈',
            '搜一搜'
        ]
        
        for i, text in enumerate(test_texts):
            ax.text(0.1, 0.8 - i * 0.15, text, fontsize=14, transform=ax.transAxes)
        
        ax.set_title(f'中文字体测试 - 使用字体: {font_name}', fontsize=16)
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')
        
        # 保存到临时文件
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.png')
        plt.savefig(temp_file.name, dpi=150, bbox_inches='tight', facecolor='white')
        plt.close()
        
        # 检查文件是否生成
        if os.path.exists(temp_file.name) and os.path.getsize(temp_file.name) > 0:
            logger.info(f"中文字体测试成功，测试文件: {temp_file.name}")
            # 清理测试文件
            os.unlink(temp_file.name)
            return True
        else:
            logger.error("中文字体测试失败，文件未生成或为空")
            return False
            
    except Exception as e:
        logger.error(f"中文字体测试失败: {str(e)}")
        return False


def get_font_info():
    """获取字体信息，用于调试"""
    
    try:
        import matplotlib.font_manager as fm
        
        # 获取所有字体
        fonts = fm.fontManager.ttflist
        
        # 过滤中文相关字体
        chinese_fonts = []
        for font in fonts:
            font_name = font.name
            if any(keyword in font_name.lower() for keyword in ['cjk', 'chinese', 'zh', 'han', 'hei', 'song']):
                chinese_fonts.append({
                    'name': font_name,
                    'path': font.fname,
                    'style': font.style,
                    'weight': font.weight
                })
        
        logger.info(f"找到 {len(chinese_fonts)} 个中文相关字体")
        for font in chinese_fonts[:5]:  # 只显示前5个
            logger.info(f"  - {font['name']} ({font['path']})")
        
        return chinese_fonts
        
    except Exception as e:
        logger.error(f"获取字体信息失败: {str(e)}")
        return []


# 在模块导入时自动配置字体
configure_chinese_fonts()
