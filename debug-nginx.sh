#!/bin/bash
# 检查nginx状态和配置的调试脚本

echo "=== 检查nginx进程 ==="
ps aux | grep nginx

echo -e "\n=== 检查nginx配置 ==="
nginx -t 2>&1 || echo "nginx命令不可用，可能在容器中"

echo -e "\n=== 检查Docker容器中的nginx ==="
docker ps | grep nginx

echo -e "\n=== 检查端口占用 ==="
netstat -tlnp | grep -E ":80|:8000|:3000"

echo -e "\n=== 检查nginx错误日志 ==="
if [ -f /var/log/nginx/error.log ]; then
    tail -20 /var/log/nginx/error.log
else
    echo "系统nginx日志不存在，检查Docker容器日志:"
    docker logs nginx 2>/dev/null | tail -20 || echo "没有找到nginx容器"
fi

echo -e "\n=== 检查nginx访问日志 ==="
if [ -f /var/log/nginx/access.log ]; then
    tail -20 /var/log/nginx/access.log
else
    echo "系统nginx访问日志不存在"
fi
