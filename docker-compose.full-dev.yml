services:
  # 后端API服务 - 开发环境配置
  backend:
    build: .
    container_name: social_media_backend_dev
    restart: unless-stopped
    environment:
      - DATABASE_URL=sqlite:///./social_media.db
      - SECRET_KEY=${SECRET_KEY}
      - ALGORITHM=${ALGORITHM}
      - ACCESS_TOKEN_EXPIRE_MINUTES=${ACCESS_TOKEN_EXPIRE_MINUTES}
      - PYTHONPATH=/app
    volumes:
      # 挂载整个应用代码目录，实现热更新
      - ./app:/app/app
      - ./main.py:/app/main.py
      - ./requirements.txt:/app/requirements.txt
      - ./user_data:/app/user_data
      - ./data:/app/data
      - ./social_media.db:/app/social_media.db
      - ./temp_downloads:/app/temp_downloads
      # 挂载日志目录（可选）
      - ./logs:/app/logs
    networks:
      - app-network
    # 开发环境启动命令，启用热重载
    command: ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--reload", "--reload-dir", "/app"]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 前端服务 - 开发环境配置（热更新）
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: social_media_frontend_dev
    restart: unless-stopped
    environment:
      # React开发服务器配置
      - CHOKIDAR_USEPOLLING=true
      - WATCHPACK_POLLING=true
      - FAST_REFRESH=true
      - WDS_SOCKET_HOST=localhost
      - WDS_SOCKET_PORT=3000
      - DANGEROUSLY_DISABLE_HOST_CHECK=true
      - DISABLE_HOST_CHECK=true
    volumes:
      # 挂载源码实现热更新
      - ./frontend/src:/app/src
      - ./frontend/public:/app/public
      - ./frontend/package.json:/app/package.json
      - ./frontend/tsconfig.json:/app/tsconfig.json
      # 排除node_modules，使用容器内的
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - app-network

  # Nginx代理服务
  nginx:
    image: nginx:alpine
    container_name: social_media_nginx_dev
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      # 日志目录
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - backend
      - frontend
    networks:
      - app-network

networks:
  app-network:
    driver: bridge
