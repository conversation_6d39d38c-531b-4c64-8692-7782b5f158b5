services:
  # 后端API服务
  backend:
    build: .
    container_name: social_media_backend
    restart: unless-stopped
    environment:
      # 数据库配置
      - DB_HOST=${DB_HOST}
      - DB_PORT=${DB_PORT}
      - DB_USER=${DB_USER}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_NAME=${DB_NAME}
      # JWT认证配置
      - SECRET_KEY=${SECRET_KEY}
      - ALGORITHM=${ALGORITHM}
      - ACCESS_TOKEN_EXPIRE_MINUTES=${ACCESS_TOKEN_EXPIRE_MINUTES}
      # 应用配置
      - PYTHONPATH=/app
      - ENVIRONMENT=${ENVIRONMENT:-production}
      # 设置热更新模式，可通过环境变量控制
      - HOT_RELOAD=${HOT_RELOAD:-false}
      # 登录保持器配置
      - LOGIN_KEEPER_ENABLED=${LOGIN_KEEPER_ENABLED:-false}
      - LOGIN_KEEPER_INTERVAL=${LOGIN_KEEPER_INTERVAL:-30}
      - LOGIN_KEEPER_PLATFORMS=${LOGIN_KEEPER_PLATFORMS:-wechat_mp,xiaohongshu,wechat_channels}
      - LOGIN_KEEPER_MAX_RETRIES=${LOGIN_KEEPER_MAX_RETRIES:-3}
      - LOGIN_KEEPER_CONCURRENT=${LOGIN_KEEPER_CONCURRENT:-3}
      - LOGIN_KEEPER_LOG_LEVEL=${LOGIN_KEEPER_LOG_LEVEL:-INFO}
      - LOGIN_KEEPER_TIMEOUT=${LOGIN_KEEPER_TIMEOUT:-60}
      # 微信视频号服务配置
      - WECHAT_CHANNELS_DEFAULT_TIMEOUT=${WECHAT_CHANNELS_DEFAULT_TIMEOUT:-60000}
      - WECHAT_CHANNELS_NAVIGATION_TIMEOUT=${WECHAT_CHANNELS_NAVIGATION_TIMEOUT:-120000}
      - WECHAT_CHANNELS_NETWORK_CHECK_TIMEOUT=${WECHAT_CHANNELS_NETWORK_CHECK_TIMEOUT:-30000}
      - WECHAT_CHANNELS_MAX_RETRIES=${WECHAT_CHANNELS_MAX_RETRIES:-5}
      - WECHAT_CHANNELS_RETRY_DELAY=${WECHAT_CHANNELS_RETRY_DELAY:-10}
      - WECHAT_CHANNELS_DEBUG=${WECHAT_CHANNELS_DEBUG:-false}
    volumes:
      # 数据目录
      - ./user_data:/app/user_data
      - ./data:/app/data
      - ./temp_downloads:/app/temp_downloads
      # 代码热更新目录（生产环境可选）
      - ./app:/app/app
      - ./main.py:/app/main.py
      # SQLite数据库文件（备用，现在使用远程MySQL）
      # - ./social_media.db:/app/social_media.db
    ports:
      - "8000:8000"
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: ${FRONTEND_DOCKERFILE:-Dockerfile}
    container_name: social_media_frontend
    restart: unless-stopped
    environment:
      - NODE_ENV=${NODE_ENV:-production}
      - HOT_RELOAD=${HOT_RELOAD:-false}
    volumes:
      # 开发模式下挂载源码（可选）
      - ./frontend/src:/app/src:${FRONTEND_VOLUME_MODE:-ro}
      - ./frontend/public:/app/public:${FRONTEND_VOLUME_MODE:-ro}
    ports:
      - "${FRONTEND_PORT:-3000}:${FRONTEND_INTERNAL_PORT:-80}"
    depends_on:
      - backend
    networks:
      - app-network

# volumes:
  # app_data:  # 不需要，使用本地目录挂载

networks:
  app-network:
    driver: bridge