services:
  # 后端API服务
  backend:
    build: .
    container_name: social_media_backend
    restart: unless-stopped
    environment:
      - DATABASE_URL=sqlite:///./social_media.db
      - SECRET_KEY=${SECRET_KEY}
      - ALGORITHM=${ALGORITHM}
      - ACCESS_TOKEN_EXPIRE_MINUTES=${ACCESS_TOKEN_EXPIRE_MINUTES}
      - PYTHONPATH=/app
      # 设置热更新模式，可通过环境变量控制
      - HOT_RELOAD=${HOT_RELOAD:-false}
    volumes:
      # 数据目录
      - ./user_data:/app/user_data
      - ./data:/app/data
      - ./social_media.db:/app/social_media.db
      - ./temp_downloads:/app/temp_downloads
      # 代码热更新目录（生产环境可选）
      - ./app:/app/app
      - ./main.py:/app/main.py
    ports:
      - "8000:8000"
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: ${FRONTEND_DOCKERFILE:-Dockerfile}
    container_name: social_media_frontend
    restart: unless-stopped
    environment:
      - NODE_ENV=${NODE_ENV:-production}
      - HOT_RELOAD=${HOT_RELOAD:-false}
    volumes:
      # 开发模式下挂载源码（可选）
      - ./frontend/src:/app/src:${FRONTEND_VOLUME_MODE:-ro}
      - ./frontend/public:/app/public:${FRONTEND_VOLUME_MODE:-ro}
    ports:
      - "${FRONTEND_PORT:-3000}:${FRONTEND_INTERNAL_PORT:-80}"
    depends_on:
      - backend
    networks:
      - app-network

# volumes:
  # app_data:  # 不需要，使用本地目录挂载

networks:
  app-network:
    driver: bridge