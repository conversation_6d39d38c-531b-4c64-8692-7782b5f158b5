# Docker容器中文字体支持

## 问题描述

在Docker容器中部署应用后，matplotlib生成的饼图无法正确显示中文字符，通常表现为：
- 中文字符显示为方框 □
- 中文字符完全不显示
- 字体回退到默认英文字体

## 解决方案

### 1. Dockerfile修改

已在Dockerfile中添加了中文字体支持：

```dockerfile
# 安装中文字体
RUN apt-get update && apt-get install -y \
    fonts-wqy-zenhei \      # 文泉驿正黑体
    fonts-wqy-microhei \    # 文泉驿微米黑
    fonts-noto-cjk \        # Google Noto CJK字体
    fontconfig \            # 字体配置工具
    && fc-cache -fv \       # 刷新字体缓存
    && rm -rf /var/lib/apt/lists/*
```

### 2. 字体配置模块

创建了专门的字体配置模块 `app/utils/font_config.py`：

- 自动检测系统可用的中文字体
- 按优先级选择最佳字体
- 配置matplotlib使用中文字体
- 提供字体测试功能

### 3. 支持的字体

按优先级顺序：

**Linux系统字体：**
- WenQuanYi Zen Hei (文泉驿正黑)
- WenQuanYi Micro Hei (文泉驿微米黑)
- Noto Sans CJK SC (Google Noto)

**macOS系统字体：**
- Arial Unicode MS
- PingFang SC
- Hiragino Sans GB

**Windows系统字体：**
- SimHei (黑体)
- Microsoft YaHei (微软雅黑)
- SimSun (宋体)

## 测试方法

### 1. 本地测试

```bash
# 测试字体配置
python test/test_chinese_fonts.py
```

### 2. Docker容器测试

```bash
# 构建镜像
docker-compose build backend

# 运行字体测试
docker run --rm social-media-manager_backend python test/test_chinese_fonts.py

# 或者进入容器交互测试
docker run -it --rm social-media-manager_backend bash
python test/test_chinese_fonts.py
```

### 3. 生产环境测试

部署后可以通过以下方式验证：

1. 下载包含"内容数据趋势明细"的数据
2. 检查生成的饼图是否正确显示中文
3. 查看应用日志中的字体配置信息

## 故障排除

### 1. 字体未正确显示

**检查系统字体：**
```bash
# 进入容器
docker exec -it social_media_backend bash

# 查看已安装的字体
fc-list | grep -i "zh\|chinese\|cjk\|hei"

# 运行字体测试
python test/test_chinese_fonts.py
```

**检查matplotlib配置：**
```python
import matplotlib.pyplot as plt
print("当前字体设置:", plt.rcParams['font.sans-serif'])
```

### 2. 字体缓存问题

```bash
# 清除字体缓存
fc-cache -fv

# 重启应用
docker-compose restart backend
```

### 3. 权限问题

确保应用用户有权限访问字体文件：
```bash
# 检查字体文件权限
ls -la /usr/share/fonts/truetype/
```

## 开发建议

### 1. 添加新字体

如需支持其他字体，修改 `app/utils/font_config.py` 中的 `font_candidates` 列表。

### 2. 调试字体问题

使用 `get_font_info()` 函数获取详细的字体信息：

```python
from app.utils.font_config import get_font_info
fonts = get_font_info()
for font in fonts:
    print(f"字体: {font['name']}, 路径: {font['path']}")
```

### 3. 性能优化

- 字体配置在模块导入时自动执行
- 避免重复配置字体
- 使用字体缓存提高性能

## 版本兼容性

- **Python**: 3.11+
- **matplotlib**: 3.10.3+
- **Docker**: 支持多阶段构建
- **操作系统**: Debian/Ubuntu based images

## 相关文件

- `Dockerfile` - Docker镜像构建配置
- `app/utils/font_config.py` - 字体配置模块
- `app/services/data_download_service.py` - 饼图生成服务
- `test/test_chinese_fonts.py` - 字体测试脚本
- `requirements.txt` - Python依赖配置
