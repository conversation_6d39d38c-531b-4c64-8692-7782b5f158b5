# 社交媒体管理系统部署指南

## 概述

本指南将帮助你将社交媒体管理系统部署到阿里云服务器。系统使用Docker容器化部署，前端使用React，后端使用FastAPI，数据库使用SQLite。

## 系统架构

```
Internet
    ↓
Nginx (反向代理)
    ↓
┌─────────────────┬─────────────────┐
│   Frontend      │    Backend      │
│   (React)       │   (FastAPI)     │
│   Port: 3000    │   Port: 8000    │
└─────────────────┴─────────────────┘
            ↓
        SQLite Database
```

## 域名配置

<!-- - 前端访问域名: `sm.shishu.me`
- API访问域名: `api.sm.shishu.me` -->
- 前端访问域名: `sm.dev.mynatapp.cc`

## 部署步骤

### 1. 服务器环境准备

#### 1.1 配置Docker镜像源

```bash
sudo vim /etc/docker/daemon.json
```

添加以下内容：

```json
{
  "registry-mirrors": [
    "https://docker.m.daocloud.io",
    "https://docker.mirrors.ustc.edu.cn",
    "https://hub-mirror.c.163.com"
  ]
}
```

重启Docker服务：

```bash
sudo systemctl daemon-reexec
sudo systemctl restart docker
```

#### 1.2 安装必要软件

```bash
# 安装docker-compose（如果未安装）
sudo curl -L "https://github.com/docker/compose/releases/download/1.29.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 安装rsync（如果未安装）
sudo yum install -y rsync  # CentOS/RHEL
# 或
sudo apt-get install -y rsync  # Ubuntu/Debian
```

### 2. 本地准备工作

#### 2.1 配置环境变量

编辑 `.env.production` 文件：

```bash
# 数据库配置
DATABASE_URL=sqlite:///./data/social_media.db

# JWT配置
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 飞书配置
FEISHU_APP_ID=your-feishu-app-id
FEISHU_APP_SECRET=your-feishu-app-secret

# 环境标识
ENVIRONMENT=production
```

#### 2.2 修改上传脚本配置

编辑 `upload-to-server.sh`，修改服务器信息：

```bash
SERVER_USER="root"  # 你的服务器用户名
SERVER_HOST="your-server-ip"  # 你的服务器IP地址
SERVER_PATH="/opt/social-media-manager"  # 服务器部署路径
```

#### 2.3 上传项目到服务器

```bash
chmod +x upload-to-server.sh
./upload-to-server.sh
```

### 3. 服务器部署

#### 3.1 登录服务器并部署

```bash
ssh root@your-server-ip
cd /opt/social-media-manager

# 检查并编辑环境配置
vim .env.production

# 执行部署
chmod +x server-deploy.sh
./server-deploy.sh
```

#### 3.2 验证部署

检查容器状态：

```bash
docker-compose ps
```

查看日志：

```bash
docker-compose logs -f
```

测试服务：

```bash
# 测试后端
curl http://localhost:8000/

# 测试前端
curl http://localhost:3000/
```

### 4. Nginx配置

#### 4.1 添加站点配置

将 `nginx-config.conf` 的内容添加到Nginx配置中：

```bash
sudo vim /etc/nginx/sites-available/social-media-manager
```

创建软链接：

```bash
sudo ln -s /etc/nginx/sites-available/social-media-manager /etc/nginx/sites-enabled/
```

#### 4.2 SSL证书配置

获取SSL证书（使用Let's Encrypt）：

```bash
sudo certbot --nginx -d sm.shishu.me -d api.sm.shishu.me
```

或者手动配置SSL证书路径在Nginx配置文件中。

#### 4.3 重启Nginx

```bash
sudo nginx -t  # 测试配置
sudo systemctl reload nginx
```

### 5. 域名解析

在域名管理面板中添加A记录：

```
sm.shishu.me     A    your-server-ip
api.sm.shishu.me A    your-server-ip
```

## 维护操作

### 查看日志

```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f backend
docker-compose logs -f frontend
```

### 重启服务

```bash
# 重启所有服务
docker-compose restart

# 重启特定服务
docker-compose restart backend
docker-compose restart frontend
```

### 停止服务

```bash
docker-compose down
```

### 更新部署

```bash
# 在本地执行
./upload-to-server.sh

# 在服务器执行
cd /opt/social-media-manager
docker-compose down
docker-compose up --build -d
```

### 备份数据

```bash
# 备份SQLite数据库
cp data/social_media.db data/social_media.db.backup.$(date +%Y%m%d_%H%M%S)

# 备份用户数据
tar -czf user_data_backup_$(date +%Y%m%d_%H%M%S).tar.gz user_data/
```

## 故障排除

### 常见问题

1. **容器启动失败**
   - 检查Docker镜像是否正确拉取
   - 查看容器日志：`docker-compose logs [service_name]`

2. **前端无法访问后端API**
   - 检查Nginx配置是否正确
   - 确认防火墙设置
   - 检查域名解析

3. **数据库连接失败**
   - 确认数据目录权限
   - 检查SQLite文件是否存在

4. **SSL证书问题**
   - 检查证书文件路径
   - 确认证书有效期

### 日志位置

- Nginx日志: `/var/log/nginx/`
- Docker容器日志: `docker-compose logs`
- 应用日志: `logs/` 目录

## 安全建议

1. 定期更新系统和Docker镜像
2. 使用强密码和密钥
3. 配置防火墙规则
4. 定期备份数据
5. 监控系统资源使用情况

## 联系支持

如果在部署过程中遇到问题，请检查：
1. 服务器系统要求
2. 网络连接状态
3. 域名解析配置
4. SSL证书配置
