# 热更新配置指南

## 概述

本项目支持前后端代码热更新，让您可以在不重新构建Docker镜像的情况下，直接修改代码并看到效果。

## 🔥 热更新特性

### 后端热更新
- **监控目录**: `app/` 和 `main.py`
- **触发条件**: Python文件修改
- **重启方式**: uvicorn自动重启
- **排除文件**: `*.pyc`, `__pycache__`, `*.log`, 数据目录

### 前端热更新
- **监控目录**: `frontend/src/` 和 `frontend/public/`
- **触发条件**: React/TypeScript文件修改
- **刷新方式**: 浏览器自动刷新（Fast Refresh）
- **支持**: 组件状态保持、错误边界

## 🚀 快速启动

### 开发环境（热更新）
```bash
# 使用便捷脚本
./scripts/dev.sh

# 或手动启动
docker-compose -f docker-compose.dev.yml up --build
```

### 生产环境
```bash
# 使用便捷脚本
./scripts/prod.sh

# 或手动启动
docker-compose up --build -d
```

## 📁 文件结构

```
project/
├── docker-compose.yml          # 生产环境配置
├── docker-compose.dev.yml      # 开发环境配置（热更新）
├── Dockerfile                  # 后端生产镜像
├── start.sh                    # 后端智能启动脚本
├── frontend/
│   ├── Dockerfile              # 前端生产镜像
│   ├── Dockerfile.dev          # 前端开发镜像（热更新）
│   └── start.sh                # 前端智能启动脚本
└── scripts/
    ├── dev.sh                  # 开发环境启动脚本
    └── prod.sh                 # 生产环境启动脚本
```

## ⚙️ 环境变量配置

### .env 文件示例
```bash
# 应用配置
SECRET_KEY=your-secret-key
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 热更新配置
HOT_RELOAD=true              # 启用/禁用热更新
NODE_ENV=development         # 前端环境模式

# 端口配置
FRONTEND_PORT=3000           # 前端外部端口
FRONTEND_INTERNAL_PORT=3000  # 前端内部端口（开发模式）
```

### 开发环境变量
```bash
HOT_RELOAD=true
NODE_ENV=development
FRONTEND_DOCKERFILE=Dockerfile.dev
FRONTEND_VOLUME_MODE=rw
```

### 生产环境变量
```bash
HOT_RELOAD=false
NODE_ENV=production
FRONTEND_DOCKERFILE=Dockerfile
FRONTEND_VOLUME_MODE=ro
```

## 🔧 配置详解

### 后端热更新配置

**docker-compose.dev.yml**:
```yaml
backend:
  volumes:
    - ./app:/app/app              # 挂载应用代码
    - ./main.py:/app/main.py      # 挂载主文件
  command: ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
```

**start.sh**:
```bash
if [ "${HOT_RELOAD:-false}" = "true" ]; then
    exec uvicorn main:app --host 0.0.0.0 --port 8000 --reload --reload-dir /app
else
    exec uvicorn main:app --host 0.0.0.0 --port 8000 --workers 1
fi
```

### 前端热更新配置

**docker-compose.dev.yml**:
```yaml
frontend:
  build:
    dockerfile: Dockerfile.dev
  environment:
    - CHOKIDAR_USEPOLLING=true    # 启用文件轮询
    - FAST_REFRESH=true           # 启用快速刷新
  volumes:
    - ./frontend/src:/app/src     # 挂载源码
    - /app/node_modules           # 排除node_modules
```

## 🛠️ 使用方法

### 1. 开发环境启动
```bash
# 启动开发环境
./scripts/dev.sh

# 访问应用
# 前端: http://localhost:3000
# 后端: http://localhost:8000
```

### 2. 代码修改
- **后端**: 修改 `app/` 目录下的Python文件，服务会自动重启
- **前端**: 修改 `frontend/src/` 目录下的文件，浏览器会自动刷新

### 3. 查看日志
```bash
# 查看所有服务日志
docker-compose -f docker-compose.dev.yml logs -f

# 查看特定服务日志
docker-compose -f docker-compose.dev.yml logs -f backend
docker-compose -f docker-compose.dev.yml logs -f frontend
```

### 4. 停止服务
```bash
# 停止开发环境
docker-compose -f docker-compose.dev.yml down

# 或使用 Ctrl+C（如果在前台运行）
```

## 🚀 服务器部署

### 方式一：热更新模式（推荐用于测试服务器）
```bash
# 在服务器上
git pull origin main
export HOT_RELOAD=true
docker-compose up -d

# 后续代码更新
git pull origin main
# 容器会自动检测到代码变化并重启
```

### 方式二：生产模式
```bash
# 在服务器上
git pull origin main
export HOT_RELOAD=false
docker-compose up --build -d
```

## 🔍 故障排除

### 热更新不生效
1. **检查文件挂载**:
   ```bash
   docker-compose -f docker-compose.dev.yml exec backend ls -la /app/app
   ```

2. **检查环境变量**:
   ```bash
   docker-compose -f docker-compose.dev.yml exec backend env | grep HOT_RELOAD
   ```

3. **查看重启日志**:
   ```bash
   docker-compose -f docker-compose.dev.yml logs -f backend
   ```

### 前端热更新问题
1. **检查React开发服务器**:
   ```bash
   docker compose -f docker-compose.dev.yml logs frontend
   ```

2. **检查文件权限**:
   ```bash
   ls -la frontend/src/
   ```

3. **清除缓存**:
   ```bash
   docker compose -f docker-compose.dev.yml down
   docker system prune -f
   ./scripts/dev.sh
   ```

### Invalid Host header 错误
如果通过外部域名访问时出现"Invalid Host header"错误：

1. **检查环境变量**:
   ```bash
   docker compose exec frontend env | grep DISABLE_HOST_CHECK
   ```

2. **确认配置文件**:
   ```bash
   docker compose exec frontend cat /app/.env.development
   ```

3. **重启前端容器**:
   ```bash
   docker compose restart frontend
   ```

4. **添加允许的域名**:
   编辑 `frontend/setupProxy.js`，在 `allowedHosts` 数组中添加您的域名

## 📝 注意事项

1. **性能**: 热更新模式会消耗更多资源，仅建议在开发和测试环境使用
2. **安全**: 生产环境建议禁用热更新，使用预构建的镜像
3. **文件权限**: 确保挂载的文件具有正确的读写权限
4. **网络**: 开发模式下前端运行在3000端口，生产模式运行在80端口

## 🎯 最佳实践

1. **开发流程**:
   - 使用 `./scripts/dev.sh` 启动开发环境
   - 修改代码，观察自动重启/刷新
   - 测试完成后使用 `./scripts/prod.sh` 验证生产构建

2. **服务器部署**:
   - 测试服务器可以使用热更新模式
   - 生产服务器建议使用生产模式
   - 定期重启容器以释放资源

3. **代码管理**:
   - 提交代码前在生产模式下测试
   - 使用 `.env` 文件管理环境配置
   - 不要将开发配置提交到生产分支
