# 视频号登录过期异常处理实现

## 🎯 需求描述

视频号执行 `download_single_video_data`、`download_follower_data`、`get_follower_data` 或其它类似操作时，如果检测到登录已过期，则应当抛出异常，上一级数据更新程序捕获异常后，不再做后续尝试。

## 🔧 实现方案

### 1. 创建登录过期异常类

**新增文件**: `app/exceptions.py`

```python
class LoginExpiredException(Exception):
    """登录过期异常
    
    当检测到平台登录状态已过期时抛出此异常。
    上级调用者应该捕获此异常并停止后续操作。
    """
    
    def __init__(self, platform: str, account_id: int = None, message: str = None):
        self.platform = platform
        self.account_id = account_id
        
        if message is None:
            if account_id:
                message = f"账号 {account_id} 在平台 {platform} 的登录状态已过期，请重新登录"
            else:
                message = f"平台 {platform} 的登录状态已过期，请重新登录"
        
        self.message = message
        super().__init__(self.message)
```

### 2. 修改视频号服务

**修改文件**: `app/services/wechat_channels_service.py`

#### 2.1 添加异常导入
```python
from app.exceptions import LoginExpiredException
```

#### 2.2 修改 `download_single_video_data` 方法
```python
# 检查登录状态
if not await self.check_existing_login():
    print("视频号登录状态无效，无法下载数据")
    # 更新数据库中的登录状态
    await self._update_database_login_status(False)
    # 抛出登录过期异常，让上级调用者停止后续尝试
    raise LoginExpiredException(
        platform="wechat_channels",
        account_id=self.account_id,
        message="视频号登录状态已过期，无法下载单篇视频数据"
    )
```

#### 2.3 修改 `download_follower_data` 方法
```python
# 检查登录状态
if not await self.check_login_status():
    print("视频号登录状态无效，无法下载关注者数据")
    # 更新数据库中的登录状态
    await self._update_database_login_status(False)
    # 抛出登录过期异常，让上级调用者停止后续尝试
    raise LoginExpiredException(
        platform="wechat_channels",
        account_id=self.account_id,
        message="视频号登录状态已过期，无法下载关注者数据"
    )
```

#### 2.4 修改 `get_follower_data` 方法
```python
# 检查登录状态
if not await self.check_login_status():
    print("视频号登录状态无效，无法下载关注者数据")
    # 抛出登录过期异常，让上级调用者停止后续尝试
    raise LoginExpiredException(
        platform="wechat_channels",
        account_id=self.account_id,
        message="视频号登录状态已过期，无法获取关注者数据"
    )
```

### 3. 修改数据更新服务

**修改文件**: `app/services/data_update_service.py`

#### 3.1 添加异常导入
```python
from app.exceptions import LoginExpiredException
```

#### 3.2 修改单篇视频数据下载异常处理
```python
try:
    video_result = await service.download_single_video_data(
        start_date=start_date,
        end_date=end_date,
        auto_import=True
    )
    # ... 处理结果
except LoginExpiredException as e:
    # 登录过期，立即返回，不再继续后续操作
    error_msg = f"账号 {account.name} 登录状态已过期: {e.message}"
    logger.error(error_msg)
    
    # 更新数据库中的登录状态
    try:
        account.login_status = False
        db.commit()
        logger.info(f"已更新账号 {account.name} 登录状态为失效")
    except Exception as db_e:
        logger.warning(f"更新账号 {account.name} 登录状态失败: {db_e}")
        db.rollback()
    
    return {"success": False, "error": error_msg, "login_expired": True}
```

#### 3.3 修改关注者数据下载异常处理
```python
try:
    follower_result = await asyncio.wait_for(
        service.download_follower_data(auto_import=True), timeout=120
    )
    # ... 处理结果
except LoginExpiredException as e:
    # 登录过期，立即返回，不再继续后续操作
    error_msg = f"账号 {account.name} 登录状态已过期: {e.message}"
    logger.error(error_msg)
    
    # 更新数据库中的登录状态
    try:
        account.login_status = False
        db.commit()
        logger.info(f"已更新账号 {account.name} 登录状态为失效")
    except Exception as db_e:
        logger.warning(f"更新账号 {account.name} 登录状态失败: {db_e}")
        db.rollback()
    
    return {"success": False, "error": error_msg, "login_expired": True}
except asyncio.TimeoutError:
    # ... 其他异常处理
```

### 4. 修改数据下载服务

**修改文件**: `app/services/data_download_service.py`

#### 4.1 添加异常导入
```python
from app.exceptions import LoginExpiredException
```

#### 4.2 修改视频号数据下载异常处理
```python
try:
    excel_data = await channels_service.download_single_video_data(
        start_date=start_date,
        end_date=end_date
    )
    # ... 处理结果
except LoginExpiredException as e:
    # 登录过期，立即返回错误，不再继续下载
    error_msg = f"视频号账号 {account.name} 登录状态已过期: {e.message}"
    logger.error(error_msg)
    return {"success": False, "error": error_msg, "login_expired": True}
except Exception as e:
    # ... 其他异常处理
```

### 5. 修改路由异常处理

**修改文件**: `app/routers/data_details.py`

#### 5.1 添加异常导入
```python
from app.exceptions import LoginExpiredException
```

#### 5.2 修改关注者数据抓取异常处理
```python
try:
    follower_data = await service.get_follower_data(auto_import=True)
    # ... 处理结果
except LoginExpiredException as e:
    raise HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail=f"登录状态已过期: {e.message}"
    )
except Exception as e:
    # ... 其他异常处理
```

## 🧪 测试验证

**测试文件**: `test/test_login_expired_exception.py`

测试覆盖：
1. ✅ 异常类的创建和属性
2. ✅ 视频号服务正确导入异常类
3. ✅ 数据更新服务正确导入异常类
4. ✅ 异常处理逻辑正确性
5. ✅ 集成场景测试

**测试结果**: 5/5 通过 ✅

## 🎯 功能特点

### 1. 立即停止机制
- 检测到登录过期时立即抛出异常
- 上级调用者捕获异常后停止所有后续操作
- 避免无效的重试和资源浪费

### 2. 数据库状态同步
- 抛出异常前更新数据库中的登录状态
- 确保数据库状态与实际登录状态一致

### 3. 详细错误信息
- 异常包含平台类型、账号ID和具体错误消息
- 便于调试和问题定位

### 4. 多层异常处理
- 服务层：抛出异常并更新数据库状态
- 业务层：捕获异常并停止后续操作
- 路由层：转换为HTTP异常返回给前端

## 🔄 执行流程

```mermaid
graph TD
    A[调用视频号方法] --> B[检查登录状态]
    B --> C{登录是否有效?}
    C -->|是| D[继续执行操作]
    C -->|否| E[更新数据库登录状态为False]
    E --> F[抛出LoginExpiredException]
    F --> G[上级捕获异常]
    G --> H[记录错误日志]
    H --> I[返回login_expired: true]
    I --> J[停止后续操作]
```

## 📝 使用示例

```python
try:
    # 调用视频号数据下载方法
    result = await service.download_single_video_data(
        start_date="2025-01-01",
        end_date="2025-01-31"
    )
except LoginExpiredException as e:
    # 登录过期，停止后续操作
    logger.error(f"登录已过期: {e.message}")
    return {"success": False, "error": e.message, "login_expired": True}
```

## ✅ 实现效果

1. **即时响应**: 登录过期时立即停止，不再浪费时间尝试
2. **状态同步**: 数据库登录状态实时更新
3. **错误明确**: 清晰的错误信息便于问题定位
4. **资源节约**: 避免无效操作，节约系统资源
5. **用户友好**: 前端可以根据 `login_expired` 标识引导用户重新登录

## 🔮 扩展建议

虽然当前实现专门针对视频号，但为了保持代码一致性，建议在其他平台也应用类似的异常处理模式：

### 小红书服务扩展
- `download_note_data_excel` 方法
- `download_account_overview_data` 方法
- `download_fans_data` 方法

### 微信公众号服务扩展
- 各种数据下载方法
- 统一的登录过期处理机制

### 统一异常处理框架
```python
# 可以考虑创建一个装饰器来简化异常处理
@login_required("wechat_channels")
async def download_single_video_data(self, ...):
    # 方法实现
    pass
```

## 📊 性能影响

- **响应时间**: 登录过期时立即返回，减少无效等待
- **资源消耗**: 避免无效的浏览器操作和网络请求
- **用户体验**: 快速反馈登录状态，引导用户重新登录

## 🔧 维护建议

1. **定期检查**: 确保异常处理逻辑与业务逻辑同步更新
2. **日志监控**: 监控登录过期异常的频率，优化登录保持机制
3. **测试覆盖**: 在集成测试中包含登录过期场景
4. **文档更新**: 保持API文档与异常处理行为一致

🎉 **登录过期异常处理功能已成功实现！**
