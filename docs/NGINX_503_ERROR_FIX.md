# Nginx 503错误修复总结

## 问题描述

前端请求获取微信登录二维码时，不到1分钟就返回503 Service Unavailable错误：

```
Status Code: 503 Service Unavailable
Content-Type: text/html;charset=utf-8
Content-Length: 8499
```

## 根本原因

**Nginx代理超时设置过短**：
- 全局代理超时：60秒
- 获取二维码操作需要：60-120秒（启动浏览器、导航页面、查找元素等）
- 结果：Nginx在60秒后超时，返回503错误页面

## 时间分析

### 获取二维码的完整流程耗时：
1. **浏览器启动**：10-30秒
2. **页面导航**：10-30秒
3. **等待页面加载**：5-15秒
4. **查找iframe和二维码元素**：5-15秒
5. **截取二维码**：1-5秒
6. **网络传输**：1-5秒

**总计**：32-100秒（在Docker环境中可能更长）

### 原有超时配置：
- Nginx全局超时：60秒 ❌
- 前端axios超时：120秒 ✅
- 后端导航超时：90秒（Docker环境）✅

**问题**：Nginx成为了瓶颈，在60秒时就中断了请求。

## 修复方案

### 1. 为微信登录二维码接口设置专门的超时配置

在 `nginx/conf.d/default.conf` 中添加：

```nginx
# 微信登录二维码接口 - 需要更长超时时间
location ~ ^/api/wechat/login/qrcode/ {
    limit_req zone=login burst=3 nodelay;
    
    proxy_pass http://backend;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Forwarded-Host $host;
    proxy_set_header X-Forwarded-Port $server_port;
    
    # 为获取二维码设置更长的超时时间（3分钟）
    proxy_connect_timeout 180s;
    proxy_send_timeout 180s;
    proxy_read_timeout 180s;
}
```

### 2. 提升全局代理超时时间

在 `nginx/nginx.conf` 中修改：

```nginx
# 代理配置
proxy_connect_timeout 90s;  # 从60s提升到90s
proxy_send_timeout 90s;     # 从60s提升到90s
proxy_read_timeout 90s;     # 从60s提升到90s
```

## 配置说明

### 超时参数含义：
- **proxy_connect_timeout**: 与后端服务器建立连接的超时时间
- **proxy_send_timeout**: 向后端服务器发送请求的超时时间
- **proxy_read_timeout**: 从后端服务器读取响应的超时时间

### 为什么选择180秒：
1. **足够的缓冲时间**：覆盖最坏情况下的处理时间
2. **用户体验平衡**：不会让用户等待过长时间
3. **与前端配置一致**：前端axios超时是120秒，nginx给更多缓冲

### 限流配置调整：
- 从 `burst=5` 降低到 `burst=3`
- 因为获取二维码是重操作，减少并发请求

## 修复后的完整超时链路

```
用户请求 → Nginx(180s) → 后端应用(90s) → Playwright浏览器
```

1. **前端axios超时**：120秒
2. **Nginx代理超时**：180秒（二维码接口）/ 90秒（其他接口）
3. **后端应用超时**：90秒（Docker环境）
4. **浏览器操作超时**：各种操作有独立超时

## 部署步骤

### 1. 更新配置文件
```bash
# 配置文件已更新：
# - nginx/nginx.conf
# - nginx/conf.d/default.conf
```

### 2. 重启Nginx服务
```bash
# 如果使用Docker Compose
docker-compose restart nginx

# 或者重新构建
docker-compose up -d --build nginx
```

### 3. 验证配置
```bash
# 检查Nginx配置语法
docker exec nginx_container nginx -t

# 查看Nginx错误日志
docker logs nginx_container
```

## 测试验证

### 1. 正常流程测试
- 获取二维码应该不再出现503错误
- 完整登录流程应该正常工作

### 2. 超时测试
- 模拟慢网络环境
- 验证在180秒内能完成操作

### 3. 并发测试
- 多个用户同时获取二维码
- 验证限流配置是否合理

## 监控建议

### 1. 添加Nginx访问日志监控
```nginx
log_format detailed '$remote_addr - $remote_user [$time_local] '
                   '"$request" $status $body_bytes_sent '
                   '"$http_referer" "$http_user_agent" '
                   'rt=$request_time uct="$upstream_connect_time" '
                   'uht="$upstream_header_time" urt="$upstream_response_time"';

access_log /var/log/nginx/access.log detailed;
```

### 2. 监控关键指标
- 请求响应时间
- 503错误率
- 上游服务器连接时间

### 3. 告警设置
- 503错误率超过阈值
- 平均响应时间超过阈值
- 上游服务器不可用

## 预期效果

修复后应该解决：
1. ✅ 不再出现503 Service Unavailable错误
2. ✅ 获取二维码请求能正常完成
3. ✅ 用户能看到正确的二维码
4. ✅ 完整的登录流程能正常工作

## 回滚方案

如果修复后出现问题，可以快速回滚：

```bash
# 恢复原始超时配置
proxy_connect_timeout 60s;
proxy_send_timeout 60s;
proxy_read_timeout 60s;

# 重启Nginx
docker-compose restart nginx
```

这个修复应该能彻底解决503错误问题，让获取二维码的流程正常工作。
