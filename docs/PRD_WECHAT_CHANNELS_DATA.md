# 微信视频号数据处理功能 PRD

## 1. 产品概述

### 1.1 功能背景
当前系统已实现微信公众号数据的自动下载和数据库存储功能，包括Excel文件下载、数据解析、数据库写入等完整流程。现需要参考微信公众号的处理逻辑，为微信视频号实现相同的数据处理功能。

### 1.2 功能目标
实现微信视频号数据从Excel文件下载到数据库存储的完整流程，包括：
- 创建视频号数据表结构
- 实现Excel数据解析逻辑
- 完成数据库写入功能
- 集成到现有数据下载服务

### 1.3 功能范围
本次实现仅针对微信视频号平台，数据类型为"单篇视频数据"。

## 2. 需求分析

### 2.1 现有系统架构
当前系统已具备以下基础设施：
- **数据下载服务**: `WeChatChannelsService` 已实现Excel文件下载
- **数据解析工具**: `ExcelDataParser` 提供Excel文件解析能力
- **数据导入服务**: `DataDetailsService` 提供统一的数据导入接口
- **数据库模型**: 已有微信公众号相关数据表作为参考

### 2.2 视频号数据字段
根据需求，视频号Excel文件包含以下字段：
- 视频描述 (String)
- 视频ID (String)
- 发布时间 (DateTime)
- 完播率 (String/Percentage)
- 平均播放时长 (Integer/Seconds)
- 播放量 (Integer)
- 推荐 (Integer)
- 喜欢 (Integer)
- 评论量 (Integer)
- 分享量 (Integer)
- 关注量 (Integer)
- 转发聊天和朋友圈 (Integer)
- 设为铃声 (Integer)
- 设为状态 (Integer)
- 设为朋友圈封面 (Integer)

## 3. 技术设计

### 3.1 数据库表设计

#### 3.1.1 表名
`wechat_channels_video_data`

#### 3.1.2 表结构
```sql
CREATE TABLE wechat_channels_video_data (
    id INT PRIMARY KEY AUTO_INCREMENT,
    account_id INT NOT NULL,
    video_description TEXT,                    -- 视频描述
    video_id VARCHAR(110),                     -- 视频ID
    publish_time DATETIME,                     -- 发布时间
    completion_rate VARCHAR(20),               -- 完播率
    avg_play_duration INT DEFAULT 0,           -- 平均播放时长(秒)
    play_count INT DEFAULT 0,                  -- 播放量
    recommend_count INT DEFAULT 0,             -- 推荐
    like_count INT DEFAULT 0,                  -- 喜欢
    comment_count INT DEFAULT 0,               -- 评论量
    share_count INT DEFAULT 0,                 -- 分享量
    follow_count INT DEFAULT 0,                -- 关注量
    forward_chat_moments INT DEFAULT 0,        -- 转发聊天和朋友圈
    set_as_ringtone INT DEFAULT 0,            -- 设为铃声
    set_as_status INT DEFAULT 0,              -- 设为状态
    set_as_moments_cover INT DEFAULT 0,       -- 设为朋友圈封面
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (account_id) REFERENCES platform_accounts(id),
    UNIQUE KEY unique_video_account (account_id, video_id, publish_time)
);
```

### 3.2 数据模型设计

#### 3.2.1 SQLAlchemy模型类
在 `app/models.py` 中新增：
```python
class WeChatChannelsVideoData(Base):
    """微信视频号单篇视频数据表"""
    __tablename__ = "wechat_channels_video_data"
    
    id = Column(Integer, primary_key=True, index=True)
    account_id = Column(Integer, ForeignKey("platform_accounts.id"), nullable=False)
    video_description = Column(Text)  # 视频描述
    video_id = Column(String(110))  # 视频ID
    publish_time = Column(DateTime)  # 发布时间
    completion_rate = Column(String(20))  # 完播率
    avg_play_duration = Column(Integer, default=0)  # 平均播放时长(秒)
    play_count = Column(Integer, default=0)  # 播放量
    recommend_count = Column(Integer, default=0)  # 推荐
    like_count = Column(Integer, default=0)  # 喜欢
    comment_count = Column(Integer, default=0)  # 评论量
    share_count = Column(Integer, default=0)  # 分享量
    follow_count = Column(Integer, default=0)  # 关注量
    forward_chat_moments = Column(Integer, default=0)  # 转发聊天和朋友圈
    set_as_ringtone = Column(Integer, default=0)  # 设为铃声
    set_as_status = Column(Integer, default=0)  # 设为状态
    set_as_moments_cover = Column(Integer, default=0)  # 设为朋友圈封面
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    account = relationship("PlatformAccount")
```

### 3.3 数据处理配置

#### 3.3.1 下载模板配置
在 `WeChatChannelsService` 中添加数据下载配置：
```python
DOWNLOAD_TEMPLATES = {
    'single_video': {
        'name': '单篇视频数据',
        'data_start_row': 2,  # 数据从第2行开始
        'fields': [
            ('视频描述', 1),      # 多行文本
            ('视频ID', 1),        # 文本
            ('发布时间', 1),      # 文本/日期
            ('完播率', 1),        # 文本/百分比
            ('平均播放时长', 2),   # 数字
            ('播放量', 2),        # 数字
            ('推荐', 2),          # 数字
            ('喜欢', 2),          # 数字
            ('评论量', 2),        # 数字
            ('分享量', 2),        # 数字
            ('关注量', 2),        # 数字
            ('转发聊天和朋友圈', 2), # 数字
            ('设为铃声', 2),      # 数字
            ('设为状态', 2),      # 数字
            ('设为朋友圈封面', 2), # 数字
        ]
    }
}
```

### 3.4 数据导入逻辑

#### 3.4.1 DataDetailsService扩展
在 `DataDetailsService` 中添加：
1. 模型映射：`'single_video': WeChatChannelsVideoData`
2. 导入方法：`_import_single_video()`
3. 数据解析和验证逻辑

#### 3.4.2 数据去重策略
使用 `(account_id, video_id, publish_time)` 作为唯一键，实现数据去重。

## 4. 实现计划

### 4.1 开发任务分解

#### Phase 1: 数据库层 (预计1天)
- [ ] 创建数据库表结构
- [ ] 添加SQLAlchemy模型类
- [ ] 编写数据库迁移脚本

#### Phase 2: 数据处理层 (预计1天)
- [ ] 扩展DataDetailsService支持视频号数据
- [ ] 实现数据解析和验证逻辑
- [ ] 添加数据导入方法

#### Phase 3: 服务集成 (预计0.5天)
- [ ] 更新WeChatChannelsService配置
- [ ] 集成到现有下载流程
- [ ] 测试数据导入功能

#### Phase 4: 测试验证 (预计0.5天)
- [ ] 单元测试
- [ ] 集成测试
- [ ] 端到端测试

### 4.2 技术风险评估

#### 4.2.1 低风险
- 数据库表设计：参考现有微信公众号表结构
- 数据导入逻辑：复用现有DataDetailsService框架

#### 4.2.2 中等风险
- Excel字段映射：需要确认实际Excel文件的字段名称和格式
- 数据类型转换：特别是时间格式和百分比格式的处理

### 4.3 验收标准

#### 4.3.1 功能验收
- [ ] 能够成功解析视频号Excel文件
- [ ] 数据正确写入数据库表
- [ ] 支持数据更新和去重
- [ ] 集成到现有下载服务

#### 4.3.2 性能验收
- [ ] 单次导入1000条数据耗时 < 30秒
- [ ] 数据库查询响应时间 < 1秒

#### 4.3.3 质量验收
- [ ] 代码覆盖率 > 80%
- [ ] 无严重Bug
- [ ] 符合现有代码规范

## 5. 后续扩展

### 5.1 数据分析功能
- 视频数据可视化图表
- 数据趋势分析
- 多账号数据对比

### 5.2 数据导出功能
- 支持导出为Excel格式
- 支持自定义时间范围查询
- 支持数据筛选和排序

## 6. 附录

### 6.1 参考文档
- 现有微信公众号数据处理逻辑
- DataDetailsService接口文档
- 数据库设计规范

### 6.2 相关文件
- `app/models.py` - 数据模型定义
- `app/services/data_details_service.py` - 数据导入服务
- `app/services/wechat_channels_service.py` - 视频号服务
- `app/utils/excel_parser.py` - Excel解析工具
