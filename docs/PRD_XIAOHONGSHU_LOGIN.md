# 小红书账号登录支持 - PRD文档

## 1. 项目背景

### 1.1 现状分析
当前系统已经支持微信公众号、服务号和视频号的扫码登录功能，具备完整的：
- 浏览器自动化框架（基于Playwright）
- 扫码登录流程（二维码获取、状态轮询、登录检测）
- 前端登录UI组件（登录弹窗、二维码显示、状态管理）
- 后端服务架构（服务类、路由、数据库模型）

### 1.2 需求背景
需要扩展系统支持小红书创作者平台（https://creator.xiaohongshu.com）的账号登录，为后续的数据采集功能做准备。

### 1.3 技术挑战
- 小红书登录页面的特殊性：未登录时自动跳转到登录页
- 二维码元素的动态CSS类名（如`div.css-1d81qt0`可能是随机生成）
- 需要处理登录页面的切换逻辑（密码登录 → 扫码登录）

## 2. 功能需求

### 2.1 核心功能
**目标**：实现小红书创作者平台的扫码登录功能，与现有微信登录功能保持一致的用户体验。

**功能范围**：
- 支持小红书平台账号的创建和管理
- 实现小红书扫码登录流程
- 登录状态检测和持久化
- 与现有UI组件无缝集成

### 2.2 详细功能规格

#### 2.2.1 账号管理扩展
- **平台类型**：在现有的`wechat_mp`、`wechat_service`、`wechat_channels`基础上，新增`xiaohongshu`平台类型
- **数据模型**：复用现有的`PlatformAccount`模型，无需修改数据库结构
- **前端支持**：在账号创建表单中添加小红书选项

#### 2.2.2 小红书登录服务类
**类名**：`XiaohongshuService`
**位置**：`app/services/xiaohongshu_service.py`

**核心方法**：
```python
class XiaohongshuService:
    async def get_login_qrcode(self) -> Optional[str]
    async def check_login_status(self, wait_for_redirect: bool = True, timeout: int = 30) -> bool
    async def check_existing_login(self) -> bool
    async def save_login_state(self) -> bool
    async def load_login_state(self) -> bool
    async def get_cookies(self) -> Optional[str]
    async def close(self) -> None
```

#### 2.2.3 登录流程设计

**步骤1：页面导航**
- 访问 `https://creator.xiaohongshu.com`
- 检测是否自动跳转到登录页面 `https://creator.xiaohongshu.com/login`

**步骤2：切换到扫码登录**
- 定位切换按钮：`#page > div > div.content > div.con > div.login-box-container > div > div > div > div > img`
- 点击切换到扫码登录模式

**步骤3：获取二维码**
- 等待二维码元素出现：`#page > div > div.content > div.con > div.login-box-container > div > div > div > div > div > div.css-1d81qt0 > img`
- 处理动态CSS类名的问题，使用更灵活的选择器策略
- 截图并转换为base64格式

**步骤4：登录状态检测**
- 轮询检查页面URL变化
- 检测是否跳转回创作者平台主页
- 保存登录状态和cookies

#### 2.2.4 选择器策略
由于`div.css-1d81qt0`可能是动态生成的，采用以下策略：

**主选择器**：
```javascript
// 优先使用精确选择器
"#page > div > div.content > div.con > div.login-box-container > div > div > div > div > div > div.css-1d81qt0 > img"
```

**备用选择器**：
```javascript
// 使用更宽泛的选择器作为备选
[
  "div[class*='css-'] img[src*='qr']",  // 包含css-的div下的二维码图片
  ".login-box-container img[src*='qr']", // 登录容器内的二维码图片
  "img[src*='qrcode']",  // 包含qrcode的图片
  "img[alt*='二维码']",   // alt属性包含二维码的图片
]
```

**切换按钮选择器**：
```javascript
// 切换到扫码登录的按钮
"#page > div > div.content > div.con > div.login-box-container > div > div > div > div > img"
```

## 3. 技术实现方案

### 3.1 后端实现

#### 3.1.1 服务类实现
**文件**：`app/services/xiaohongshu_service.py`
- 参考`WeChatMPService`和`WeChatChannelsService`的实现模式
- 使用Playwright进行浏览器自动化
- 实现独立的用户数据目录管理
- 支持headless和可视化模式

#### 3.1.2 路由扩展
**文件**：`app/routers/wechat.py`（或创建新的`xiaohongshu.py`）
- 复用现有的登录路由逻辑
- 在`/api/wechat/login/qrcode/{account_id}`中添加小红书平台支持
- 或创建独立的小红书路由：`/api/xiaohongshu/login/qrcode/{account_id}`

#### 3.1.3 平台类型支持
**修改位置**：
- `app/routers/wechat.py`：在平台类型判断中添加`xiaohongshu`
- 前端表单：在平台选择下拉框中添加小红书选项

### 3.2 前端实现

#### 3.2.1 平台选择扩展
**文件**：`frontend/src/pages/AccountManage.tsx`
- 在账号创建/编辑表单的平台选择中添加"小红书"选项
- 平台值：`xiaohongshu`

#### 3.2.2 UI适配
- 复用现有的登录弹窗组件
- 根据平台类型显示不同的提示文案
- 小红书登录提示："请使用小红书APP扫描二维码登录"

### 3.3 错误处理和容错机制

#### 3.3.1 选择器容错
- 实现多级选择器策略，从精确到模糊
- 添加重试机制，处理页面加载延迟
- 记录选择器匹配情况，便于调试

#### 3.3.2 登录状态检测
- 多种检测方式：URL变化、页面元素、cookies
- 设置合理的超时时间（建议2分钟）
- 提供登录失败的详细错误信息

## 4. 开发计划

### 4.1 开发阶段

**阶段1：后端服务实现**（预计2-3小时）
- [ ] 创建`XiaohongshuService`类
- [ ] 实现基础的页面导航和二维码获取
- [ ] 实现登录状态检测逻辑
- [ ] 添加错误处理和日志记录

**阶段2：路由集成**（预计1小时）
- [ ] 在现有路由中添加小红书平台支持
- [ ] 测试API接口功能
- [ ] 验证与数据库的集成

**阶段3：前端集成**（预计1小时）
- [ ] 添加小红书平台选项
- [ ] 测试登录流程的前端交互
- [ ] 优化用户体验和错误提示

**阶段4：测试和优化**（预计1-2小时）
- [ ] 端到端测试登录流程
- [ ] 处理边界情况和异常场景
- [ ] 性能优化和代码重构

### 4.2 测试计划

**单元测试**：
- 测试`XiaohongshuService`的各个方法
- 验证选择器策略的有效性
- 测试错误处理逻辑

**集成测试**：
- 测试完整的登录流程
- 验证前后端数据交互
- 测试登录状态持久化

**用户验收测试**：
- 在实际小红书环境中测试登录
- 验证用户体验的一致性
- 确认错误提示的友好性

## 5. 风险评估

### 5.1 技术风险
- **动态CSS类名**：小红书可能使用动态生成的CSS类名，需要实现灵活的选择器策略
- **页面结构变化**：小红书可能更新登录页面结构，需要定期维护选择器
- **反爬虫机制**：可能存在反自动化检测，需要模拟真实用户行为

### 5.2 缓解措施
- 实现多级选择器备选方案
- 添加详细的日志记录，便于问题诊断
- 使用真实的浏览器环境，降低被检测的风险
- 设置合理的延迟和重试机制

## 6. 验收标准

### 6.1 功能验收
- [ ] 能够成功获取小红书登录二维码
- [ ] 扫码后能够正确检测登录状态
- [ ] 登录状态能够持久化保存
- [ ] 前端UI与现有功能保持一致
- [ ] 错误处理友好且信息明确

### 6.2 性能验收
- [ ] 二维码获取时间 < 30秒
- [ ] 登录状态检测响应时间 < 5秒
- [ ] 系统稳定性不受影响

### 6.3 兼容性验收
- [ ] 与现有微信登录功能无冲突
- [ ] 支持headless和可视化模式
- [ ] 数据库兼容性良好

## 7. 后续规划

### 7.1 短期目标
- 完成基础登录功能
- 优化用户体验
- 完善错误处理

### 7.2 长期目标
- 实现小红书数据采集功能
- 添加小红书数据分析功能
- 支持更多小红书创作者工具

---

**总结**：本PRD基于现有的成熟架构，通过最小化的修改实现小红书登录支持。重点关注选择器的灵活性和错误处理的完善性，确保功能的稳定性和可维护性。
