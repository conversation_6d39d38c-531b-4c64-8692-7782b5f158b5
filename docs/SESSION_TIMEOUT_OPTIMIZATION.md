# Session时间配置优化

## 问题分析

### 原始问题
用户反馈系统总是需要频繁登录，session时间过短。

### 根本原因
系统使用JWT token进行身份认证，token过期时间配置过短：
- **生产环境**: `ACCESS_TOKEN_EXPIRE_MINUTES=30` (30分钟)
- **开发环境**: `ACCESS_TOKEN_EXPIRE_MINUTES=180` (3小时)

30分钟的过期时间对于日常使用来说太短，用户在正常操作过程中经常遇到token过期需要重新登录的情况。

## 技术实现

### 1. JWT Token配置

**配置文件**: `app/services/auth_service.py`
```python
# JWT配置
SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key-here")
ALGORITHM = os.getenv("ALGORITHM", "HS256")
ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30"))
```

**环境变量控制**:
- `.env.production`: 生产环境配置
- `.env`: 开发环境配置
- `docker-compose.yml`: Docker环境变量传递

### 2. Token创建逻辑

**文件**: `app/services/auth_service.py`
```python
@staticmethod
def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """创建访问令牌"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt
```

### 3. 前端Token处理

**文件**: `frontend/src/services/api.ts`
- 自动在请求头中添加Bearer token
- 401响应时自动清除token并跳转登录页
- 添加友好的过期提示信息

## 解决方案

### 1. 延长Token过期时间

**修改前**:
```env
ACCESS_TOKEN_EXPIRE_MINUTES=30  # 30分钟
```

**修改后**:
```env
ACCESS_TOKEN_EXPIRE_MINUTES=480  # 8小时
```

**优势**:
- 用户一个工作日内无需重复登录
- 减少用户操作中断
- 提升用户体验

### 2. 改进前端错误处理

**响应拦截器优化**:
```typescript
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      
      if (window.location.pathname !== '/login') {
        import('antd').then(({ message }) => {
          message.warning('登录已过期，请重新登录');
        });
        
        setTimeout(() => {
          window.location.href = '/login';
        }, 1500);
      }
    }
    return Promise.reject(error);
  }
);
```

### 3. 添加Token过期预警

**AuthContext增强**:
```typescript
const checkAuth = async () => {
  try {
    const token = localStorage.getItem('token');
    if (token) {
      // 检查token是否即将过期（提前5分钟提醒）
      const tokenPayload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Math.floor(Date.now() / 1000);
      const timeUntilExpiry = tokenPayload.exp - currentTime;
      
      if (timeUntilExpiry < 300 && timeUntilExpiry > 0) {
        message.warning('登录即将过期，请及时保存工作并重新登录');
      }
      
      const response = await api.get('/auth/me');
      setUser(response.data);
    }
  } catch (error) {
    localStorage.removeItem('token');
  } finally {
    setLoading(false);
  }
};
```

## 配置建议

### 不同环境的推荐配置

1. **开发环境**: `ACCESS_TOKEN_EXPIRE_MINUTES=480` (8小时)
   - 开发过程中减少登录中断
   - 便于调试和测试

2. **生产环境**: `ACCESS_TOKEN_EXPIRE_MINUTES=480` (8小时)
   - 平衡安全性和用户体验
   - 适合日常办公使用

3. **高安全要求环境**: `ACCESS_TOKEN_EXPIRE_MINUTES=120` (2小时)
   - 提高安全性
   - 配合自动刷新机制

### 安全考虑

1. **Token存储**: 使用localStorage存储，页面刷新后保持登录状态
2. **自动清理**: 401响应时自动清除过期token
3. **HTTPS**: 生产环境必须使用HTTPS传输token
4. **Secret Key**: 使用强随机密钥，定期轮换

## 部署更新

### 1. 更新生产环境配置
```bash
# 修改 .env.production 文件
ACCESS_TOKEN_EXPIRE_MINUTES=480

# 重新部署服务
docker-compose down
docker-compose up -d
```

### 2. 验证配置生效
```bash
# 检查环境变量
docker exec -it social_media_backend env | grep ACCESS_TOKEN

# 测试登录token过期时间
curl -X POST "http://localhost:8000/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "testuser", "password": "password"}'
```

## 用户体验改进

### 1. 友好的过期提示
- 登录过期时显示明确的提示信息
- 延迟跳转，让用户看到提示

### 2. 过期预警
- 提前5分钟提醒用户登录即将过期
- 给用户时间保存工作进度

### 3. 无感知刷新（未来优化）
- 可考虑实现refresh token机制
- 在token即将过期时自动刷新

## 监控和维护

### 1. 日志监控
- 监控401错误频率
- 跟踪用户登录频次

### 2. 用户反馈
- 收集用户对登录体验的反馈
- 根据使用模式调整过期时间

### 3. 安全审计
- 定期检查token配置的安全性
- 评估过期时间是否合适

## 总结

通过将JWT token过期时间从30分钟延长到8小时，并改进前端的错误处理和用户提示，显著提升了用户体验：

✅ **解决频繁登录问题**: 8小时有效期覆盖正常工作时间
✅ **友好的错误提示**: 明确告知用户登录过期原因
✅ **过期预警机制**: 提前提醒用户保存工作
✅ **平衡安全性**: 在安全性和用户体验间找到平衡点

这些改进让用户可以更专注于业务操作，而不会被频繁的登录中断打扰。
