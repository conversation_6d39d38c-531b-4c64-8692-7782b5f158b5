# 数据更新任务明细功能实现文档

## 📋 功能概述

本次实现为数据更新功能增加了任务明细管理，支持：
- 显示各账号和数据类型的详细任务列表
- 实时更新任务状态和进度
- 失败任务显示错误信息
- 支持单个任务项的重试功能

## 🏗️ 架构设计

### 数据库层
- **新增表**: `data_update_task_items` - 存储任务明细信息
- **关联关系**: 与 `data_update_records` 和 `platform_accounts` 建立外键关系
- **索引优化**: 为常用查询字段创建索引

### 服务层
- **平台服务基类**: `PlatformServiceBase` - 统一的平台服务接口
- **服务工厂**: `PlatformServiceFactory` - 创建和管理平台服务实例
- **任务管理**: `DataUpdateService` - 扩展任务明细管理功能

### API层
- **任务明细查询**: `GET /data-update/tasks/{record_id}/items`
- **任务汇总统计**: `GET /data-update/tasks/{record_id}/summary`
- **单项重试**: `POST /data-update/tasks/items/{item_id}/retry`

### 前端层
- **任务明细表格**: 显示账号、平台、数据类型、状态等信息
- **状态统计**: 实时显示各状态的任务数量
- **重试功能**: 失败任务提供重试按钮

## 🔧 技术实现

### 1. 数据库模型

```python
class DataUpdateTaskItem(Base):
    __tablename__ = "data_update_task_items"
    
    id = Column(Integer, primary_key=True, index=True)
    update_record_id = Column(Integer, ForeignKey("data_update_records.id"))
    account_id = Column(Integer, ForeignKey("platform_accounts.id"))
    account_name = Column(String(100))
    platform = Column(String(50))
    data_type = Column(String(50))
    data_type_display = Column(String(100))
    status = Column(String(20), default='pending')
    error_message = Column(Text)
    started_at = Column(DateTime)
    completed_at = Column(DateTime)
    # ... 其他字段
```

### 2. 平台服务统一接口

```python
class PlatformServiceBase(ABC):
    @abstractmethod
    async def download_single_data_type(self, data_type: str, **kwargs) -> DataDownloadResult:
        """根据数据类型下载对应数据的统一入口"""
        pass
    
    @abstractmethod
    def get_supported_data_types(self) -> List[Tuple[str, str]]:
        """获取支持的数据类型列表"""
        pass
```

### 3. 任务状态管理

```python
class TaskItemStatus:
    PENDING = 'pending'      # 等待执行
    RUNNING = 'running'      # 正在执行
    COMPLETED = 'completed'  # 执行成功
    FAILED = 'failed'        # 执行失败
    RETRYING = 'retrying'    # 重试中
```

### 4. 重试机制

```python
async def retry_task_item(item_id: int, user_id: int) -> Dict[str, Any]:
    # 1. 验证任务项和权限
    # 2. 更新状态为重试中
    # 3. 创建平台服务实例
    # 4. 加载登录状态
    # 5. 执行特定数据类型下载
    # 6. 更新任务状态
    # 7. 返回结果
```

## 📊 支持的平台和数据类型

### 视频号 (wechat_channels)
- `single_video`: 单篇视频数据
- `follower_data`: 关注者数据

### 小红书 (xiaohongshu)
- `note_data`: 笔记数据
- `account_overview`: 账号概览
- `fans_data`: 粉丝数据

### 微信公众号 (wechat_mp)
- `content_trend`: 内容趋势
- `content_source`: 内容来源
- `content_detail`: 内容详情
- `user_channel`: 用户渠道
- `user_source`: 用户来源

## 🎯 核心功能

### 1. 任务明细创建
- 在数据更新任务启动时，自动为每个账号的每种数据类型创建任务项
- 支持多平台、多数据类型的任务拆分

### 2. 状态实时更新
- 任务执行过程中实时更新任务项状态
- 支持轮询机制，前端自动刷新任务状态

### 3. 错误信息记录
- 详细记录每个任务项的错误信息
- 支持登录过期异常的特殊处理

### 4. 单项重试功能
- 失败的任务项可以单独重试
- 重试时重新加载登录状态
- 只执行特定数据类型的下载

### 5. 统计和汇总
- 实时统计各状态的任务数量
- 计算成功率和完成进度
- 支持分页查询和状态筛选

## 🧪 测试验证

### 测试覆盖
- ✅ 平台服务工厂测试
- ✅ 任务项创建测试
- ✅ 任务项状态更新测试
- ✅ 获取任务明细测试
- ✅ 服务创建测试

### 测试结果
```
🎉 测试完成: 5/5 通过
✅ 所有测试通过！任务明细功能正常工作
```

## 📱 前端界面

### 任务明细表格
- 显示账号、平台、数据类型、状态等信息
- 支持分页和状态筛选
- 错误信息悬浮显示
- 重试按钮仅对失败任务显示

### 状态统计
- 彩色标签显示各状态数量
- 实时更新统计信息
- 直观的进度展示

### 交互功能
- 点击重试按钮执行单项重试
- 自动刷新任务状态
- 响应式布局适配

## 🔄 工作流程

1. **任务启动**: 创建更新记录和所有任务项
2. **任务执行**: 逐个处理任务项，更新状态
3. **状态监控**: 前端轮询获取最新状态
4. **错误处理**: 记录错误信息，标记失败状态
5. **重试机制**: 用户可重试失败的任务项
6. **完成汇总**: 统计最终结果和成功率

## 🚀 部署和使用

### 数据库迁移
```bash
python migrations/add_data_update_task_items.py
```

### 启动服务
```bash
python -m uvicorn main:app --reload
```

### 功能测试
```bash
python test/test_task_items.py
```

## 📈 性能优化

- 数据库索引优化查询性能
- 分页查询避免大量数据加载
- 异步处理提高响应速度
- 状态缓存减少数据库访问

## 🔒 安全考虑

- 用户权限验证
- 任务项访问控制
- 错误信息脱敏
- 重试频率限制

## 🎉 总结

本次实现成功为数据更新功能增加了完整的任务明细管理系统，包括：

- **数据库层**: 新增任务明细表和关联关系
- **服务层**: 统一平台接口和任务管理
- **API层**: 完整的任务明细相关接口
- **前端层**: 直观的任务明细界面和交互

功能特点：
- ✅ 任务明细可视化
- ✅ 实时状态更新
- ✅ 单项重试功能
- ✅ 错误信息展示
- ✅ 统计和汇总
- ✅ 多平台支持
- ✅ 完整测试覆盖

现在用户可以：
1. 查看每个账号和数据类型的详细执行状态
2. 了解具体的失败原因
3. 对失败的任务项进行单独重试
4. 实时监控任务进度和统计信息

这大大提升了数据更新功能的可用性和用户体验！
