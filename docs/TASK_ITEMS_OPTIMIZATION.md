# 任务明细显示优化

## 优化目标

1. **状态排序优化**: 进行中的任务放在最上面，然后是待处理的，已完成条目放在后面
2. **增量更新机制**: 不要每次获取结果后都更新整个表格，只更新必要列的值，避免表格刷新和分页问题

## 技术实现

### 1. 前端状态排序

#### 状态优先级定义
```typescript
const getStatusPriority = (status: string): number => {
  const priorityMap: Record<string, number> = {
    'running': 1,    // 进行中
    'retrying': 2,   // 重试中
    'pending': 3,    // 待处理
    'failed': 4,     // 失败
    'cancelled': 5,  // 已取消
    'completed': 6   // 已完成
  };
  return priorityMap[status] || 7;
};
```

#### 排序函数
```typescript
const sortTaskItems = (items: TaskItem[]): TaskItem[] => {
  return [...items].sort((a, b) => {
    const priorityA = getStatusPriority(a.status);
    const priorityB = getStatusPriority(b.status);
    
    if (priorityA !== priorityB) {
      return priorityA - priorityB;
    }
    
    // 同状态按ID排序
    return a.id - b.id;
  });
};
```

### 2. 增量更新机制

#### 修改后的fetchTaskItems函数
```typescript
const fetchTaskItems = async (recordId: number, page: number = 1, isUpdate: boolean = false) => {
  if (!recordId) return;

  if (!isUpdate) {
    setTaskItemsLoading(true);
  }
  
  try {
    const response = await api.get(`/data-update/tasks/${recordId}/items`, {
      params: { page, page_size: 20 }
    });

    if (response.data.success) {
      const sortedItems = sortTaskItems(response.data.items);
      
      if (isUpdate && taskItems.length > 0) {
        // 增量更新：只更新变化的项目
        setTaskItems(prevItems => {
          const updatedItems = [...prevItems];
          
          sortedItems.forEach(newItem => {
            const existingIndex = updatedItems.findIndex(item => item.id === newItem.id);
            if (existingIndex >= 0) {
              // 只更新状态相关字段
              const existingItem = updatedItems[existingIndex];
              if (existingItem.status !== newItem.status ||
                  existingItem.error_message !== newItem.error_message ||
                  existingItem.started_at !== newItem.started_at ||
                  existingItem.completed_at !== newItem.completed_at) {
                updatedItems[existingIndex] = { ...existingItem, ...newItem };
              }
            } else {
              // 新增项目
              updatedItems.push(newItem);
            }
          });
          
          return sortTaskItems(updatedItems);
        });
      } else {
        // 全量更新
        setTaskItems(sortedItems);
      }
      
      setTaskItemsTotal(response.data.total);
      setTaskItemsPage(page);
      setTaskItemsStats(response.data.stats);
    }
  } catch (error) {
    console.error('获取任务明细失败:', error);
    if (!isUpdate) {
      message.error('获取任务明细失败');
    }
  } finally {
    if (!isUpdate) {
      setTaskItemsLoading(false);
    }
  }
};
```

#### 调用方式区分
```typescript
// 全量更新（用户主动操作）
fetchTaskItems(taskId, page, false);

// 增量更新（轮询更新）
fetchTaskItems(taskId, page, true);
```

### 3. 后端排序优化

#### 数据库查询排序
```python
# 按状态优先级排序：进行中 > 重试中 > 待处理 > 失败 > 已取消 > 已完成
from sqlalchemy import case

status_priority = case(
    (DataUpdateTaskItem.status == TaskItemStatus.RUNNING, 1),
    (DataUpdateTaskItem.status == TaskItemStatus.RETRYING, 2),
    (DataUpdateTaskItem.status == TaskItemStatus.PENDING, 3),
    (DataUpdateTaskItem.status == TaskItemStatus.FAILED, 4),
    (DataUpdateTaskItem.status == TaskItemStatus.CANCELLED, 5),
    (DataUpdateTaskItem.status == TaskItemStatus.COMPLETED, 6),
    else_=7
)

# 分页查询，按状态优先级和ID排序
items = query.order_by(status_priority, DataUpdateTaskItem.id).offset(offset).limit(page_size).all()
```

## 用户体验改进

### 1. 状态排序优势

- ✅ **重要任务优先**: 进行中和待处理的任务始终在顶部
- ✅ **状态清晰**: 用户可以快速找到需要关注的任务
- ✅ **逻辑合理**: 按照任务处理的优先级排序

### 2. 增量更新优势

- ✅ **减少闪烁**: 表格不会因为数据更新而重新渲染
- ✅ **保持分页**: 用户的分页状态不会被重置
- ✅ **性能优化**: 只更新变化的数据，减少DOM操作
- ✅ **用户友好**: 用户可以正常点击分页和操作按钮

### 3. 轮询策略

```typescript
// 轮询时使用增量更新
const interval = setInterval(async () => {
  try {
    const response = await api.get(`/data-update/status/${taskId}`);
    if (response.data.success) {
      setCurrentTask(response.data);
      
      // 增量更新任务明细
      fetchTaskItems(taskId, taskItemsPage, true);
      
      // 任务完成时停止轮询
      if (response.data.status === 'completed' || response.data.status === 'failed') {
        clearInterval(interval);
        setPollingInterval(null);
      }
    }
  } catch (error) {
    console.error('轮询更新失败:', error);
  }
}, 3000);
```

## 技术细节

### 1. 状态映射

前端和后端保持一致的状态定义：
- `pending`: 待处理
- `running`: 进行中
- `retrying`: 重试中
- `completed`: 已完成
- `failed`: 失败
- `cancelled`: 已取消

### 2. 数据比较逻辑

增量更新时只比较关键字段：
- `status`: 任务状态
- `error_message`: 错误信息
- `started_at`: 开始时间
- `completed_at`: 完成时间

### 3. 性能考虑

- **前端排序**: 在前端进行排序，减少后端计算压力
- **后端排序**: 在数据库层面排序，确保分页的正确性
- **双重保障**: 前后端都实现排序，确保数据一致性

## 部署和测试

### 1. 功能测试

- ✅ 验证状态排序是否正确
- ✅ 验证增量更新不会影响分页
- ✅ 验证轮询更新的流畅性
- ✅ 验证用户操作的响应性

### 2. 性能测试

- ✅ 测试大量任务项的排序性能
- ✅ 测试增量更新的内存使用
- ✅ 测试轮询频率对系统的影响

### 3. 用户体验测试

- ✅ 验证表格不会频繁刷新
- ✅ 验证分页功能正常工作
- ✅ 验证重要任务始终在顶部

## 总结

通过实现状态优先级排序和增量更新机制，显著提升了任务明细页面的用户体验：

1. **状态排序**: 重要任务（进行中、待处理）始终在顶部，用户可以快速关注需要处理的任务
2. **增量更新**: 避免了表格的频繁刷新，保持了分页状态，提升了操作的流畅性
3. **性能优化**: 减少了不必要的DOM操作和数据传输，提升了页面响应速度

这些优化让用户在监控和管理数据更新任务时有了更好的体验，特别是在处理大量任务项时效果更加明显。
