# 微信视频号数据下载故障排除指南

## 常见问题及解决方案

### 1. 网络超时问题

**症状：**
```
Page.goto: Timeout 15000ms exceeded.
Call log:
  - navigating to "https://channels.weixin.qq.com/platform/", waiting until "domcontentloaded"
```

**原因：**
- Docker/WSL2环境网络配置问题
- 防火墙阻止外网访问
- DNS解析问题
- 网络代理设置问题

**解决方案：**

1. **增加超时时间**
   ```bash
   # 设置环境变量
   export WECHAT_CHANNELS_NAVIGATION_TIMEOUT=120000  # 2分钟
   export WECHAT_CHANNELS_MAX_RETRIES=5              # 增加重试次数
   ```

2. **检查Docker网络配置**
   ```bash
   # 检查Docker网络
   docker network ls
   docker network inspect bridge
   
   # 测试网络连接
   docker exec -it your_container ping channels.weixin.qq.com
   ```

3. **配置DNS**
   ```bash
   # 在Docker容器中设置DNS
   echo "nameserver 8.8.8.8" >> /etc/resolv.conf
   echo "nameserver 114.114.114.114" >> /etc/resolv.conf
   ```

4. **代理配置**
   ```bash
   # 如果需要通过代理访问
   export WECHAT_CHANNELS_BROWSER_ARGS="--proxy-server=http://your-proxy:8080"
   ```

### 2. 浏览器启动失败

**症状：**
```
Browser startup failed
Chromium compatibility check failed
```

**解决方案：**

1. **安装必要的依赖**
   ```bash
   # Ubuntu/Debian
   apt-get update
   apt-get install -y \
       libnss3 \
       libatk-bridge2.0-0 \
       libdrm2 \
       libxkbcommon0 \
       libxcomposite1 \
       libxdamage1 \
       libxrandr2 \
       libgbm1 \
       libxss1 \
       libasound2
   ```

2. **检查系统架构**
   ```bash
   uname -m  # 确保是x86_64
   ```

3. **增加内存限制**
   ```bash
   # Docker运行时增加内存
   docker run --memory=2g --memory-swap=2g your_image
   ```

### 3. 登录状态检查失败

**症状：**
```
视频号登录状态无效，无法下载数据
```

**解决方案：**

1. **重新登录**
   - 删除保存的登录状态文件
   - 重新执行登录流程

2. **检查登录状态文件**
   ```bash
   ls -la login_states/
   # 确保有对应账号的登录状态文件
   ```

3. **增加登录检查重试**
   ```bash
   export WECHAT_CHANNELS_MAX_RETRIES=5
   export WECHAT_CHANNELS_RETRY_DELAY=15
   ```

### 4. 请求拦截失败

**症状：**
```
AttributeError: 'Route' object has no attribute 'url'
```

**解决方案：**
- 这个问题已经在最新版本中修复
- 确保使用正确的Playwright API调用

### 5. 数据下载失败

**症状：**
```
检测到下载事件但无法获取文件
```

**解决方案：**

1. **检查下载目录权限**
   ```bash
   chmod 755 temp_downloads/
   chown -R app:app temp_downloads/
   ```

2. **增加下载等待时间**
   ```python
   # 在下载后增加等待时间
   await asyncio.sleep(10)
   ```

## 环境配置

### Docker环境推荐配置

```dockerfile
# Dockerfile
FROM python:3.11-slim

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    libnss3 \
    libatk-bridge2.0-0 \
    libdrm2 \
    libxkbcommon0 \
    libxcomposite1 \
    libxdamage1 \
    libxrandr2 \
    libgbm1 \
    libxss1 \
    libasound2 \
    && rm -rf /var/lib/apt/lists/*

# 设置环境变量
ENV WECHAT_CHANNELS_NAVIGATION_TIMEOUT=120000
ENV WECHAT_CHANNELS_MAX_RETRIES=5
ENV WECHAT_CHANNELS_DEBUG=true
```

### docker-compose.yml 配置

```yaml
version: '3.8'
services:
  app:
    build: .
    environment:
      - WECHAT_CHANNELS_NAVIGATION_TIMEOUT=120000
      - WECHAT_CHANNELS_MAX_RETRIES=5
      - WECHAT_CHANNELS_RETRY_DELAY=10
      - WECHAT_CHANNELS_DEBUG=true
    volumes:
      - ./login_states:/app/login_states
      - ./temp_downloads:/app/temp_downloads
    shm_size: 2gb  # 增加共享内存
    mem_limit: 2g  # 限制内存使用
```

## 调试方法

### 1. 启用调试模式

```bash
export WECHAT_CHANNELS_DEBUG=true
```

### 2. 运行测试脚本

```bash
python test_server_environment.py
```

### 3. 检查日志

```bash
# 查看详细日志
tail -f logs/app.log

# 查看Docker容器日志
docker logs -f container_name
```

### 4. 网络诊断

```bash
# 测试DNS解析
nslookup channels.weixin.qq.com

# 测试网络连接
curl -I https://channels.weixin.qq.com/platform/

# 检查代理设置
echo $http_proxy
echo $https_proxy
```

## 性能优化

### 1. 内存优化

```bash
# 设置浏览器内存限制
export WECHAT_CHANNELS_BROWSER_ARGS="--max_old_space_size=2048,--memory-pressure-off"
```

### 2. 网络优化

```bash
# 禁用不必要的功能
export WECHAT_CHANNELS_BROWSER_ARGS="--disable-images,--disable-javascript"
```

### 3. 并发控制

```python
# 限制同时运行的下载任务数量
MAX_CONCURRENT_DOWNLOADS = 2
```

## 联系支持

如果以上解决方案都无法解决问题，请提供以下信息：

1. 系统环境信息（操作系统、Docker版本等）
2. 完整的错误日志
3. 网络配置信息
4. 使用的环境变量配置

这将帮助我们更快地定位和解决问题。
