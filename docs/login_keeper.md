# 登录状态维持服务

## 概述

登录状态维持服务是一个后台程序，定期访问各平台页面来维持用户的登录状态，确保在需要进行数据操作时，登录状态仍然有效。

## 功能特性

### 🔄 自动维持
- 定期（默认30分钟）自动访问平台页面
- 智能检测登录状态是否有效
- 自动更新数据库中的登录状态信息

### 🎯 多平台支持
- **微信公众号** (wechat_mp)
- **小红书** (xiaohongshu) 
- **微信视频号** (wechat_channels)

### 🛡️ 智能防护
- 随机选择页面访问，避免被检测
- 随机停留时间，模拟真实用户行为
- 智能重试机制，提高成功率

### 📊 完整监控
- 详细的操作日志记录
- 平台和账号维度的统计分析
- 实时状态监控

## 配置说明

### 环境变量配置

```bash
# 是否启用登录状态维持服务
LOGIN_KEEPER_ENABLED=true

# 维持间隔（分钟）
LOGIN_KEEPER_INTERVAL=30

# 启用的平台（逗号分隔）
LOGIN_KEEPER_PLATFORMS=wechat_mp,xiaohongshu,wechat_channels

# 最大重试次数
LOGIN_KEEPER_MAX_RETRIES=3

# 同时处理的账号数量
LOGIN_KEEPER_CONCURRENT=3

# 浏览器超时时间（秒）
LOGIN_KEEPER_TIMEOUT=60

# 日志级别
LOGIN_KEEPER_LOG_LEVEL=INFO
```

### 页面配置

每个平台都配置了多个可访问的页面，系统会根据权重随机选择：

- **高权重页面**：首页等常用页面
- **中权重页面**：管理页面
- **低权重页面**：设置页面等

## API接口

### 服务控制

```http
# 获取服务状态
GET /api/login-keeper/status

# 启动服务
POST /api/login-keeper/start

# 停止服务
POST /api/login-keeper/stop

# 暂停任务
POST /api/login-keeper/pause

# 恢复任务
POST /api/login-keeper/resume

# 手动触发
POST /api/login-keeper/trigger

# 修改执行间隔
POST /api/login-keeper/modify-interval?minutes=60
```

### 监控和统计

```http
# 获取操作日志
GET /api/login-keeper/logs?limit=50

# 获取平台统计
GET /api/login-keeper/stats/platform?days=7

# 获取账号统计
GET /api/login-keeper/stats/account/{account_id}?days=7

# 获取账号列表
GET /api/login-keeper/accounts

# 测试账号登录状态
POST /api/login-keeper/test-account/{account_id}

# 获取配置信息
GET /api/login-keeper/config
```

## 工作原理

### 1. 定时调度
- 使用APScheduler进行任务调度
- 支持动态修改执行间隔
- 防止任务重复执行

### 2. 状态检测
- 加载保存的登录状态文件
- 访问随机选择的平台页面
- 通过页面内容验证登录状态

### 3. 状态更新
- 更新数据库中的登录状态
- 保存最新的浏览器会话信息
- 记录详细的操作日志

### 4. 错误处理
- 自动重试机制
- 详细的错误日志记录
- 优雅的资源清理

## 日志和监控

### 操作记录
每次维持操作都会记录：
- 账号信息
- 访问的页面
- 操作结果
- 响应时间
- 错误信息

### 统计分析
提供多维度统计：
- 成功率统计
- 平均响应时间
- 平台对比分析
- 趋势分析

## 最佳实践

### 1. 合理设置间隔
- 生产环境建议30-60分钟
- 开发环境可以设置更短间隔进行测试
- 避免过于频繁的访问

### 2. 监控服务状态
- 定期检查服务运行状态
- 关注错误日志和统计数据
- 及时处理登录失效的账号

### 3. 资源管理
- 合理控制并发账号数量
- 监控系统资源使用情况
- 定期清理过期的日志记录

## 故障排除

### 常见问题

1. **服务无法启动**
   - 检查APScheduler依赖是否安装
   - 确认配置文件格式正确
   - 查看应用启动日志

2. **登录状态检测失败**
   - 检查网络连接
   - 确认平台页面结构未发生变化
   - 查看浏览器超时设置

3. **数据库更新失败**
   - 检查数据库连接
   - 确认表结构是否正确
   - 查看数据库权限设置

### 日志分析
- 查看应用日志了解详细错误信息
- 使用API接口获取操作记录
- 分析统计数据找出问题模式

## 安全考虑

- 所有操作都在headless模式下进行
- 使用随机化策略避免被检测
- 不存储敏感的登录凭据
- 支持访问频率控制
