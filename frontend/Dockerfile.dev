# 前端开发环境Dockerfile - 支持热更新
FROM node:24-alpine

WORKDIR /app

# 配置npm使用国内镜像源（加速下载）
RUN npm config set registry https://registry.npmmirror.com

# 复制package文件
COPY package*.json ./
COPY .npmrc ./

# 创建开发环境配置
RUN echo "# React开发服务器配置" > .env.development && \
    echo "DANGEROUSLY_DISABLE_HOST_CHECK=true" >> .env.development && \
    echo "DISABLE_HOST_CHECK=true" >> .env.development && \
    echo "CHOKIDAR_USEPOLLING=true" >> .env.development && \
    echo "WATCHPACK_POLLING=true" >> .env.development && \
    echo "FAST_REFRESH=true" >> .env.development && \
    echo "HOST=0.0.0.0" >> .env.development && \
    echo "PORT=3000" >> .env.development && \
    echo "WDS_SOCKET_HOST=localhost" >> .env.development && \
    echo "WDS_SOCKET_PORT=3000" >> .env.development

# 安装依赖
RUN npm ci

# 确保http-proxy-middleware已安装
RUN npm list http-proxy-middleware || npm install http-proxy-middleware@^2.0.6

# 复制源代码（开发环境会通过volume挂载覆盖）
COPY . .

# 复制启动脚本并设置权限
COPY start.sh /app/start.sh
RUN chmod +x /app/start.sh

# 暴露开发服务器端口
EXPOSE 3000

# 设置环境变量
ENV CHOKIDAR_USEPOLLING=true
ENV WATCHPACK_POLLING=true
ENV FAST_REFRESH=true
ENV NODE_ENV=development

# 使用智能启动脚本
CMD ["/app/start.sh"]
