import React from 'react';
import { Card, Row, Col, Progress, Statistic, Tag, Empty } from 'antd';
import { CheckCircleOutlined, CloseCircleOutlined, ExclamationCircleOutlined, ClockCircleOutlined } from '@ant-design/icons';
import { PlatformStats } from '../services/loginKeeperService';

interface LoginKeeperChartsProps {
  platformStats: PlatformStats;
  loading?: boolean;
}

const LoginKeeperCharts: React.FC<LoginKeeperChartsProps> = ({ platformStats, loading = false }) => {
  // 获取平台名称
  const getPlatformName = (platform: string) => {
    const platformNames: { [key: string]: string } = {
      'wechat_mp': '微信公众号',
      'xiaohongshu': '小红书',
      'wechat_channels': '微信视频号'
    };
    return platformNames[platform] || platform;
  };

  // 获取平台图标颜色
  const getPlatformColor = (platform: string) => {
    const colors: { [key: string]: string } = {
      'wechat_mp': '#07c160',
      'xiaohongshu': '#ff2442',
      'wechat_channels': '#576b95'
    };
    return colors[platform] || '#1890ff';
  };

  // 获取成功率颜色
  const getSuccessRateColor = (rate: number) => {
    if (rate >= 90) return '#52c41a';
    if (rate >= 70) return '#faad14';
    return '#ff4d4f';
  };

  // 计算总体统计
  const totalStats = Object.values(platformStats).reduce(
    (acc, stats) => ({
      total_attempts: acc.total_attempts + stats.total_attempts,
      successful: acc.successful + stats.successful,
      failed: acc.failed + stats.failed,
      expired: acc.expired + stats.expired,
      avg_response_time: acc.avg_response_time + stats.avg_response_time
    }),
    { total_attempts: 0, successful: 0, failed: 0, expired: 0, avg_response_time: 0 }
  );

  const platformCount = Object.keys(platformStats).length;
  const overallSuccessRate = totalStats.total_attempts > 0 
    ? (totalStats.successful / totalStats.total_attempts) * 100 
    : 0;
  const avgResponseTime = platformCount > 0 
    ? totalStats.avg_response_time / platformCount 
    : 0;

  if (Object.keys(platformStats).length === 0) {
    return (
      <Card title="平台统计" loading={loading}>
        <Empty description="暂无统计数据" />
      </Card>
    );
  }

  return (
    <div>
      {/* 总体统计 */}
      <Card title="总体统计" loading={loading} style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={6}>
            <Statistic
              title="总尝试次数"
              value={totalStats.total_attempts}
              prefix={<ClockCircleOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="总体成功率"
              value={overallSuccessRate.toFixed(1)}
              suffix="%"
              valueStyle={{ color: getSuccessRateColor(overallSuccessRate) }}
              prefix={<CheckCircleOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="平均响应时间"
              value={avgResponseTime.toFixed(2)}
              suffix="s"
              prefix={<ClockCircleOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="活跃平台"
              value={platformCount}
              suffix="个"
            />
          </Col>
        </Row>
      </Card>

      {/* 平台详细统计 */}
      <Row gutter={[16, 16]}>
        {Object.entries(platformStats).map(([platform, stats]) => (
          <Col span={8} key={platform}>
            <Card
              title={
                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                  <div
                    style={{
                      width: 12,
                      height: 12,
                      borderRadius: '50%',
                      backgroundColor: getPlatformColor(platform)
                    }}
                  />
                  {getPlatformName(platform)}
                </div>
              }
              loading={loading}
            >
              {/* 成功率进度条 */}
              <div style={{ marginBottom: 16 }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>
                  <span>成功率</span>
                  <span style={{ color: getSuccessRateColor(stats.success_rate) }}>
                    {stats.success_rate.toFixed(1)}%
                  </span>
                </div>
                <Progress
                  percent={stats.success_rate}
                  strokeColor={getSuccessRateColor(stats.success_rate)}
                  showInfo={false}
                  size="small"
                />
              </div>

              {/* 详细统计 */}
              <Row gutter={8} style={{ marginBottom: 12 }}>
                <Col span={12}>
                  <Statistic
                    title="总尝试"
                    value={stats.total_attempts}
                    valueStyle={{ fontSize: 16 }}
                  />
                </Col>
                <Col span={12}>
                  <Statistic
                    title="平均响应"
                    value={stats.avg_response_time.toFixed(2)}
                    suffix="s"
                    valueStyle={{ fontSize: 16 }}
                  />
                </Col>
              </Row>

              {/* 状态标签 */}
              <div style={{ display: 'flex', gap: 4, flexWrap: 'wrap' }}>
                <Tag color="success" icon={<CheckCircleOutlined />}>
                  成功 {stats.successful}
                </Tag>
                <Tag color="error" icon={<CloseCircleOutlined />}>
                  失败 {stats.failed}
                </Tag>
                <Tag color="warning" icon={<ExclamationCircleOutlined />}>
                  过期 {stats.expired}
                </Tag>
              </div>

              {/* 健康度指示器 */}
              <div style={{ marginTop: 12, padding: 8, backgroundColor: '#f5f5f5', borderRadius: 4 }}>
                <div style={{ fontSize: 12, color: '#666', marginBottom: 4 }}>平台健康度</div>
                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                  <div
                    style={{
                      width: 8,
                      height: 8,
                      borderRadius: '50%',
                      backgroundColor: stats.success_rate >= 90 ? '#52c41a' : 
                                     stats.success_rate >= 70 ? '#faad14' : '#ff4d4f'
                    }}
                  />
                  <span style={{ fontSize: 12 }}>
                    {stats.success_rate >= 90 ? '优秀' : 
                     stats.success_rate >= 70 ? '良好' : '需要关注'}
                  </span>
                </div>
              </div>
            </Card>
          </Col>
        ))}
      </Row>
    </div>
  );
};

export default LoginKeeperCharts;
