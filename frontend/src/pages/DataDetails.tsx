import React, { useState, useEffect } from 'react';
import { Layout, Menu, Table, Card, Select, Input, Button, message, Spin, Tag, Space, Row, Col } from 'antd';
import { SearchOutlined, ReloadOutlined, DownloadOutlined } from '@ant-design/icons';
import { useSearchParams } from 'react-router-dom';
import { Pie, Line } from '@ant-design/plots';
import api from '../services/api';
import { overviewService } from '../services/overviewService';

const { Sider, Content } = Layout;
const { Search } = Input;
const { Option } = Select;

interface Account {
  id: number;
  name: string;
  platform: string;
  login_status: boolean;
  last_login_time: string | null;
  created_at: string;
}

interface DataItem {
  id: number;
  account_id: number;
  [key: string]: any;
}

interface DataConfig {
  name: string;
  description: string;
  columns: Array<{
    key: string;
    title: string;
    type: string;
  }>;
}
interface DataTypeConfig {
  [key: string]: DataConfig;
}

const DataDetails: React.FC = () => {
  const [searchParams] = useSearchParams();

  // 从URL参数获取初始值
  const initialPlatform = searchParams.get('platform') || 'overview';
  const initialDataType = searchParams.get('type') || 'overview';

  const [selectedPlatform, setSelectedPlatform] = useState<string>(initialPlatform);
  const [selectedDataType, setSelectedDataType] = useState<string>(initialDataType);
  const [selectedAccount, setSelectedAccount] = useState<number | string | null>('all');
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [dataList, setDataList] = useState<DataItem[]>([]);
  const [dataConfig, setDataConfig] = useState<DataTypeConfig>({});
  const [searchText, setSearchText] = useState<string>('');
  const [platformFilter, setPlatformFilter] = useState<string>('all');
  const [loading, setLoading] = useState(false);
  const [accountSummary, setAccountSummary] = useState<any>(null);
  const [growthSummary, setGrowthSummary] = useState<any>(null);
  const [overviewLoading, setOverviewLoading] = useState(false);
  const [currentDataRange, setCurrentDataRange] = useState<any>(null);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });

  const [sorterState, setSorterState] = useState<{ field?: string; order?: 'ascend' | 'descend' | null }>({
    field: 'created_at',
    order: 'descend'
  });


  // 数据过滤函数
  const filterDataByPlatform = (data: any[]) => {
    if (platformFilter === 'all') {
      return data;
    }

    return data.filter(item => {
      if (platformFilter === 'wechat_mp') {
        return item.platform === 'wechat_mp' || item.platform === 'wechat_service';
      }
      return item.platform === platformFilter;
    });
  };

  // 准备饼图数据
  const preparePieData = () => {
    if (!accountSummary?.data || !accountSummary?.date_columns) return [];

    const filteredData = filterDataByPlatform(accountSummary.data);

    // 过滤掉当天的数据
    let today = new Date().toISOString().split('T')[0]; // 获取今天的日期 YYYY-MM-DD
    // today = '2025-08-18'
    const availableDates = accountSummary.date_columns.filter((dateCol: string) => dateCol !== today);

    if (availableDates.length === 0) return [];

    const latestDateCol = availableDates[0]; // 最新的可用日期

    const pieData = filteredData.map(item => {
      const rawValue = item[latestDateCol];
      let value = 0;

      // 处理各种形式的空值：null, undefined, '', 'null', 'NULL'
      if (rawValue !== null && rawValue !== undefined && rawValue !== '' &&
        rawValue !== 'null' && rawValue !== 'NULL') {
        const numValue = Number(rawValue);
        if (!isNaN(numValue)) {
          value = numValue;
        }
      }

      return {
        type: item.account_name || '未知账号',
        value: value,
        platform: item.platform
      };
    }).filter(item =>
      item.value > 0 &&
      item.type !== '未知账号' &&
      item.type !== 'null' &&
      item.type !== 'NULL' &&
      item.type !== ''
    );

    console.log('饼图数据:', pieData);
    return pieData;
  };

  // 准备折线图数据
  const prepareLineData = () => {
    if (!accountSummary?.data || !accountSummary?.date_columns) return [];

    const filteredData = filterDataByPlatform(accountSummary.data);
    const lineData: any[] = [];

    // 过滤掉当天的数据
    let today = new Date().toISOString().split('T')[0]; // 获取今天的日期 YYYY-MM-DD
    // today = '2025-08-23'
    const availableDates = accountSummary.date_columns.filter((dateCol: string) => dateCol !== today);

    filteredData.forEach(account => {
      // 过滤掉账号名为空或null的数据
      if (!account.account_name ||
        account.account_name === 'null' ||
        account.account_name === 'NULL' ||
        account.account_name === '') {
        return;
      }

      availableDates.forEach((dateCol: string) => {
        const rawValue = account[dateCol];
        let value = 0;

        // 处理各种形式的空值：null, undefined, '', 'null', 'NULL'
        if (rawValue !== null && rawValue !== undefined && rawValue !== '' &&
          rawValue !== 'null' && rawValue !== 'NULL') {
          const numValue = Number(rawValue);
          if (!isNaN(numValue)) {
            value = numValue;
          }
        }

        lineData.push({
          date: dateCol,
          value: value,
          category: account.account_name,
          platform: account.platform
        });
      });
    });

    // 按日期正序排序
    lineData.sort((a, b) => {
      // 将日期字符串转换为Date对象进行比较
      const dateA = new Date(a.date);
      const dateB = new Date(b.date);
      return dateA.getTime() - dateB.getTime();
    });

    console.log('折线图数据:', lineData);
    return lineData;
  };

  // 平台配置
  const platformConfig = {
    wechat_mp: {
      name: '微信公众号',
      dataTypes: {
        content_trend: '内容数据趋势明细',
        content_source: '内容流量来源明细',
        content_detail: '内容已通知内容明细',
        user_channel: '用户增长明细',
        user_source: '用户来源明细'
      }
    },
    wechat_channels: {
      name: '视频号',
      dataTypes: {}
    },
    xiaohongshu: {
      name: '小红书',
      dataTypes: {}
    }
  };

  // 菜单项
  const menuItems = [
    {
      key: 'overview',
      label: '总览'
    },
    {
      key: 'wechat_mp',
      label: '微信公众号',
      children: [
        { key: 'wechat_mp_content_trend', label: '内容数据趋势明细' },
        { key: 'wechat_mp_content_source', label: '内容流量来源明细' },
        { key: 'wechat_mp_content_detail', label: '内容已通知内容明细' },
        { key: 'wechat_mp_user_channel', label: '用户增长明细' },
        { key: 'wechat_mp_user_source', label: '用户来源明细' }
      ]
    },
    {
      key: 'wechat_channels',
      label: '视频号',
      children: [
        { key: 'wechat_channels_follower_data', label: '关注者数据' },
        { key: 'wechat_channels_single_video', label: '单篇视频数据' }
      ]
    },
    {
      key: 'xiaohongshu',
      label: '小红书',
      children: [
        { key: 'xiaohongshu_account_overview', label: '账号概览' },
        { key: 'xiaohongshu_fans_data', label: '粉丝数据' },
        { key: 'xiaohongshu_note_data', label: '笔记数据' }
      ]
    }
  ];

  useEffect(() => {
    fetchDataConfig();
    fetchAccounts();
  }, []);

  // 处理URL参数变化
  useEffect(() => {
    const platform = searchParams.get('platform');
    const type = searchParams.get('type');

    if (platform) {
      setSelectedPlatform(platform);
    }
    if (type) {
      setSelectedDataType(type);
    }
  }, [searchParams]);

  useEffect(() => {
    if (selectedAccount && selectedDataType) {
      if (selectedDataType === 'overview') {
        fetchOverviewData();
      } else {
        fetchDataList();
      }
    }
  }, [selectedAccount, selectedDataType, pagination.current, pagination.pageSize, searchText, sorterState.field, sorterState.order]);

  const fetchDataConfig = async () => {
    try {
      // 获取微信公众号配置
      const wechatMpResponse = await api.get('/data-details/wechat-mp/config');
      // 获取视频号配置
      const wechatChannelsResponse = await api.get('/data-details/wechat-channels/config');
      // 获取小红书配置
      const xiaohongshuResponse = await api.get('/data-details/xiaohongshu/config');

      const allConfig = {};
      if (wechatMpResponse.data.success) {
        Object.assign(allConfig, wechatMpResponse.data.data_types);
      }
      if (wechatChannelsResponse.data.success) {
        Object.assign(allConfig, wechatChannelsResponse.data.data_types);
      }
      if (xiaohongshuResponse.data.success) {
        Object.assign(allConfig, xiaohongshuResponse.data.data_types);
      }

      setDataConfig(allConfig);
    } catch (error: any) {
      console.error('获取数据配置失败:', error);
      if (error.response) {
        console.error('错误响应:', error.response.data);
        message.error(`获取数据配置失败: ${error.response.data.detail || error.message}`);
      } else {
        message.error('获取数据配置失败');
      }
    }
  };

  const fetchAccounts = async () => {
    try {
      // 获取微信公众号账号
      const wechatMpResponse = await api.get('/data-details/wechat-mp/accounts');
      // 获取视频号账号
      const wechatChannelsResponse = await api.get('/data-details/wechat-channels/accounts');
      // 获取小红书账号
      const xiaohongshuResponse = await api.get('/data-details/xiaohongshu/accounts');

      const allAccounts = [];
      if (wechatMpResponse.data.success) {
        // 为微信公众号账号添加平台标识
        const mpAccounts = wechatMpResponse.data.accounts.map((account: any) => ({
          ...account,
          platform: 'wechat_mp'
        }));
        allAccounts.push(...mpAccounts);
      }
      if (wechatChannelsResponse.data.success) {
        // 为视频号账号添加平台标识
        const channelsAccounts = wechatChannelsResponse.data.accounts.map((account: any) => ({
          ...account,
          platform: 'wechat_channels'
        }));
        allAccounts.push(...channelsAccounts);
      }
      if (xiaohongshuResponse && xiaohongshuResponse.data.success) {
        // 为小红书账号添加平台标识
        const xiaohongshuAccounts = xiaohongshuResponse.data.accounts.map((account: any) => ({
          ...account,
          platform: 'xiaohongshu'
        }));
        allAccounts.push(...xiaohongshuAccounts);
      }

      setAccounts(allAccounts);
      // 默认选择"全部"
      setSelectedAccount('all');
    } catch (error: any) {
      console.error('获取账号列表失败:', error);
      if (error.response) {
        console.error('错误响应:', error.response.data);
        message.error(`获取账号列表失败: ${error.response.data.detail || error.message}`);
      } else {
        message.error('获取账号列表失败');
      }
    }
  };

  const fetchDataList = async (override?: { page?: number; pageSize?: number; sortField?: string; sortOrder?: 'ascend' | 'descend' | null }) => {
    if (!selectedAccount || !selectedDataType) return;

    const nextPage = override?.page ?? pagination.current;
    const nextPageSize = override?.pageSize ?? pagination.pageSize;
    const nextSortField = override?.sortField ?? sorterState.field ?? 'created_at';
    const nextSortOrder = override?.sortOrder ?? sorterState.order ?? 'descend';

    setLoading(true);
    try {
      const params: any = {
        page: nextPage,
        page_size: nextPageSize,
        search: searchText || undefined,
        sort_field: nextSortField || 'created_at',
        sort_order: nextSortOrder === 'ascend' ? 'asc' : 'desc'
      };

      // 只有当选择了具体账号时才传递 account_id 参数
      if (selectedAccount !== 'all') {
        params.account_id = selectedAccount;
      }

      // 根据数据类型确定API路径
      let apiPath = '';
      if (selectedDataType === 'single_video' || selectedDataType === 'follower_data') {
        apiPath = `/data-details/wechat-channels/${selectedDataType}`;
      } else if (selectedDataType === 'note_data' || selectedDataType === 'account_overview' || selectedDataType === 'fans_data') {
        apiPath = `/data-details/xiaohongshu/${selectedDataType}`;
      } else {
        apiPath = `/data-details/wechat-mp/${selectedDataType}`;
      }

      const response = await api.get(apiPath, { params });

      if (response.data.success) {
        setDataList(response.data.data);
        setPagination(prev => ({
          ...prev,
          total: response.data.total,
          current: response.data.page
        }));
      } else {
        message.error(response.data.error || '获取数据失败');
      }
    } catch (error) {
      message.error('获取数据列表失败');
    } finally {
      setLoading(false);
    }
  };

  const fetchOverviewData = async () => {
    setOverviewLoading(true);
    try {
      // 并行获取多平台数据：账号汇总、增长汇总
      const [accountSummaryResult, growthSummaryResult] = await Promise.all([
        overviewService.getMultiPlatformAccountSummary(),
        overviewService.getMultiPlatformGrowthSummary()
      ]);

      console.log('accountSummaryResult', accountSummaryResult)
      if (accountSummaryResult.success) {
        setAccountSummary(accountSummaryResult);
      } else {
        message.error(`获取多平台账号汇总失败: ${accountSummaryResult.error}`);
      }

      if (growthSummaryResult.success) {
        setGrowthSummary(growthSummaryResult);
      } else {
        message.error(`获取多平台增长汇总失败: ${growthSummaryResult.error}`);
      }
    } catch (error: any) {
      console.error('获取总览数据失败:', error);
      message.error('获取总览数据失败');
    } finally {
      setOverviewLoading(false);
    }
  };

  const handleMenuClick = ({ key }: { key: string }) => {
    // 处理总览菜单
    if (key === 'overview') {
      setSelectedPlatform('overview');
      setSelectedDataType('overview');
      return;
    }

    // 正确解析平台和数据类型
    let platform = '';
    let dataType = '';

    if (key.startsWith('wechat_mp_')) {
      platform = 'wechat_mp';
      dataType = key.substring('wechat_mp_'.length);
    } else if (key.startsWith('wechat_channels_')) {
      platform = 'wechat_channels';
      dataType = key.substring('wechat_channels_'.length);
    } else if (key.startsWith('xiaohongshu_')) {
      platform = 'xiaohongshu';
      dataType = key.substring('xiaohongshu_'.length);
    }

    if (platform && dataType) {
      setSelectedPlatform(platform);
      setSelectedDataType(dataType);
      setPagination(prev => ({ ...prev, current: 1 }));

      // 切换平台时重置账号选择为"全部"
      setSelectedAccount('all');
    }
  };

  // 根据当前平台过滤账号
  const getFilteredAccounts = () => {
    return accounts.filter(account => {
      // 根据当前选择的平台过滤账号
      if (selectedPlatform === 'wechat_mp') {
        return account.platform === 'wechat_mp';
      } else if (selectedPlatform === 'wechat_channels') {
        return account.platform === 'wechat_channels';
      } else if (selectedPlatform === 'xiaohongshu') {
        return account.platform === 'xiaohongshu';
      }
      return true; // 默认显示所有账号
    });
  };

  const handleTableChange = (paginationInfo: any, _filters: any, sorter: any) => {
    const order = sorter && sorter.order ? sorter.order : null;
    const field = sorter && sorter.field ? sorter.field : undefined;

    // 更新分页与排序状态
    setPagination(prev => ({
      ...prev,
      current: order ? 1 : paginationInfo.current, // 排序变化时回到第1页
      pageSize: paginationInfo.pageSize
    }));
    setSorterState({ field, order });

    // 立即按新参数触发请求
    fetchDataList({
      page: order ? 1 : paginationInfo.current,
      pageSize: paginationInfo.pageSize,
      sortField: field,
      sortOrder: order
    });
  };

  const handleSearch = (value: string) => {
    setSearchText(value);
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  const handleRefresh = () => {
    fetchDataList();
  };

  // 用户来源映射
  const userSourceMapping: { [key: string]: string } = {
    "0": "其他合计",
    "1": "公众号搜索",
    "17": "名片分享",
    "30": "扫描二维码",
    "57": "文章内账号名称",
    "100": "微信广告",
    "161": "他人转载",
    "149": "小程序关注",
    "200": "视频号",
    "201": "直播"
  };

  // 生成表格列配置
  const generateColumns = () => {
    const config = dataConfig[selectedDataType];
    if (!config) return [];

    return config.columns.map(col => ({
      title: col.title,
      dataIndex: col.key,
      key: col.key,
      render: (value: any) => {
        if (col.type === 'date') {
          return value ? new Date(value).toLocaleDateString() : '-';
        }
        if (col.type === 'datetime') {
          return value ? new Date(value).toLocaleString() : '-';
        }
        if (col.type === 'number') {
          return typeof value === 'number' ? value.toLocaleString() : value || 0;
        }
        if (col.type === 'url') {
          return value ? (
            <a href={value} target="_blank" rel="noopener noreferrer">
              查看链接
            </a>
          ) : '-';
        }
        if (col.type === 'user_source') {
          return userSourceMapping[String(value)] || `未知来源(${value})`;
        }
        return value || '-';
      },
      sorter: col.type === 'number' || col.type === 'date' || col.type === 'datetime' || col.key === 'account_name',
      sortOrder: sorterState.field === col.key ? sorterState.order : null,
      width: col.key === 'account_name' ? 150 :
        col.key === 'updated_at' ? 180 :
          col.type === 'text' ? 200 : 120,
      fixed: col.key === 'account_name' ? 'left' as const :
        col.key === 'updated_at' ? 'right' as const : undefined
    }));
  };

  const currentConfig = dataConfig[selectedDataType];

  return (
    <div>
      <Layout style={{ height: '100%' }}>
        <Sider width={250} theme="light" style={{ borderRight: '1px solid #f0f0f0' }}>
          <div style={{ padding: '16px', borderBottom: '1px solid #f0f0f0' }}>
            <h3>数据明细</h3>
          </div>
          <Menu
            mode="inline"
            selectedKeys={selectedPlatform === 'overview' ? ['overview'] : [`${selectedPlatform}_${selectedDataType}`]}
            items={menuItems}
            onClick={handleMenuClick}
            style={{ height: 'calc(100% - 64px)', borderRight: 0 }}
          />
        </Sider>

        <Layout>
          <Content style={{ padding: '24px', background: '#fff' }}>
            {selectedDataType === 'overview' ? (
              // 总览视图
              <div>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
                  <h2 style={{ margin: 0 }}>数据总览</h2>
                  <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                    <span>账号类型：</span>
                    <Select
                      value={platformFilter}
                      onChange={setPlatformFilter}
                      style={{ width: 120 }}
                      options={[
                        { value: 'all', label: '全部' },
                        { value: 'wechat_mp', label: '公众号' },
                        { value: 'wechat_channels', label: '视频号' },
                        { value: 'xiaohongshu', label: '小红书' }
                      ]}
                    />
                  </div>
                </div>

                {/* 数据表格 */}
                <Card
                  title={`时间节点数据汇总`}
                  style={{ marginBottom: 24 }}
                  loading={overviewLoading}
                >
                  {accountSummary && (
                    <Table
                      dataSource={filterDataByPlatform(accountSummary.data)}
                      rowKey="account_name"
                      pagination={false}
                      scroll={{ x: 'max-content' }}
                      size="small"
                      columns={[
                        {
                          title: '账号名称',
                          dataIndex: 'account_name',
                          key: 'account_name',
                          fixed: 'left',
                          width: 150
                        },
                        {
                          title: '数据日期',
                          dataIndex: 'latest_update_date',
                          key: 'latest_update_date',
                          width: 140,
                          render: (date: string) => {
                            if (!date) return '-';
                            if (date === '未更新') {
                              return <span style={{ color: '#ff4d4f' }}>未更新</span>;
                            }
                            return date;
                          }
                        },
                        {
                          title: '平台',
                          dataIndex: 'platform',
                          key: 'platform',
                          width: 100,
                          render: (platform: string) => {
                            const platformNames: { [key: string]: string } = {
                              'wechat_mp': '公众号',
                              'wechat_service': '公众号',
                              'wechat_channels': '视频号',
                              'xiaohongshu': '小红书'
                            };
                            return platformNames[platform] || platform;
                          }
                        },
                        {
                          title: '关注数',
                          children: accountSummary.date_columns?.map((dateCol: string) => ({
                            title: dateCol,
                            dataIndex: dateCol,
                            key: dateCol,
                            width: 120,
                            render: (value: number) => value?.toLocaleString() || '-'
                          })) || []
                        }
                      ]}
                      summary={(pageData) => {
                        const filteredData = filterDataByPlatform(accountSummary.data);
                        if (filteredData.length === 0) return null;

                        return (
                          <Table.Summary.Row>
                            <Table.Summary.Cell index={0}>
                              <strong>共 {filteredData.length} 个账号</strong>
                            </Table.Summary.Cell>
                            <Table.Summary.Cell index={1}>

                            </Table.Summary.Cell>
                            <Table.Summary.Cell index={2}>
                              <strong>合计</strong>
                            </Table.Summary.Cell>
                            {accountSummary.date_columns?.map((dateCol: string, index: number) => (
                              <Table.Summary.Cell key={dateCol} index={index + 3}>
                                <strong>
                                  {filteredData.reduce((sum, item) => sum + (item[dateCol] || 0), 0).toLocaleString()}
                                </strong>
                              </Table.Summary.Cell>
                            ))}
                          </Table.Summary.Row>
                        );
                      }}
                    />
                  )}
                </Card>

                {/* 社媒账号数据汇总表 */}
                <Card
                  title={`30天数据汇总${accountSummary?.data_date_range ? `（${accountSummary.data_date_range}）` : '（数据日期跨度）'}`}
                  style={{ marginBottom: 24 }}
                  loading={overviewLoading}
                >
                  {accountSummary && (
                    <>
                      {/* 图表区域 */}
                      <Row gutter={16} style={{ marginBottom: 24 }}>
                        <Col span={12}>
                          <Card title="账号关注数占比" size="small">
                            {(() => {
                              const pieData = preparePieData();
                              console.log('pieData', pieData)
                              const total = pieData.reduce((sum, item) => sum + item.value, 0);
                              console.log('total', total)
                              return (
                                <Pie
                                  data={pieData}
                                  angleField="value"
                                  colorField="type"
                                  radius={0.8}
                                  label={{
                                    text: (datum: any) => `${datum.type} ${((Number(datum.value) || 0) / (total || 1) * 100).toFixed(1)}%`,
                                    position: 'outside'
                                  }}
                                  interactions={[{ type: 'element-active' }]}
                                  height={300}
                                  legend={{
                                    color: {
                                      title: false,
                                      position: 'right',
                                      rowPadding: 5,
                                    },
                                  }}
                                  tooltip={({ type, value }) => {
                                    const accountName = type || '未知账号';
                                    const percent = total > 0 ? ((Number(value) || 0) / total * 100).toFixed(1) : '0.0';
                                    return { type: accountName, value: `${percent}%` };
                                  }}
                                  interaction={{
                                    tooltip: {
                                      render: (e: any, { items }: { items: any[] }) => {
                                        return (
                                          <React.Fragment>
                                            {items.map((item: any) => {
                                              const { type, value, color } = item;
                                              return (
                                                <div key={type} style={{ margin: 0, display: 'flex', justifyContent: 'space-between', padding: '4px 8px' }}>
                                                  <div>
                                                    <span
                                                      style={{
                                                        display: 'inline-block',
                                                        width: 6,
                                                        height: 6,
                                                        borderRadius: '50%',
                                                        backgroundColor: color,
                                                        marginRight: 6,
                                                      }}
                                                    ></span>
                                                    <span>{type}</span>
                                                  </div>
                                                  <b>{value}</b>
                                                </div>
                                              );
                                            })}
                                          </React.Fragment>
                                        );
                                      },
                                    },
                                  }}
                                  statistic={{
                                    title: false,
                                    content: false
                                  }}
                                />
                              );
                            })()}
                          </Card>
                        </Col>
                        <Col span={12}>
                          <Card title="关注数趋势" size="small">
                            <Line
                              data={prepareLineData()}
                              xField="date"
                              yField="value"
                              seriesField="category"
                              colorField="category"
                              height={300}
                              point={{
                                size: 5,
                                shape: 'diamond'
                              }}
                              color={['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1', '#eb2f96', '#13c2c2', '#fa8c16']}
                              legend={{
                                color: {
                                  title: false,
                                  position: 'right',
                                  rowPadding: 5,
                                },
                              }}
                              smooth={true}
                            />
                          </Card>
                        </Col>
                      </Row>


                    </>
                  )}
                </Card>

                {/* 关注数合计净增长表 */}
                <Card
                  title={`关注数合计净增长`}
                  loading={overviewLoading}
                >
                  {growthSummary && (
                    <Table
                      dataSource={filterDataByPlatform(growthSummary.data)}
                      rowKey="account_name"
                      pagination={false}
                      scroll={{ x: 'max-content' }}
                      size="small"
                      columns={[
                        {
                          title: '账号名称',
                          dataIndex: 'account_name',
                          key: 'account_name',
                          fixed: 'left',
                          width: 150
                        },
                        {
                          title: '数据日期',
                          dataIndex: 'latest_update_date',
                          key: 'latest_update_date',
                          width: 140,
                          render: (date: string) => {
                            if (!date) return '-';
                            if (date === '未更新') {
                              return <span style={{ color: '#ff4d4f' }}>未更新</span>;
                            }
                            return date;
                          }
                        },
                        {
                          title: '平台',
                          dataIndex: 'platform',
                          key: 'platform',
                          width: 100,
                          render: (platform: string) => {
                            const platformNames: { [key: string]: string } = {
                              'wechat_mp': '公众号',
                              'wechat_service': '公众号',
                              'wechat_channels': '视频号',
                              'xiaohongshu': '小红书'
                            };
                            return platformNames[platform] || platform;
                          }
                        },
                        {
                          title: '新增用户',
                          dataIndex: 'new_user',
                          key: 'new_user',
                          width: 100,
                          render: (value: number) => value?.toLocaleString() || 0
                        },
                        {
                          title: '取消关注',
                          dataIndex: 'cancel_user',
                          key: 'cancel_user',
                          width: 100,
                          render: (value: number) => value?.toLocaleString() || 0
                        },
                        {
                          title: '累积用户',
                          dataIndex: 'cumulate_user',
                          key: 'cumulate_user',
                          width: 120,
                          render: (value: number) => value?.toLocaleString() || 0
                        },
                        ...(growthSummary.user_sources?.map((source: number) => ({
                          title: userSourceMapping[String(source)] || `全部来源`,
                          dataIndex: `source_${source}`,
                          key: `source_${source}`,
                          width: 100,
                          render: (value: number) => value?.toLocaleString() || 0
                        })) || [])
                      ]}
                      summary={() => {
                        const filteredData = filterDataByPlatform(growthSummary.data);
                        if (filteredData.length === 0) return null;

                        const totalNew = filteredData.reduce((sum, item) => sum + (item.new_user || 0), 0);
                        const totalCancel = filteredData.reduce((sum, item) => sum + (item.cancel_user || 0), 0);
                        const totalCumulate = filteredData.reduce((sum, item) => sum + (item.cumulate_user || 0), 0);

                        return (
                          <Table.Summary.Row>
                            <Table.Summary.Cell index={0}>
                              <strong>共 {filteredData.length} 个账号</strong>
                            </Table.Summary.Cell>
                            <Table.Summary.Cell index={1}>
                              
                            </Table.Summary.Cell>
                            <Table.Summary.Cell index={2}>
                              <strong>合计</strong>
                            </Table.Summary.Cell>
                            <Table.Summary.Cell index={2}>
                              <strong>{totalNew.toLocaleString()}</strong>
                            </Table.Summary.Cell>
                            <Table.Summary.Cell index={3}>
                              <strong>{totalCancel.toLocaleString()}</strong>
                            </Table.Summary.Cell>
                            <Table.Summary.Cell index={4}>
                              <strong>{totalCumulate.toLocaleString()}</strong>
                            </Table.Summary.Cell>
                            {growthSummary.user_sources?.map((source: number, index: number) => (
                              <Table.Summary.Cell key={`source_${source}`} index={index + 5}>
                                <strong>
                                  {filteredData.reduce((sum, item) => sum + (item[`source_${source}`] || 0), 0).toLocaleString()}
                                </strong>
                              </Table.Summary.Cell>
                            ))}
                          </Table.Summary.Row>
                        );
                      }}
                    />
                  )}
                </Card>
              </div>
            ) : currentConfig ? (
              // 原有的数据明细视图
              <>
                <Card
                  title={currentConfig.name}
                  extra={
                    <Space>
                      <Select
                        value={selectedAccount}
                        onChange={setSelectedAccount}
                        style={{ width: 200 }}
                        placeholder="选择账号"
                      >
                        <Option key="all" value="all">
                          <Space>
                            全部账号
                            <Tag color="blue">
                              {getFilteredAccounts().length} 个账号
                            </Tag>
                          </Space>
                        </Option>
                        {getFilteredAccounts().map(account => (
                          <Option key={account.id} value={account.id}>
                            <Space>
                              {account.name}
                              <Tag color={account.platform === 'wechat_mp' ? 'green' : account.platform === 'wechat_channels' ? 'blue' : 'orange'}>
                                {account.platform === 'wechat_mp' ? '公众号' : account.platform === 'wechat_channels' ? '视频号' : '小红书'}
                              </Tag>
                            </Space>
                          </Option>
                        ))}
                      </Select>
                      <Search
                        placeholder="搜索..."
                        allowClear
                        onSearch={handleSearch}
                        style={{ width: 200 }}
                      />
                      <Button icon={<ReloadOutlined />} onClick={handleRefresh}>
                        刷新
                      </Button>
                    </Space>
                  }
                  style={{ marginBottom: 16 }}
                >
                  <p style={{ margin: 0, color: '#666' }}>{currentConfig.description}</p>
                </Card>

                <Table
                  columns={generateColumns()}
                  dataSource={dataList}
                  rowKey="id"
                  loading={loading}
                  pagination={{
                    ...pagination,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total, range) =>
                      `第 ${range[0]}-${range[1]} 条，共 ${total} 条数据`
                  }}
                  onChange={handleTableChange}
                  scroll={{ x: 'max-content' }}
                  size="small"
                />
              </>
            ) : (
              <div style={{ textAlign: 'center', padding: '50px' }}>
                <Spin size="large" />
                <p style={{ marginTop: 16 }}>加载中...</p>
              </div>
            )}
          </Content>
        </Layout>
      </Layout>
    </div>
  );
};

export default DataDetails;
