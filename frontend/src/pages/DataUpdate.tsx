import React, { useState, useEffect } from 'react';
import {
  Card, Button, Progress, Alert, message,
  Descriptions, Table, Space, Typography, Divider, Switch,
  TimePicker, InputNumber, Row, Col, Form, Tooltip, Tag, Modal, Popconfirm
} from 'antd';
import { PlayCircleOutlined, ReloadOutlined, HistoryOutlined, SettingOutlined, ClockCircleOutlined, RedoOutlined, InfoCircleOutlined, StopOutlined, DeleteOutlined } from '@ant-design/icons';
import dayjs, { Dayjs } from 'dayjs';
import api from '../services/api';
import autoUpdateService, { AutoUpdateConfig, AutoUpdateStatus } from '../services/autoUpdateService';
import dataUpdateService from '../services/dataUpdateService';


const { Title, Text } = Typography;

interface UpdateTask {
  task_id: number;
  start_date: string;
  end_date: string;
  status: string;
  total_accounts: number;
  completed_accounts: number;
  current_account_name?: string;
  current_step?: string;
  error_message?: string;
  created_at: string;
  updated_at: string;
  completed_at?: string;
  progress_percent: number;
}

interface DataRange {
  min_date?: string;
  max_date?: string;
  total_records: number;
  last_update_time?: string;
  last_update_status?: string;
}

interface TaskItem {
  id: number;
  account_name: string;
  platform: string;
  data_type: string;
  data_type_display: string;
  status: string;
  error_message?: string;
  started_at?: string;
  completed_at?: string;
  can_retry: boolean;
}

interface TaskItemsResponse {
  success: boolean;
  items: TaskItem[];
  total: number;
  page: number;
  page_size: number;
  stats: Record<string, number>;
  error?: string;
}

const DataUpdate: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [currentTask, setCurrentTask] = useState<UpdateTask | null>(null);
  const [dataRange, setDataRange] = useState<DataRange | null>(null);
  const [history, setHistory] = useState<any[]>([]);
  const [pollingInterval, setPollingInterval] = useState<NodeJS.Timeout | null>(null);

  // 任务明细相关状态
  const [taskItems, setTaskItems] = useState<TaskItem[]>([]);
  const [taskItemsLoading, setTaskItemsLoading] = useState(false);
  const [taskItemsTotal, setTaskItemsTotal] = useState(0);
  const [taskItemsPage, setTaskItemsPage] = useState(1);
  const [taskItemsStats, setTaskItemsStats] = useState<Record<string, number>>({});
  const [retryingItems, setRetryingItems] = useState<Set<number>>(new Set());

  // 自动更新相关状态
  const [autoUpdateConfig, setAutoUpdateConfig] = useState<AutoUpdateConfig>({
    enabled: false,
    update_time: '02:00',
    update_days: 30
  });
  const [autoUpdateStatus, setAutoUpdateStatus] = useState<AutoUpdateStatus | null>(null);
  const [autoUpdateLoading, setAutoUpdateLoading] = useState(false);
  const [form] = Form.useForm();

  // 获取当前数据范围
  const fetchDataRange = async () => {
    try {
      const response = await api.get('/data-update/current-range');
      if (response.data.success) {
        setDataRange(response.data);
      }
    } catch (error) {
      console.error('获取数据范围失败:', error);
    }
  };

  // 获取自动更新配置
  const fetchAutoUpdateConfig = async () => {
    try {
      const result = await autoUpdateService.getConfig();
      if (result.success && result.data) {
        const config = result.data;
        setAutoUpdateConfig(config);
        form.setFieldsValue({
          enabled: config.enabled,
          update_time: dayjs(config.update_time, 'HH:mm'),
          update_days: config.update_days
        });
      }
    } catch (error) {
      console.error('获取自动更新配置失败:', error);
    }
  };

  // 获取自动更新状态
  const fetchAutoUpdateStatus = async () => {
    try {
      const result = await autoUpdateService.getStatus();
      if (result.success && result.data) {
        setAutoUpdateStatus(result.data);
      }
    } catch (error) {
      console.error('获取自动更新状态失败:', error);
    }
  };

  // 保存自动更新配置
  const saveAutoUpdateConfig = async (values: any) => {
    setAutoUpdateLoading(true);
    try {
      const config: AutoUpdateConfig = {
        enabled: values.enabled,
        update_time: values.update_time.format('HH:mm'),
        update_days: values.update_days
      };

      const result = await autoUpdateService.updateConfig(config);
      if (result.success) {
        message.success('自动更新配置保存成功');
        setAutoUpdateConfig(config);
        await fetchAutoUpdateStatus(); // 刷新状态
      } else {
        message.error(`保存配置失败: ${result.error}`);
      }
    } catch (error: any) {
      message.error(`保存配置失败: ${error.message}`);
    } finally {
      setAutoUpdateLoading(false);
    }
  };



  // 获取历史记录
  const fetchHistory = async () => {
    try {
      const response = await api.get('/data-update/history');
      if (response.data.success) {
        setHistory(response.data.data);

        // 如果没有当前任务但有历史记录，获取最新的任务明细
        if (!currentTask && response.data.data.length > 0) {
          fetchTaskItems(response.data.data[0].id, 1, false);
        }
      }
    } catch (error) {
      console.error('获取历史记录失败:', error);
    }
  };

  // 任务状态排序优先级
  const getStatusPriority = (status: string): number => {
    const priorityMap: Record<string, number> = {
      'running': 1,    // 进行中
      'retrying': 2,   // 重试中
      'pending': 3,    // 待处理
      'failed': 4,     // 失败
      'cancelled': 5,  // 已取消
      'completed': 6   // 已完成
    };
    return priorityMap[status] || 7;
  };

  // 排序任务明细
  const sortTaskItems = (items: TaskItem[]): TaskItem[] => {
    return [...items].sort((a, b) => {
      const priorityA = getStatusPriority(a.status);
      const priorityB = getStatusPriority(b.status);

      if (priorityA !== priorityB) {
        return priorityA - priorityB;
      }

      // 同状态按ID排序
      return a.id - b.id;
    });
  };

  // 获取任务明细
  const fetchTaskItems = async (recordId: number, page: number = 1, isUpdate: boolean = false) => {
    if (!recordId) return;

    if (!isUpdate) {
      setTaskItemsLoading(true);
    }

    try {
      const response = await api.get(`/data-update/tasks/${recordId}/items`, {
        params: { page, page_size: 20 }
      });

      if (response.data.success) {
        const sortedItems = sortTaskItems(response.data.items);

        if (isUpdate && taskItems.length > 0) {
          // 增量更新：只更新变化的项目
          setTaskItems(prevItems => {
            const updatedItems = [...prevItems];

            sortedItems.forEach(newItem => {
              const existingIndex = updatedItems.findIndex(item => item.id === newItem.id);
              if (existingIndex >= 0) {
                // 只更新状态相关字段
                const existingItem = updatedItems[existingIndex];
                if (existingItem.status !== newItem.status ||
                    existingItem.error_message !== newItem.error_message ||
                    existingItem.started_at !== newItem.started_at ||
                    existingItem.completed_at !== newItem.completed_at) {
                  updatedItems[existingIndex] = { ...existingItem, ...newItem };
                }
              } else {
                // 新增项目
                updatedItems.push(newItem);
              }
            });

            return sortTaskItems(updatedItems);
          });
        } else {
          // 全量更新
          setTaskItems(sortedItems);
        }

        setTaskItemsTotal(response.data.total);
        setTaskItemsPage(page);
        setTaskItemsStats(response.data.stats);
      }
    } catch (error) {
      console.error('获取任务明细失败:', error);
      if (!isUpdate) {
        message.error('获取任务明细失败');
      }
    } finally {
      if (!isUpdate) {
        setTaskItemsLoading(false);
      }
    }
  };

  // 重试任务项
  const handleRetryTaskItem = async (itemId: number) => {
    setRetryingItems(prev => new Set(prev).add(itemId));

    try {
      const response = await api.post(`/data-update/tasks/items/${itemId}/retry`);

      if (response.data.success) {
        message.success(`重试成功: ${response.data.account_name} - ${response.data.data_type_display}`);
        // 刷新任务明细
        if (currentTask) {
          fetchTaskItems(currentTask.task_id, taskItemsPage, false);
        }
      } else {
        message.error(`重试失败: ${response.data.error}`);
      }
    } catch (error) {
      console.error('重试任务项失败:', error);
      message.error('重试任务项失败');
    } finally {
      setRetryingItems(prev => {
        const newSet = new Set(prev);
        newSet.delete(itemId);
        return newSet;
      });
    }
  };

  // 检查是否有正在运行的任务
  const checkRunningTask = async () => {
    try {
      const response = await api.get('/data-update/running-task');
      if (response.data.success && response.data.has_running_task) {
        setCurrentTask(response.data.task);
        fetchTaskItems(response.data.task.task_id, 1, false);
        startPolling(response.data.task.task_id);
      }
    } catch (error) {
      console.error('检查运行任务失败:', error);
    }
  };

  // 开始轮询任务状态
  const startPolling = (taskId: number) => {
    if (pollingInterval) {
      clearInterval(pollingInterval);
    }

    const interval = setInterval(async () => {
      try {
        const response = await api.get(`/data-update/status/${taskId}`);
        if (response.data.success) {
          setCurrentTask(response.data);

          // 更新任务明细（增量更新）
          fetchTaskItems(taskId, taskItemsPage, true);

          // 如果任务完成，停止轮询
          if (response.data.status === 'completed' || response.data.status === 'failed') {
            clearInterval(interval);
            setPollingInterval(null);
            fetchHistory(); // 刷新历史记录
            fetchDataRange(); // 刷新数据范围

            if (response.data.status === 'completed') {
              message.success('数据更新完成！');
            } else {
              message.error('数据更新失败！');
            }
          }
        }
      } catch (error) {
        console.error('获取任务状态失败:', error);
        clearInterval(interval);
        setPollingInterval(null);
      }
    }, 2000);

    setPollingInterval(interval);
  };

  // 停止轮询
  const stopPolling = () => {
    if (pollingInterval) {
      clearInterval(pollingInterval);
      setPollingInterval(null);
    }
  };

  // 启动数据更新
  const handleStartUpdate = async () => {
    // 使用固定的日期范围：30天前到昨天（最近30天的数据）
    const yesterday = dayjs().subtract(1, 'day');
    const startDate = yesterday.subtract(29, 'day').format('YYYY-MM-DD'); // 30天前（包含昨天共30天）
    const endDate = yesterday.format('YYYY-MM-DD');

    setLoading(true);
    try {
      const response = await api.post('/data-update/start', {
        start_date: startDate,
        end_date: endDate
      });

      if (response.data.success) {
        message.success('数据更新任务已启动');
        setCurrentTask({
          task_id: response.data.task_id,
          start_date: startDate,
          end_date: endDate,
          status: 'running',
          total_accounts: response.data.total_accounts,
          completed_accounts: 0,
          current_step: '准备中...',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          progress_percent: 0
        });

        // 获取任务明细
        fetchTaskItems(response.data.task_id, 1, false);

        startPolling(response.data.task_id);
      }
    } catch (error: any) {
      message.error(error.response?.data?.detail || '启动更新失败');
    } finally {
      setLoading(false);
    }
  };

  // 停止任务
  const handleStopTask = async (recordId: number) => {
    try {
      const response = await dataUpdateService.stopTask(recordId);
      if (response.success) {
        message.success(response.message);
        fetchHistory();
        // 如果停止的是当前任务，清除当前任务状态
        if (currentTask && currentTask.task_id === recordId) {
          setCurrentTask(null);
          stopPolling();
        }
      } else {
        message.error(response.error || '停止任务失败');
      }
    } catch (error: any) {
      message.error(error.response?.data?.detail || '停止任务失败');
    }
  };

  // 删除任务
  const handleDeleteTask = async (recordId: number) => {
    try {
      const response = await dataUpdateService.deleteTask(recordId);
      if (response.success) {
        message.success(response.message);
        fetchHistory();
        // 如果删除的是当前任务，清除当前任务状态
        if (currentTask && currentTask.task_id === recordId) {
          setCurrentTask(null);
          stopPolling();
        }
      } else {
        message.error(response.error || '删除任务失败');
      }
    } catch (error: any) {
      message.error(error.response?.data?.detail || '删除任务失败');
    }
  };

  // 历史记录表格列
  const historyColumns = [
    {
      title: '日期范围',
      key: 'date_range',
      render: (record: any) => `${record.start_date} 至 ${record.end_date}`
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusMap = {
          'running': { text: '运行中', color: 'blue' },
          'completed': { text: '已完成', color: 'green' },
          'failed': { text: '失败', color: 'red' },
          'cancelled': { text: '已取消', color: 'orange' }
        };
        const config = statusMap[status as keyof typeof statusMap] || { text: status, color: 'default' };
        return <Text style={{ color: config.color }}>{config.text}</Text>;
      }
    },
    {
      title: '账号数',
      key: 'accounts',
      render: (record: any) => `${record.completed_accounts}/${record.total_accounts}`
    },
    {
      title: '进度',
      dataIndex: 'progress_percent',
      key: 'progress_percent',
      render: (percent: number) => `${percent}%`
    },
    {
      title: '耗时',
      dataIndex: 'duration',
      key: 'duration'
    },
    {
      title: '开始时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (time: string) => new Date(time).toLocaleString()
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      render: (record: any) => (
        <Space size="small">
          {record.status === 'running' && (
            <Popconfirm
              title="确定要停止这个任务吗？"
              description="停止后任务将无法继续执行"
              onConfirm={() => handleStopTask(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="link"
                size="small"
                icon={<StopOutlined />}
                danger
              >
                停止
              </Button>
            </Popconfirm>
          )}
          {record.status !== 'running' && (
            <Popconfirm
              title="确定要删除这个任务记录吗？"
              description="删除后将无法恢复，包括所有任务明细"
              onConfirm={() => handleDeleteTask(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="link"
                size="small"
                icon={<DeleteOutlined />}
                danger
              >
                删除
              </Button>
            </Popconfirm>
          )}
        </Space>
      )
    }
  ];

  // 任务明细表格列
  const taskItemColumns = [
    {
      title: '账号',
      dataIndex: 'account_name',
      key: 'account_name',
      width: 120
    },
    {
      title: '平台',
      dataIndex: 'platform',
      key: 'platform',
      width: 100,
      render: (platform: string) => {
        const platformMap: Record<string, { text: string; color: string }> = {
          'wechat_channels': { text: '视频号', color: 'green' },
          'xiaohongshu': { text: '小红书', color: 'red' },
          'wechat_mp': { text: '公众号', color: 'blue' },
          'wechat_service': { text: '公众号', color: 'blue' },
          'douyin': { text: '抖音', color: 'purple' }
        };
        const config = platformMap[platform] || { text: platform, color: 'default' };
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    },
    {
      title: '数据类型',
      dataIndex: 'data_type_display',
      key: 'data_type_display',
      width: 150
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => {
        const statusMap: Record<string, { text: string; color: string }> = {
          'pending': { text: '待处理', color: 'default' },
          'running': { text: '进行中', color: 'processing' },
          'completed': { text: '已完成', color: 'success' },
          'failed': { text: '失败', color: 'error' },
          'retrying': { text: '重试中', color: 'warning' },
          'cancelled': { text: '已取消', color: 'orange' }
        };
        const config = statusMap[status] || { text: status, color: 'default' };
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    },
    {
      title: '错误信息',
      dataIndex: 'error_message',
      key: 'error_message',
      width: 200,
      render: (error: string) => {
        if (!error) return '-';
        return (
          <Tooltip title={error}>
            <Text type="danger" ellipsis style={{ maxWidth: 180 }}>
              {error}
            </Text>
          </Tooltip>
        );
      }
    },
    {
      title: '开始时间',
      dataIndex: 'started_at',
      key: 'started_at',
      width: 150,
      render: (time: string) => time ? new Date(time).toLocaleString() : '-'
    },
    {
      title: '完成时间',
      dataIndex: 'completed_at',
      key: 'completed_at',
      width: 150,
      render: (time: string) => time ? new Date(time).toLocaleString() : '-'
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (record: TaskItem) => {
        if (!record.can_retry) return '-';

        return (
          <Button
            type="link"
            size="small"
            icon={<RedoOutlined />}
            loading={retryingItems.has(record.id)}
            onClick={() => handleRetryTaskItem(record.id)}
          >
            重试
          </Button>
        );
      }
    }
  ];

  useEffect(() => {
    fetchDataRange();
    fetchHistory();
    checkRunningTask();
    fetchAutoUpdateConfig();
    fetchAutoUpdateStatus();

    return () => {
      if (pollingInterval) {
        clearInterval(pollingInterval);
      }
    };
  }, []);

  const isTaskRunning = Boolean(currentTask && currentTask.status === 'running');

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>数据更新</Title>

      {/* 自动更新配置 */}
      <Card
        title={
          <Space>
            <SettingOutlined />
            自动更新配置
          </Space>
        }
        style={{ marginBottom: 24 }}
        extra={
          autoUpdateStatus && (
            <Tooltip title={autoUpdateStatus.scheduler_running ? '调度器运行中' : '调度器已停止'}>
              <div style={{
                width: 8,
                height: 8,
                borderRadius: '50%',
                backgroundColor: autoUpdateStatus.scheduler_running ? '#52c41a' : '#ff4d4f'
              }} />
            </Tooltip>
          )
        }
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={saveAutoUpdateConfig}
          initialValues={{
            enabled: autoUpdateConfig.enabled,
            update_time: dayjs(autoUpdateConfig.update_time, 'HH:mm'),
            update_days: autoUpdateConfig.update_days
          }}
        >
          <Row gutter={16}>
            <Col span={6}>
              <Form.Item
                label="启用自动更新"
                name="enabled"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                label="更新时间"
                name="update_time"
                rules={[{ required: true, message: '请选择更新时间' }]}
              >
                <TimePicker
                  format="HH:mm"
                  placeholder="选择时间"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                label="时间跨度（天）"
                name="update_days"
                rules={[
                  { required: true, message: '请输入时间跨度' },
                  { type: 'number', min: 1, max: 90, message: '时间跨度应在1-90天之间' }
                ]}
              >
                <InputNumber
                  min={1}
                  max={90}
                  style={{ width: '100%' }}
                  placeholder="天数"
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label=" " style={{ marginBottom: 0 }}>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={autoUpdateLoading}
                  style={{ width: '50%' }}
                >
                  保存配置
                </Button>
              </Form.Item>
            </Col>
          </Row>
        </Form>
        {/* 自动更新状态信息 */}
        {autoUpdateStatus && (
          <div style={{ marginTop: 16, padding: 16, backgroundColor: '#f5f5f5', borderRadius: 6 }}>
            <Row gutter={16}>
              <Col span={6}>
                <div>
                  <Text type="secondary">状态：</Text>
                  <Text strong style={{ color: autoUpdateStatus.enabled ? '#52c41a' : '#ff4d4f' }}>
                    {autoUpdateStatus.enabled ? '已启用' : '已禁用'}
                  </Text>
                </div>
              </Col>
              <Col span={6}>
                <div>
                  <Text type="secondary">更新时间：</Text>
                  <Text strong>{autoUpdateStatus.update_time}</Text>
                </div>
              </Col>
              <Col span={6}>
                <div>
                  <Text type="secondary">更新天数：</Text>
                  <Text strong>{autoUpdateStatus.update_days} 天</Text>
                </div>
              </Col>
              <Col span={6}>
                <div>
                  <Text type="secondary">上次更新：</Text>
                  <Text strong>
                    {autoUpdateStatus.last_update
                      ? new Date(autoUpdateStatus.last_update).toLocaleString()
                      : '从未更新'
                    }
                  </Text>
                </div>
              </Col>
            </Row>
            {autoUpdateStatus.next_run && (
              <div style={{ marginTop: 8 }}>
                <ClockCircleOutlined style={{ marginRight: 8, color: '#1890ff' }} />
                <Text type="secondary">下次运行时间：</Text>
                <Text strong style={{ color: '#1890ff' }}>
                  {new Date(autoUpdateStatus.next_run).toLocaleString()}
                </Text>
              </div>
            )}
          </div>
        )}
      </Card>

      {/* 手动更新 */}
      <Card
        style={{ marginBottom: 24 }}
      >
        {/* 当前数据范围 */}
        <div style={{ marginBottom: 24 }}>
          <Title level={4} style={{ marginBottom: 16 }}>上次更新</Title>
          {dataRange ? (
            <Descriptions column={3} size="small">
              <Descriptions.Item label="开始日期">
                {dataRange.min_date || '无数据'}
              </Descriptions.Item>
              <Descriptions.Item label="结束日期">
                {dataRange.max_date || '无数据'}
              </Descriptions.Item>
              <Descriptions.Item label="总记录数">
                {dataRange.total_records?.toLocaleString() || 0}
              </Descriptions.Item>
              <Descriptions.Item label="最后更新时间">
                {dataRange.last_update_time ? new Date(dataRange.last_update_time).toLocaleString() : '无'}
              </Descriptions.Item>
              <Descriptions.Item label="更新状态">
                {dataRange.last_update_status === 'completed' ? '成功' :
                  dataRange.last_update_status === 'failed' ? '失败' : '无'}
              </Descriptions.Item>
            </Descriptions>
          ) : (
            <Text>加载中...</Text>
          )}
        </div>

        {/* 手动更新按钮 */}
        <div style={{ marginTop: 24, textAlign: 'center' }}>
          <Button
            type="primary"
            icon={<PlayCircleOutlined />}
            onClick={handleStartUpdate}
            loading={loading}
            disabled={isTaskRunning}
            style={{
              height: 48,
              fontSize: 16,
              paddingLeft: 32,
              paddingRight: 32,
              minWidth: 200
            }}
          >
            立即更新
          </Button>
        </div>

        {/* 当前任务进度 */}
        {currentTask && (
          <div style={{ marginBottom: 24 }}>
            <Divider />
            <Title level={4} style={{ marginBottom: 16 }}>当前任务进度</Title>
            <Space direction="vertical" style={{ width: '100%' }}>
              <Progress
                percent={currentTask.progress_percent}
                status={currentTask.status === 'failed' ? 'exception' : 'active'}
                strokeColor={currentTask.status === 'completed' ? '#52c41a' : undefined}
              />

              <Descriptions column={2} size="small">
                <Descriptions.Item label="日期范围">
                  {currentTask.start_date} 至 {currentTask.end_date}
                </Descriptions.Item>
                <Descriptions.Item label="状态">
                  {currentTask.status === 'running' ? '运行中' :
                    currentTask.status === 'completed' ? '已完成' : '失败'}
                </Descriptions.Item>
                <Descriptions.Item label="当前账号">
                  {currentTask.current_account_name || '-'}
                </Descriptions.Item>
                <Descriptions.Item label="当前步骤">
                  {currentTask.current_step || '-'}
                </Descriptions.Item>
                <Descriptions.Item label="进度">
                  {currentTask.completed_accounts}/{currentTask.total_accounts} 个账号
                </Descriptions.Item>
                <Descriptions.Item label="开始时间">
                  {new Date(currentTask.created_at).toLocaleString()}
                </Descriptions.Item>
              </Descriptions>

              {currentTask.error_message && (
                <Alert
                  message="错误信息"
                  description={currentTask.error_message}
                  type="error"
                  showIcon
                />
              )}
            </Space>
          </div>
        )}

        {/* 任务明细 */}
        <div style={{ marginBottom: 24 }}>
          <Divider />
          <Title level={4} style={{ marginBottom: 16 }}>
            <Space>
              <InfoCircleOutlined />
              任务明细
              {taskItemsStats && Object.keys(taskItemsStats).length > 0 && (
                <Space size="small">
                  {Object.entries(taskItemsStats).map(([status, count]) => {
                    const statusMap: Record<string, { text: string; color: string }> = {
                      'pending': { text: '待处理', color: 'default' },
                      'running': { text: '进行中', color: 'processing' },
                      'completed': { text: '已完成', color: 'success' },
                      'failed': { text: '失败', color: 'error' },
                      'retrying': { text: '重试中', color: 'warning' }
                    };
                    const config = statusMap[status] || { text: status, color: 'default' };
                    return (
                      <Tag key={status} color={config.color}>
                        {config.text}: {count}
                      </Tag>
                    );
                  })}
                </Space>
              )}
            </Space>
          </Title>

          <div style={{ marginBottom: 16, textAlign: 'right' }}>
            <Button
              icon={<ReloadOutlined />}
              onClick={() => {
                if (currentTask) {
                  fetchTaskItems(currentTask.task_id, taskItemsPage, false);
                } else {
                  // 如果没有当前任务，获取最新的任务明细
                  if (history.length > 0) {
                    fetchTaskItems(history[0].id, taskItemsPage, false);
                  }
                }
              }}
              loading={taskItemsLoading}
              disabled={!currentTask && history.length === 0}
            >
              刷新明细
            </Button>
          </div>

          <Table
            columns={taskItemColumns}
            dataSource={taskItems}
            rowKey="id"
            loading={taskItemsLoading}
            pagination={{
              current: taskItemsPage,
              pageSize: 20,
              total: taskItemsTotal,
              showSizeChanger: false,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              onChange: (page) => {
                if (currentTask) {
                  fetchTaskItems(currentTask.task_id, page, false);
                } else if (history.length > 0) {
                  fetchTaskItems(history[0].id, page, false);
                }
              }
            }}
            size="small"
            scroll={{ x: 1200 }}
            locale={{
              emptyText: currentTask ? '暂无任务明细' : '请先执行数据更新任务'
            }}
          />
        </div>

        {/* 更新历史 */}
        <div>
          <Divider />
          <Title level={4} style={{ marginBottom: 16 }}>
            <Space>
              <HistoryOutlined />
              历史更新记录
            </Space>
          </Title>
          <div style={{ marginBottom: 16, textAlign: 'right' }}>
            <Button
              icon={<ReloadOutlined />}
              onClick={() => { fetchHistory(); fetchDataRange(); }}
            >
              刷新
            </Button>
          </div>
          <Table
            columns={historyColumns}
            dataSource={history}
            rowKey="id"
            pagination={{ pageSize: 10 }}
            size="small"
          />
        </div>
      </Card>
    </div>
  );
};

export default DataUpdate;
