import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Button,
  Table,
  Tag,
  Space,
  message,
  Modal,
  Tabs,

  Tooltip,
  Badge,
  Alert,
  InputNumber,
  Form,
  Descriptions,
  Typography
} from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  ReloadOutlined,
  SettingOutlined,
  EyeOutlined,
  <PERSON>boltOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import loginKeeperService, {
  LoginKeeperStatus,
  LoginKeeperRecord,
  PlatformStats,
  LoginKeeperAccount
} from '../services/loginKeeperService';
import LoginKeeperCharts from '../components/LoginKeeperCharts';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

const LoginKeeper: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [status, setStatus] = useState<LoginKeeperStatus | null>(null);
  const [records, setRecords] = useState<LoginKeeperRecord[]>([]);
  const [platformStats, setPlatformStats] = useState<PlatformStats>({});
  const [accounts, setAccounts] = useState<LoginKeeperAccount[]>([]);
  const [intervalModalVisible, setIntervalModalVisible] = useState(false);
  const [testingAccount, setTestingAccount] = useState<number | null>(null);
  const [form] = Form.useForm();

  // 加载数据
  const loadData = async () => {
    setLoading(true);
    try {
      console.log('开始加载登录保持数据...');

      const [statusRes, recordsRes, statsRes, accountsRes] = await Promise.all([
        loginKeeperService.getStatus(),
        loginKeeperService.getLogs(50),
        loginKeeperService.getPlatformStats(7),
        loginKeeperService.getAccounts()
      ]);

      console.log('状态数据:', statusRes);
      console.log('日志数据:', recordsRes);
      console.log('统计数据:', statsRes);
      console.log('账号数据:', accountsRes);

      setStatus(statusRes);
      setRecords(recordsRes.records || []);
      setPlatformStats(statsRes.stats || {});
      setAccounts(accountsRes.accounts || []);
    } catch (error) {
      message.error('加载数据失败');
      console.error('Load data error:', error);

      // 设置默认值以防止渲染错误
      setStatus({
        is_running: false,
        last_run_time: null,
        config: {
          interval_minutes: 30,
          enabled_platforms: [],
          max_retries: 3,
          concurrent_accounts: 3,
          browser_timeout: 60
        },
        stats: {
          total_runs: 0,
          successful_maintains: 0,
          failed_maintains: 0,
          accounts_processed: 0
        },
        enabled: false
      });
      setRecords([]);
      setPlatformStats({});
      setAccounts([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
    // 每30秒刷新一次状态
    const interval = setInterval(loadData, 30000);
    return () => clearInterval(interval);
  }, []);

  // 启动服务
  const handleStart = async () => {
    try {
      const result = await loginKeeperService.startService();
      if (result.success) {
        message.success(result.message);
        loadData();
      } else {
        message.error(result.message);
      }
    } catch (error) {
      message.error('启动服务失败');
    }
  };

  // 停止服务
  const handleStop = async () => {
    try {
      const result = await loginKeeperService.stopService();
      if (result.success) {
        message.success(result.message);
        loadData();
      } else {
        message.error(result.message);
      }
    } catch (error) {
      message.error('停止服务失败');
    }
  };

  // 暂停任务
  const handlePause = async () => {
    try {
      const result = await loginKeeperService.pauseJob();
      if (result.success) {
        message.success(result.message);
        loadData();
      } else {
        message.error(result.message);
      }
    } catch (error) {
      message.error('暂停任务失败');
    }
  };

  // 恢复任务
  const handleResume = async () => {
    try {
      const result = await loginKeeperService.resumeJob();
      if (result.success) {
        message.success(result.message);
        loadData();
      } else {
        message.error(result.message);
      }
    } catch (error) {
      message.error('恢复任务失败');
    }
  };

  // 手动触发
  const handleTrigger = async () => {
    try {
      message.loading('正在执行维持任务...', 0);
      const result = await loginKeeperService.triggerJob();
      message.destroy();
      
      if (result.success) {
        message.success('任务执行完成');
        loadData();
      } else {
        message.error('任务执行失败');
      }
    } catch (error) {
      message.destroy();
      message.error('触发任务失败');
    }
  };

  // 修改执行间隔
  const handleModifyInterval = async (values: { minutes: number }) => {
    try {
      const result = await loginKeeperService.modifyInterval(values.minutes);
      if (result.success) {
        message.success(result.message);
        setIntervalModalVisible(false);
        loadData();
      } else {
        message.error(result.message);
      }
    } catch (error) {
      message.error('修改间隔失败');
    }
  };

  // 测试账号
  const handleTestAccount = async (accountId: number) => {
    setTestingAccount(accountId);
    try {
      const result = await loginKeeperService.testAccount(accountId);
      if (result.success) {
        message.success('账号测试完成');
        loadData();
      } else {
        message.error('账号测试失败');
      }
    } catch (error) {
      message.error('测试账号失败');
    } finally {
      setTestingAccount(null);
    }
  };

  // 获取状态标签
  const getStatusTag = (status: string) => {
    switch (status) {
      case 'success':
        return <Tag color="success" icon={<CheckCircleOutlined />}>成功</Tag>;
      case 'failed':
        return <Tag color="error" icon={<CloseCircleOutlined />}>失败</Tag>;
      case 'login_expired':
        return <Tag color="warning" icon={<ExclamationCircleOutlined />}>登录过期</Tag>;
      default:
        return <Tag>{status}</Tag>;
    }
  };

  // 获取平台名称
  const getPlatformName = (platform: string) => {
    const platformNames: { [key: string]: string } = {
      'wechat_mp': '微信公众号',
      'xiaohongshu': '小红书',
      'wechat_channels': '微信视频号'
    };
    return platformNames[platform] || platform;
  };

  // 操作日志表格列
  const recordColumns = [
    {
      title: '时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
      render: (text: string) => new Date(text).toLocaleString()
    },
    {
      title: '账号',
      dataIndex: 'account_name',
      key: 'account_name',
      width: 120
    },
    {
      title: '平台',
      dataIndex: 'platform',
      key: 'platform',
      width: 100,
      render: (platform: string) => getPlatformName(platform)
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: string) => getStatusTag(status)
    },
    {
      title: '响应时间',
      dataIndex: 'response_time',
      key: 'response_time',
      width: 100,
      render: (time: number) => `${time.toFixed(2)}s`
    },
    {
      title: '访问页面',
      dataIndex: 'visited_page',
      key: 'visited_page',
      ellipsis: true,
      render: (url: string) => (
        <Tooltip title={url}>
          <Text ellipsis style={{ maxWidth: 200 }}>{url}</Text>
        </Tooltip>
      )
    },
    {
      title: '错误信息',
      dataIndex: 'error_message',
      key: 'error_message',
      ellipsis: true,
      render: (error: string) => error ? (
        <Tooltip title={error}>
          <Text type="danger" ellipsis style={{ maxWidth: 200 }}>{error}</Text>
        </Tooltip>
      ) : '-'
    }
  ];

  // 账号列表表格列
  const accountColumns = [
    {
      title: '账号名称',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: '平台',
      dataIndex: 'platform_name',
      key: 'platform_name'
    },
    {
      title: '登录状态',
      dataIndex: 'login_status',
      key: 'login_status',
      render: (status: boolean) => (
        <Badge 
          status={status ? 'success' : 'error'} 
          text={status ? '已登录' : '未登录'} 
        />
      )
    },
    {
      title: '最后登录时间',
      dataIndex: 'last_login_time',
      key: 'last_login_time',
      render: (time: string) => time ? new Date(time).toLocaleString() : '-'
    },
    {
      title: '支持状态',
      dataIndex: 'is_supported',
      key: 'is_supported',
      render: (supported: boolean) => (
        <Tag color={supported ? 'success' : 'default'}>
          {supported ? '支持' : '不支持'}
        </Tag>
      )
    },
    {
      title: '操作',
      key: 'action',
      render: (record: LoginKeeperAccount) => (
        <Button
          size="small"
          icon={<EyeOutlined />}
          loading={testingAccount === record.id}
          disabled={!record.is_supported || !record.login_status}
          onClick={() => handleTestAccount(record.id)}
        >
          测试
        </Button>
      )
    }
  ];

  if (!status || !status.config || !status.stats) {
    return (
      <div style={{ padding: 24, textAlign: 'center' }}>
        <div>加载中...</div>
        {loading && <div style={{ marginTop: 8, color: '#666' }}>正在获取服务状态...</div>}
      </div>
    );
  }

  return (
    <div>
      <Title level={2}>登录保持</Title>

      {/* 状态提示 */}
      {!status.enabled && (
        <Alert
          message="服务已禁用"
          description="登录状态维持服务已在配置中禁用，请检查环境变量 LOGIN_KEEPER_ENABLED 设置。"
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      {status.enabled && !status.is_running && (
        <Alert
          message="服务未运行"
          description="登录状态维持服务已启用但未运行，点击「启动服务」按钮开始自动维持登录状态。"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      {status.enabled && status.is_running && (
        <Alert
          message="服务运行中"
          description={`登录状态维持服务正在运行，每 ${status.config?.interval_minutes || 30} 分钟自动检查并维持登录状态。`}
          type="success"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}
      
      {/* 服务状态卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={24}>
          <Card title="服务状态" loading={loading}>
            <Row gutter={16}>
              <Col span={6}>
                <Statistic
                  title="服务状态"
                  value={status.enabled ? (status.is_running ? '运行中' : '已停止') : '已禁用'}
                  valueStyle={{ 
                    color: status.enabled ? (status.is_running ? '#3f8600' : '#cf1322') : '#8c8c8c' 
                  }}
                  prefix={
                    status.enabled ? (
                      status.is_running ? <CheckCircleOutlined /> : <CloseCircleOutlined />
                    ) : <ExclamationCircleOutlined />
                  }
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="执行间隔"
                  value={status.config?.interval_minutes || 30}
                  suffix="分钟"
                  prefix={<ClockCircleOutlined />}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="总运行次数"
                  value={status.stats?.total_runs || 0}
                  prefix={<ReloadOutlined />}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="成功率"
                  value={
                    (status.stats?.total_runs || 0) > 0
                      ? (((status.stats?.successful_maintains || 0) / (status.stats?.total_runs || 1)) * 100).toFixed(1)
                      : 0
                  }
                  suffix="%"
                  prefix={<CheckCircleOutlined />}
                />
              </Col>
            </Row>
            
            <div style={{ marginTop: 16 }}>
              <Space>
                {status.enabled && (
                  <>
                    {!status.is_running ? (
                      <Button
                        type="primary"
                        icon={<PlayCircleOutlined />}
                        onClick={handleStart}
                      >
                        启动服务
                      </Button>
                    ) : (
                      <Button
                        danger
                        icon={<StopOutlined />}
                        onClick={handleStop}
                      >
                        停止服务
                      </Button>
                    )}
                    
                    <Button
                      icon={<PauseCircleOutlined />}
                      onClick={handlePause}
                      disabled={!status.is_running}
                    >
                      暂停任务
                    </Button>
                    
                    <Button
                      icon={<PlayCircleOutlined />}
                      onClick={handleResume}
                      disabled={!status.is_running}
                    >
                      恢复任务
                    </Button>
                    
                    <Button
                      icon={<ThunderboltOutlined />}
                      onClick={handleTrigger}
                      disabled={!status.is_running}
                    >
                      立即执行
                    </Button>
                  </>
                )}
                
                <Button
                  icon={<SettingOutlined />}
                  onClick={() => {
                    form.setFieldsValue({ minutes: status.config?.interval_minutes || 30 });
                    setIntervalModalVisible(true);
                  }}
                >
                  设置间隔
                </Button>
                
                <Button
                  icon={<ReloadOutlined />}
                  onClick={loadData}
                >
                  刷新
                </Button>
              </Space>
            </div>
            
            {status.last_run_time && (
              <div style={{ marginTop: 16 }}>
                <Text type="secondary">
                  最后运行时间: {new Date(status.last_run_time).toLocaleString()}
                </Text>
              </div>
            )}

            {/* 实时状态指示器 */}
            <div style={{ marginTop: 16, padding: 12, backgroundColor: '#f5f5f5', borderRadius: 6 }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: 8, marginBottom: 8 }}>
                <div
                  style={{
                    width: 8,
                    height: 8,
                    borderRadius: '50%',
                    backgroundColor: status.enabled && status.is_running ? '#52c41a' : '#d9d9d9',
                    animation: status.enabled && status.is_running ? 'pulse 2s infinite' : 'none'
                  }}
                />
                <Text strong>
                  {status.enabled ? (status.is_running ? '服务运行中' : '服务已停止') : '服务已禁用'}
                </Text>
              </div>
              <Text type="secondary" style={{ fontSize: 12 }}>
                {status.enabled && status.is_running
                  ? `下次执行时间约为 ${status.config?.interval_minutes || 30} 分钟后`
                  : '服务未运行，不会自动维持登录状态'
                }
              </Text>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 详细信息标签页 */}
      <Card>
        <Tabs defaultActiveKey="logs">
          <TabPane tab="操作日志" key="logs">
            <Table
              columns={recordColumns}
              dataSource={records}
              rowKey="id"
              pagination={{ pageSize: 20 }}
              scroll={{ x: 1000 }}
            />
          </TabPane>
          
          <TabPane tab="账号列表" key="accounts">
            <Table
              columns={accountColumns}
              dataSource={accounts}
              rowKey="id"
              pagination={false}
            />
          </TabPane>
          
          <TabPane tab="平台统计" key="stats">
            <LoginKeeperCharts platformStats={platformStats} loading={loading} />
          </TabPane>

          <TabPane tab="配置信息" key="config">
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Card title="服务配置" size="small">
                  <Descriptions column={1} size="small">
                    <Descriptions.Item label="执行间隔">
                      {status.config?.interval_minutes || 30} 分钟
                    </Descriptions.Item>
                    <Descriptions.Item label="最大重试次数">
                      {status.config?.max_retries || 3} 次
                    </Descriptions.Item>
                    <Descriptions.Item label="并发账号数">
                      {status.config?.concurrent_accounts || 3} 个
                    </Descriptions.Item>
                    <Descriptions.Item label="浏览器超时">
                      {status.config?.browser_timeout || 60} 秒
                    </Descriptions.Item>
                    <Descriptions.Item label="启用平台">
                      <div>
                        {(status.config?.enabled_platforms || []).map(platform => (
                          <Tag key={platform} color="blue" style={{ marginBottom: 4 }}>
                            {getPlatformName(platform)}
                          </Tag>
                        ))}
                      </div>
                    </Descriptions.Item>
                  </Descriptions>
                </Card>
              </Col>
              <Col span={12}>
                <Card title="运行统计" size="small">
                  <Descriptions column={1} size="small">
                    <Descriptions.Item label="总运行次数">
                      {status.stats?.total_runs || 0} 次
                    </Descriptions.Item>
                    <Descriptions.Item label="成功维持次数">
                      <Text style={{ color: '#52c41a' }}>
                        {status.stats?.successful_maintains || 0} 次
                      </Text>
                    </Descriptions.Item>
                    <Descriptions.Item label="失败维持次数">
                      <Text style={{ color: '#ff4d4f' }}>
                        {status.stats?.failed_maintains || 0} 次
                      </Text>
                    </Descriptions.Item>
                    <Descriptions.Item label="处理账号总数">
                      {status.stats?.accounts_processed || 0} 个
                    </Descriptions.Item>
                    <Descriptions.Item label="平均成功率">
                      <Text style={{
                        color: (status.stats?.total_runs || 0) > 0 &&
                               ((status.stats?.successful_maintains || 0) / (status.stats?.total_runs || 1)) > 0.8
                               ? '#52c41a' : '#ff4d4f'
                      }}>
                        {(status.stats?.total_runs || 0) > 0
                          ? (((status.stats?.successful_maintains || 0) / (status.stats?.total_runs || 1)) * 100).toFixed(1)
                          : 0}%
                      </Text>
                    </Descriptions.Item>
                  </Descriptions>
                </Card>
              </Col>
            </Row>
          </TabPane>

          <TabPane tab="使用说明" key="help">
            <Card title="功能说明">
              <div style={{ lineHeight: '1.8' }}>
                <Title level={4}>什么是登录保持？</Title>
                <p>
                  登录保持功能会定期自动访问各个社交媒体平台的页面，以维持您的登录状态。
                  这样可以确保在需要进行数据操作时，您的账号仍然处于登录状态，避免因登录过期而导致的操作失败。
                </p>

                <Title level={4}>支持的平台</Title>
                <ul>
                  <li><strong>微信公众号</strong> - 自动访问公众号管理后台页面</li>
                  <li><strong>小红书</strong> - 自动访问创作者中心页面</li>
                  <li><strong>微信视频号</strong> - 自动访问视频号助手页面</li>
                </ul>

                <Title level={4}>工作原理</Title>
                <ol>
                  <li>系统会根据设定的时间间隔（默认30分钟）自动运行</li>
                  <li>随机选择平台页面进行访问，模拟真实用户行为</li>
                  <li>检测页面内容，判断登录状态是否有效</li>
                  <li>更新数据库中的登录状态信息</li>
                  <li>记录详细的操作日志供查看和分析</li>
                </ol>

                <Title level={4}>注意事项</Title>
                <Alert
                  message="重要提示"
                  description={
                    <ul style={{ margin: 0, paddingLeft: 20 }}>
                      <li>建议设置30-60分钟的执行间隔，避免过于频繁的访问</li>
                      <li>服务会在后台自动运行，无需人工干预</li>
                      <li>如果检测到登录状态失效，会自动更新数据库状态</li>
                      <li>可以通过「立即执行」按钮手动触发一次维持任务</li>
                      <li>所有操作都会记录详细日志，便于问题排查</li>
                    </ul>
                  }
                  type="info"
                  showIcon
                />
              </div>
            </Card>
          </TabPane>
        </Tabs>
      </Card>

      {/* 设置间隔模态框 */}
      <Modal
        title="设置执行间隔"
        open={intervalModalVisible}
        onCancel={() => setIntervalModalVisible(false)}
        onOk={() => form.submit()}
      >
        <Form
          form={form}
          onFinish={handleModifyInterval}
          layout="vertical"
        >
          <Form.Item
            name="minutes"
            label="执行间隔（分钟）"
            rules={[
              { required: true, message: '请输入执行间隔' },
              { type: 'number', min: 5, max: 1440, message: '间隔应在5-1440分钟之间' }
            ]}
          >
            <InputNumber
              min={5}
              max={1440}
              style={{ width: '100%' }}
              placeholder="请输入执行间隔（分钟）"
            />
          </Form.Item>
          <Alert
            message="提示"
            description="建议设置30-60分钟的间隔，过于频繁可能被平台检测为异常行为。"
            type="info"
            showIcon
          />
        </Form>
      </Modal>
    </div>
  );
};

export default LoginKeeper;
