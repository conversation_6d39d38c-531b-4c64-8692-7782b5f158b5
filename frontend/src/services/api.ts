import axios from 'axios';

// 根据环境变量确定API基础URL
const getBaseURL = () => {
  return '/api';
};

const api = axios.create({
  baseURL: getBaseURL(),
  timeout: 300000, // 5分钟超时，适合长时间操作
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // 清除过期的token
      localStorage.removeItem('token');

      // 显示友好的提示信息
      if (window.location.pathname !== '/login') {
        // 动态导入message以避免循环依赖
        import('antd').then(({ message }) => {
          message.warning('登录已过期，请重新登录');
        });

        // 延迟跳转，让用户看到提示信息
        setTimeout(() => {
          window.location.href = '/login';
        }, 1500);
      }
    }
    return Promise.reject(error);
  }
);

export default api;