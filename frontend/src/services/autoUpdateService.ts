import api from './api';

export interface AutoUpdateConfig {
  id?: number;
  enabled: boolean;
  update_time: string;  // HH:MM format
  update_days: number;
  last_update?: string;
  created_at?: string;
  updated_at?: string;
}

export interface AutoUpdateStatus {
  enabled: boolean;
  update_time: string;
  update_days: number;
  last_update?: string;
  scheduler_running: boolean;
  next_run?: string;
}

export interface AutoUpdateResponse {
  success: boolean;
  data?: any;
  error?: string;
  message?: string;
}

class AutoUpdateService {
  // 获取自动更新配置
  async getConfig(): Promise<AutoUpdateResponse> {
    try {
      const response = await api.get('/auto-update/config');
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.detail || error.message || '获取配置失败'
      };
    }
  }

  // 更新自动更新配置
  async updateConfig(config: AutoUpdateConfig): Promise<AutoUpdateResponse> {
    try {
      const response = await api.post('/auto-update/config', {
        enabled: config.enabled,
        update_time: config.update_time,
        update_days: config.update_days
      });
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.detail || error.message || '更新配置失败'
      };
    }
  }

  // 获取自动更新状态
  async getStatus(): Promise<AutoUpdateResponse> {
    try {
      const response = await api.get('/auto-update/status');
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.detail || error.message || '获取状态失败'
      };
    }
  }

  // 启动调度器
  async startScheduler(): Promise<AutoUpdateResponse> {
    try {
      const response = await api.post('/auto-update/start');
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.detail || error.message || '启动调度器失败'
      };
    }
  }

  // 停止调度器
  async stopScheduler(): Promise<AutoUpdateResponse> {
    try {
      const response = await api.post('/auto-update/stop');
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.detail || error.message || '停止调度器失败'
      };
    }
  }

  // 测试自动更新
  async testUpdate(): Promise<AutoUpdateResponse> {
    try {
      const response = await api.post('/auto-update/test');
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.detail || error.message || '测试更新失败'
      };
    }
  }
}

// 导出单例实例
export const autoUpdateService = new AutoUpdateService();
export default autoUpdateService;
