import api from './api';

export interface UpdateTask {
  task_id: number;
  start_date: string;
  end_date: string;
  status: string;
  total_accounts: number;
  completed_accounts: number;
  current_account_name?: string;
  current_step?: string;
  error_message?: string;
  created_at: string;
  updated_at: string;
  completed_at?: string;
  progress_percent: number;
}

export interface TaskItem {
  id: number;
  account_name: string;
  platform: string;
  data_type: string;
  data_type_display: string;
  status: string;
  error_message?: string;
  started_at?: string;
  completed_at?: string;
  can_retry: boolean;
}

export interface TaskItemsResponse {
  items: TaskItem[];
  total: number;
  page: number;
  page_size: number;
  stats: {
    pending: number;
    running: number;
    completed: number;
    failed: number;
    retrying: number;
    cancelled: number;
    success_rate: number;
  };
}

export interface TaskSummary {
  total: number;
  pending: number;
  running: number;
  completed: number;
  failed: number;
  retrying: number;
  cancelled: number;
  success_rate: number;
}

class DataUpdateService {
  // 启动数据更新任务
  async startDataUpdate(startDate: string, endDate: string): Promise<any> {
    const response = await api.post('/data-update/start', {
      start_date: startDate,
      end_date: endDate
    });
    return response.data;
  }

  // 获取任务状态
  async getTaskStatus(taskId: number): Promise<UpdateTask> {
    const response = await api.get(`/data-update/status/${taskId}`);
    return response.data;
  }

  // 获取历史记录
  async getHistory(): Promise<UpdateTask[]> {
    const response = await api.get('/data-update/history');
    return response.data;
  }

  // 获取任务明细
  async getTaskItems(
    recordId: number,
    page: number = 1,
    pageSize: number = 20,
    status?: string
  ): Promise<TaskItemsResponse> {
    const params = new URLSearchParams({
      page: page.toString(),
      page_size: pageSize.toString()
    });
    
    if (status) {
      params.append('status', status);
    }

    const response = await api.get(`/data-update/tasks/${recordId}/items?${params}`);
    return response.data;
  }

  // 获取任务汇总
  async getTaskSummary(recordId: number): Promise<TaskSummary> {
    const response = await api.get(`/data-update/tasks/${recordId}/summary`);
    return response.data;
  }

  // 重试任务项
  async retryTaskItem(itemId: number): Promise<any> {
    const response = await api.post(`/data-update/tasks/items/${itemId}/retry`);
    return response.data;
  }

  // 停止任务
  async stopTask(recordId: number): Promise<any> {
    const response = await api.post(`/data-update/tasks/${recordId}/stop`);
    return response.data;
  }

  // 删除任务
  async deleteTask(recordId: number): Promise<any> {
    const response = await api.delete(`/data-update/tasks/${recordId}`);
    return response.data;
  }

  // 获取数据范围
  async getDataRange(): Promise<any> {
    const response = await api.get('/data-update/data-range');
    return response.data;
  }
}

export default new DataUpdateService();
