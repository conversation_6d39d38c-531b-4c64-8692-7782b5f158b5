import api from './api';

export interface LoginKeeperStatus {
  is_running: boolean;
  last_run_time: string | null;
  config: {
    interval_minutes: number;
    enabled_platforms: string[];
    max_retries: number;
    concurrent_accounts: number;
    browser_timeout: number;
  };
  stats: {
    total_runs: number;
    successful_maintains: number;
    failed_maintains: number;
    accounts_processed: number;
  };
  enabled: boolean;
}

export interface LoginKeeperRecord {
  id: number;
  account_name: string;
  platform: string;
  visited_page: string;
  page_title: string;
  status: 'success' | 'failed' | 'login_expired';
  error_message: string | null;
  response_time: number;
  created_at: string;
}

export interface PlatformStats {
  [platform: string]: {
    total_attempts: number;
    successful: number;
    failed: number;
    expired: number;
    success_rate: number;
    avg_response_time: number;
  };
}

export interface AccountStats {
  account_id: number;
  account_name: string;
  platform: string;
  days: number;
  total_attempts: number;
  successful: number;
  failed: number;
  expired: number;
  success_rate: number;
  avg_response_time: number;
}

export interface LoginKeeperAccount {
  id: number;
  name: string;
  platform: string;
  platform_name: string;
  login_status: boolean;
  last_login_time: string | null;
  is_supported: boolean;
  created_at: string;
}

export interface LoginKeeperConfig {
  config: {
    interval_minutes: number;
    enabled_platforms: string[];
    max_retries: number;
    concurrent_accounts: number;
    browser_timeout: number;
    enabled: boolean;
  };
  platform_info: {
    [platform: string]: {
      info: {
        name: string;
        description: string;
        base_url: string;
      };
      pages_count: number;
      validation_rules: {
        success_indicators_count: number;
        login_required_indicators_count: number;
      };
    };
  };
  enabled: boolean;
}

class LoginKeeperService {
  // 获取服务状态
  async getStatus(): Promise<LoginKeeperStatus> {
    const response = await api.get('/login-keeper/status');
    const data = response.data;

    // 转换后端返回的数据结构为前端期望的格式
    if (data.success && data.job_status && data.job_status.keeper_service_status) {
      const jobStatus = data.job_status;
      const keeperStatus = data.job_status.keeper_service_status;

      // is_running 应该基于调度器状态，而不是keeper服务的内部状态
      const isRunning = jobStatus.scheduler_running && jobStatus.job_exists && !jobStatus.job_paused;

      return {
        is_running: isRunning,
        last_run_time: keeperStatus.last_run_time,
        config: keeperStatus.config,
        stats: keeperStatus.stats,
        enabled: data.enabled
      };
    }

    // 如果数据结构不符合预期，返回默认值
    return {
      is_running: false,
      last_run_time: null,
      config: {
        interval_minutes: 30,
        enabled_platforms: [],
        max_retries: 3,
        concurrent_accounts: 3,
        browser_timeout: 60
      },
      stats: {
        total_runs: 0,
        successful_maintains: 0,
        failed_maintains: 0,
        accounts_processed: 0
      },
      enabled: false
    };
  }

  // 启动服务
  async startService(): Promise<{ success: boolean; message: string }> {
    const response = await api.post('/login-keeper/start');
    return response.data;
  }

  // 停止服务
  async stopService(): Promise<{ success: boolean; message: string }> {
    const response = await api.post('/login-keeper/stop');
    return response.data;
  }

  // 暂停任务
  async pauseJob(): Promise<{ success: boolean; message: string }> {
    const response = await api.post('/login-keeper/pause');
    return response.data;
  }

  // 恢复任务
  async resumeJob(): Promise<{ success: boolean; message: string }> {
    const response = await api.post('/login-keeper/resume');
    return response.data;
  }

  // 手动触发任务
  async triggerJob(): Promise<{ success: boolean; message: string; result: any }> {
    const response = await api.post('/login-keeper/trigger');
    return response.data;
  }

  // 修改执行间隔
  async modifyInterval(minutes: number): Promise<{ success: boolean; message: string }> {
    const response = await api.post(`/login-keeper/modify-interval?minutes=${minutes}`);
    return response.data;
  }

  // 获取操作日志
  async getLogs(limit: number = 50): Promise<{ success: boolean; records: LoginKeeperRecord[]; total: number }> {
    const response = await api.get(`/login-keeper/logs?limit=${limit}`);
    return response.data;
  }

  // 获取平台统计
  async getPlatformStats(days: number = 7): Promise<{ success: boolean; stats: PlatformStats; days: number }> {
    const response = await api.get(`/login-keeper/stats/platform?days=${days}`);
    return response.data;
  }

  // 获取账号统计
  async getAccountStats(accountId: number, days: number = 7): Promise<{ success: boolean; stats: AccountStats }> {
    const response = await api.get(`/login-keeper/stats/account/${accountId}?days=${days}`);
    return response.data;
  }

  // 获取账号列表
  async getAccounts(): Promise<{ success: boolean; accounts: LoginKeeperAccount[]; total: number; supported_platforms: string[] }> {
    const response = await api.get('/login-keeper/accounts');
    return response.data;
  }

  // 测试账号登录状态
  async testAccount(accountId: number): Promise<{ success: boolean; test_result: any; message: string }> {
    const response = await api.post(`/login-keeper/test-account/${accountId}`);
    return response.data;
  }

  // 获取配置信息
  async getConfig(): Promise<LoginKeeperConfig> {
    const response = await api.get('/login-keeper/config');
    return response.data;
  }
}

const loginKeeperService = new LoginKeeperService();
export default loginKeeperService;
