import api from './api';

export interface OverviewResponse {
  success: boolean;
  data?: any[];
  error?: string;
  date_columns?: string[];
  data_date_range?: string;
  user_sources?: string[];
}

class OverviewService {
  // 获取多平台账号汇总数据
  async getMultiPlatformAccountSummary(): Promise<OverviewResponse> {
    try {
      const response = await api.get('/data-details/account-summary');
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.detail || error.message || '获取多平台账号汇总失败'
      };
    }
  }

  // 获取多平台增长汇总数据
  async getMultiPlatformGrowthSummary(): Promise<OverviewResponse> {
    try {
      const response = await api.get('/data-details/growth-summary');
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.detail || error.message || '获取多平台增长汇总失败'
      };
    }
  }
}

// 导出单例实例
export const overviewService = new OverviewService();
export default overviewService;
