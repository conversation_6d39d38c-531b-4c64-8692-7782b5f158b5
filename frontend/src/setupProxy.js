const { createProxyMiddleware } = require('http-proxy-middleware');

module.exports = function(app) {
  // 检测是否在Docker容器中
  const isDocker = process.env.NODE_ENV === 'development' && process.env.DOCKER_ENV === 'true';
  const backendTarget = isDocker ? 'http://backend:8000' : 'http://localhost:8000';
  
  console.log(`Setting up proxy to: ${backendTarget}`);

  // 微信登录二维码接口 - 需要更长的超时时间
  app.use(
    '/api/wechat/login/qrcode',
    createProxyMiddleware({
      target: backendTarget,
      changeOrigin: true,
      secure: false,
      logLevel: 'debug',
      timeout: 300000, // 5分钟超时
      proxyTimeout: 300000,
      onError: (err, req, res) => {
        console.error('Proxy Error for login:', err.message);
        console.error('Request URL:', req.url);
        console.error('Target:', backendTarget);
        console.error('Timeout settings: 300000ms');
      }
    })
  );

  // 代理其他API请求到后端容器
  app.use(
    '/api',
    createProxyMiddleware({
      target: backendTarget,
      changeOrigin: true,
      secure: false,
      logLevel: 'warn',
      timeout: 60000, // 其他接口60秒超时
      onError: (err, req, res) => {
        console.error('Proxy Error:', err.message);
        console.error('Request URL:', req.url);
        console.error('Target:', backendTarget);
      }
    })
  );
};
