#!/bin/sh

# 前端智能启动脚本

set -e

echo "🚀 启动前端服务..."

# 检查是否为开发模式
if [ "${NODE_ENV:-production}" = "development" ] || [ "${HOT_RELOAD:-false}" = "true" ]; then
    echo "🔥 启用前端热更新模式"
    echo "📁 监控目录: /app/src"
    echo "⚡ 代码变更将自动刷新浏览器"
    
    # 设置开发环境变量
    export CHOKIDAR_USEPOLLING=true
    export WATCHPACK_POLLING=true
    export FAST_REFRESH=true
    export DANGEROUSLY_DISABLE_HOST_CHECK=true
    export DISABLE_HOST_CHECK=true
    export HOST=0.0.0.0
    export PORT=3000
    export WDS_SOCKET_HOST=localhost
    export WDS_SOCKET_PORT=3000

    echo "🔥 开发模式配置:"
    echo "   - Host检查: 已禁用"
    echo "   - 监听地址: 0.0.0.0:3000"
    echo "   - 热更新: 已启用"
    echo "   - 文件轮询: 已启用"

    # 启动开发服务器，允许所有Host
    exec npm start
else
    echo "🏭 生产模式启动"
    echo "📦 使用预构建的静态文件"
    
    # 如果没有构建文件，先构建
    if [ ! -d "/app/build" ]; then
        echo "📦 构建生产版本..."
        npm run build
    fi
    
    # 启动nginx（如果在nginx容器中）
    if command -v nginx >/dev/null 2>&1; then
        exec nginx -g "daemon off;"
    else
        # 如果不在nginx容器中，使用serve
        npx serve -s build -l 3000
    fi
fi
