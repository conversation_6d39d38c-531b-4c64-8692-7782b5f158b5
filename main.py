from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from dotenv import load_dotenv
from contextlib import asynccontextmanager
import logging
import sys
from datetime import datetime
from app.routers import auth, accounts, wechat, analytics, feishu, feishu_app, data_details, data_update, data_download, login_keeper, auto_update
from app.database import engine, Base
from app.background.login_state_keeper import start_login_keeper, stop_login_keeper
from app.services.auto_update_service import AutoUpdateService

# 加载环境变量
load_dotenv()

# 配置日志格式，包含时间戳
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

# 获取logger
logger = logging.getLogger(__name__)

# 重写print函数，添加时间戳
original_print = print
def print_with_timestamp(*args, **kwargs):
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    original_print(f"[{timestamp}]", *args, **kwargs)

# 替换全局print函数
print = print_with_timestamp

# 创建数据库表
Base.metadata.create_all(bind=engine)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("🚀 社交媒体数据管理系统启动中...")
    print("🚀 社交媒体数据管理系统启动中...")

    # 启动常驻浏览器
    from app.services.browser_manager import browser_manager
    await browser_manager.start()
    logger.info("✅ 常驻浏览器已启动")
    print("✅ 常驻浏览器已启动")

    # await start_login_keeper()
    # logger.info("✅ 登录状态保持器已启动")
    # print("✅ 登录状态保持器已启动")

    AutoUpdateService.start_scheduler()  # 启动自动更新调度器
    logger.info("✅ 自动更新调度器已启动")
    print("✅ 自动更新调度器已启动")

    logger.info("🎉 系统启动完成，准备接收请求")
    print("🎉 系统启动完成，准备接收请求")

    yield

    # 关闭时执行
    logger.info("🔄 系统正在关闭...")
    print("🔄 系统正在关闭...")

    try:
        # 停止自动更新调度器
        AutoUpdateService.stop_scheduler()
        logger.info("✅ 自动更新调度器已停止")
        print("✅ 自动更新调度器已停止")
    except Exception as e:
        logger.warning(f"停止自动更新调度器时出错: {e}")

    try:
        # 停止常驻浏览器
        from app.services.browser_manager import browser_manager
        await browser_manager.stop()
        logger.info("✅ 常驻浏览器已停止")
        print("✅ 常驻浏览器已停止")
    except Exception as e:
        logger.warning(f"停止浏览器时出错: {e}")

    try:
        # 关闭数据库连接池
        from app.database import engine
        engine.dispose()
        logger.info("✅ 数据库连接池已关闭")
        print("✅ 数据库连接池已关闭")
    except Exception as e:
        logger.warning(f"关闭数据库连接池时出错: {e}")

    # 等待一下确保所有资源都释放
    import asyncio
    await asyncio.sleep(1)

    logger.info("👋 系统已安全关闭")
    print("👋 系统已安全关闭")

app = FastAPI(
    title="社交媒体数据管理系统",
    version="1.0.0"
    , lifespan=lifespan
)

# CORS配置
import os
allowed_origins = os.getenv("ALLOWED_ORIGINS", "http://localhost:3000,http://sm.dev.mynatapp.cc").split(",")
app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 路由注册
app.include_router(auth.router, prefix="/api/auth", tags=["认证"])
app.include_router(accounts.router, prefix="/api/accounts", tags=["账号管理"])
app.include_router(wechat.router, prefix="/api/wechat", tags=["微信公众号"])
app.include_router(analytics.router, prefix="/api/analytics", tags=["数据分析"])
app.include_router(data_details.router, prefix="/api/data-details", tags=["数据明细"])
app.include_router(data_update.router, prefix="/api/data-update", tags=["数据更新"])
app.include_router(data_download.router, prefix="/api/data-download", tags=["数据下载"])
app.include_router(feishu.router, tags=["飞书多维表格"])
app.include_router(feishu_app.router, tags=["飞书应用管理"])
app.include_router(login_keeper.router, tags=["登录状态维持"])
app.include_router(auto_update.router, prefix="/api", tags=["自动更新"])

@app.get("/")
async def root():
    return {"message": "社交媒体数据管理系统"}