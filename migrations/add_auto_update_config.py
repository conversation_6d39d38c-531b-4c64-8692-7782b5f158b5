#!/usr/bin/env python3
"""
添加自动更新配置表
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import engine, SessionLocal
from app.models import AutoUpdateConfig
from sqlalchemy import text

def create_auto_update_config_table():
    """创建自动更新配置表"""
    print("创建自动更新配置表...")
    
    try:
        # 创建表
        AutoUpdateConfig.__table__.create(engine, checkfirst=True)
        print("✅ 自动更新配置表创建成功")
        
        # 插入默认配置
        db = SessionLocal()
        try:
            existing_config = db.query(AutoUpdateConfig).first()
            if not existing_config:
                default_config = AutoUpdateConfig(
                    enabled=False,
                    update_time="02:00",
                    update_days=30
                )
                db.add(default_config)
                db.commit()
                print("✅ 默认配置插入成功")
            else:
                print("✅ 配置已存在，跳过插入")
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ 创建表失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("🚀 开始数据库迁移：添加自动更新配置表")
    
    success = create_auto_update_config_table()
    
    if success:
        print("🎉 数据库迁移完成！")
        print("\n📋 迁移内容:")
        print("  ✅ 创建 auto_update_config 表")
        print("  ✅ 插入默认配置")
        print("\n🔧 表结构:")
        print("  - id: 主键")
        print("  - enabled: 是否启用自动更新")
        print("  - update_time: 更新时间 (HH:MM)")
        print("  - update_days: 更新天数")
        print("  - last_update: 上次更新时间")
        print("  - created_at: 创建时间")
        print("  - updated_at: 更新时间")
    else:
        print("❌ 数据库迁移失败")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
