#!/usr/bin/env python3
"""
数据更新任务明细表迁移脚本

创建 data_update_task_items 表来支持任务明细管理和单项重试功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import engine, SessionLocal
from app.models import Base, DataUpdateTaskItem
from sqlalchemy import text


def create_task_items_table():
    """创建任务明细表"""
    print("正在创建 data_update_task_items 表...")
    
    # 创建表的SQL
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS data_update_task_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        update_record_id INTEGER NOT NULL,
        account_id INTEGER NOT NULL,
        account_name VARCHAR(100) NOT NULL,
        platform VARCHAR(50) NOT NULL,
        data_type VARCHAR(50) NOT NULL,
        data_type_display VARCHAR(100),
        status VARCHAR(20) DEFAULT 'pending',
        error_message TEXT,
        started_at DATETIME,
        completed_at DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (update_record_id) REFERENCES data_update_records(id),
        FOREIGN KEY (account_id) REFERENCES platform_accounts(id)
    );
    """
    
    # 创建索引的SQL
    create_indexes_sql = [
        "CREATE INDEX IF NOT EXISTS idx_task_items_update_record ON data_update_task_items(update_record_id);",
        "CREATE INDEX IF NOT EXISTS idx_task_items_account ON data_update_task_items(account_id);",
        "CREATE INDEX IF NOT EXISTS idx_task_items_status ON data_update_task_items(status);",
        "CREATE INDEX IF NOT EXISTS idx_task_items_platform ON data_update_task_items(platform);",
        "CREATE INDEX IF NOT EXISTS idx_task_items_data_type ON data_update_task_items(data_type);"
    ]
    
    try:
        with engine.connect() as conn:
            # 创建表
            conn.execute(text(create_table_sql))
            print("✅ data_update_task_items 表创建成功")
            
            # 创建索引
            for index_sql in create_indexes_sql:
                conn.execute(text(index_sql))
            print("✅ 索引创建成功")
            
            conn.commit()
            
    except Exception as e:
        print(f"❌ 创建表失败: {e}")
        return False
    
    return True


def migrate_existing_records():
    """为现有的数据更新记录创建任务明细"""
    print("正在为现有记录创建任务明细...")
    
    db = SessionLocal()
    try:
        # 获取所有现有的数据更新记录
        from app.models import DataUpdateRecord, PlatformAccount
        
        existing_records = db.query(DataUpdateRecord).all()
        
        if not existing_records:
            print("没有现有的数据更新记录需要迁移")
            return True
        
        print(f"找到 {len(existing_records)} 条现有记录需要迁移")
        
        # 平台数据类型映射
        PLATFORM_DATA_TYPES = {
            'wechat_channels': [
                ('single_video', '单篇视频数据'),
                ('follower_data', '关注者数据')
            ],
            'xiaohongshu': [
                ('note_data', '笔记数据'),
                ('account_overview', '账号概览'),
                ('fans_data', '粉丝数据')
            ],
            'wechat_mp': [
                ('content_trend', '内容趋势'),
                ('content_source', '内容来源'),
                ('content_detail', '内容详情'),
                ('user_channel', '用户渠道'),
                ('user_source', '用户来源')
            ]
        }
        
        migrated_count = 0
        
        for record in existing_records:
            # 获取所有已登录的账号（模拟原始逻辑）
            accounts = db.query(PlatformAccount).filter(
                PlatformAccount.login_status == True
            ).all()
            
            for account in accounts:
                if account.platform in PLATFORM_DATA_TYPES:
                    data_types = PLATFORM_DATA_TYPES[account.platform]
                    
                    for data_type, display_name in data_types:
                        # 检查是否已存在任务明细
                        existing_item = db.query(DataUpdateTaskItem).filter(
                            DataUpdateTaskItem.update_record_id == record.id,
                            DataUpdateTaskItem.account_id == account.id,
                            DataUpdateTaskItem.data_type == data_type
                        ).first()
                        
                        if not existing_item:
                            # 创建任务明细
                            task_item = DataUpdateTaskItem(
                                update_record_id=record.id,
                                account_id=account.id,
                                account_name=account.name,
                                platform=account.platform,
                                data_type=data_type,
                                data_type_display=display_name,
                                status='completed' if record.status == 'completed' else 'failed',
                                created_at=record.created_at,
                                updated_at=record.updated_at,
                                started_at=record.created_at,
                                completed_at=record.completed_at
                            )
                            db.add(task_item)
                            migrated_count += 1
        
        db.commit()
        print(f"✅ 成功迁移 {migrated_count} 条任务明细记录")
        
    except Exception as e:
        print(f"❌ 迁移现有记录失败: {e}")
        db.rollback()
        return False
    finally:
        db.close()
    
    return True


def verify_migration():
    """验证迁移结果"""
    print("正在验证迁移结果...")
    
    db = SessionLocal()
    try:
        # 检查表是否存在
        task_items_count = db.query(DataUpdateTaskItem).count()
        print(f"✅ data_update_task_items 表中有 {task_items_count} 条记录")
        
        # 检查关联关系
        from app.models import DataUpdateRecord
        records_with_items = db.query(DataUpdateRecord).join(DataUpdateTaskItem).count()
        print(f"✅ 有 {records_with_items} 条更新记录关联了任务明细")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False
    finally:
        db.close()


def main():
    """主函数"""
    print("🚀 开始数据更新任务明细表迁移")
    print("=" * 50)
    
    steps = [
        ("创建任务明细表", create_task_items_table),
        ("迁移现有记录", migrate_existing_records),
        ("验证迁移结果", verify_migration)
    ]
    
    for step_name, step_func in steps:
        print(f"\n📋 步骤: {step_name}")
        if not step_func():
            print(f"❌ {step_name} 失败，迁移中止")
            return False
    
    print("\n" + "=" * 50)
    print("🎉 数据更新任务明细表迁移完成！")
    print("\n📊 新功能:")
    print("  ✅ 任务明细管理")
    print("  ✅ 单项重试功能")
    print("  ✅ 详细状态跟踪")
    print("  ✅ 错误信息记录")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
