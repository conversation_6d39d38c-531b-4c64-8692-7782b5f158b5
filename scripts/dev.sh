#!/bin/bash

# 开发环境启动脚本 - 启用热更新

set -e

echo "🔥 启动开发环境（热更新模式）"

# 设置环境变量
export HOT_RELOAD=true
export NODE_ENV=development
export FRONTEND_DOCKERFILE=Dockerfile.dev
export FRONTEND_PORT=3000
export FRONTEND_INTERNAL_PORT=3000
export FRONTEND_VOLUME_MODE=rw
export DANGEROUSLY_DISABLE_HOST_CHECK=true

# 检查.env文件
if [ ! -f .env ]; then
    echo "⚠️  .env文件不存在，创建默认配置..."
    cat > .env << EOF
SECRET_KEY=dev-secret-key-$(date +%s)
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
HOT_RELOAD=true
NODE_ENV=development
EOF
    echo "✅ 已创建.env文件"
fi

# 停止现有容器
echo "🛑 停止现有容器..."
docker compose down 2>/dev/null || true

# 使用开发配置启动
echo "🚀 启动开发环境..."
docker compose -f docker-compose.dev.yml up --build

echo "🎉 开发环境启动完成！"
echo ""
echo "📋 访问地址："
echo "  前端: http://localhost:3000"
echo "  后端: http://localhost:8000"
echo ""
echo "🔥 热更新已启用："
echo "  - 修改 app/ 目录下的Python代码会自动重启后端"
echo "  - 修改 frontend/src/ 目录下的React代码会自动刷新前端"
echo ""
echo "⏹️  停止服务: Ctrl+C 或运行 docker-compose -f docker-compose.dev.yml down"
