#!/bin/bash

# 生产环境启动脚本

set -e

echo "🏭 启动生产环境"

# 设置环境变量
export HOT_RELOAD=false
export NODE_ENV=production
export FRONTEND_DOCKERFILE=Dockerfile
export FRONTEND_PORT=3000
export FRONTEND_INTERNAL_PORT=80
export FRONTEND_VOLUME_MODE=ro

# 检查.env文件
if [ ! -f .env ]; then
    echo "❌ .env文件不存在，请先创建配置文件"
    echo "可以复制 .env.example 并修改配置"
    exit 1
fi

# 停止现有容器
echo "🛑 停止现有容器..."
docker-compose down 2>/dev/null || true

# 构建并启动生产环境
echo "🚀 启动生产环境..."
docker-compose up --build -d

echo "🎉 生产环境启动完成！"
echo ""
echo "📋 访问地址："
echo "  前端: http://localhost:3000"
echo "  后端: http://localhost:8000"
echo ""
echo "📊 查看状态: docker-compose ps"
echo "📝 查看日志: docker-compose logs -f"
echo "⏹️  停止服务: docker-compose down"
