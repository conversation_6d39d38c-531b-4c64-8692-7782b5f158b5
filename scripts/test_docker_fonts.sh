#!/bin/bash

# Docker中文字体测试脚本

set -e

echo "=== Docker中文字体支持测试 ==="

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker未运行，请启动Docker"
    exit 1
fi

echo "✅ Docker运行正常"

# 构建镜像
echo ""
echo "1. 构建Docker镜像..."
docker-compose build backend

if [ $? -eq 0 ]; then
    echo "✅ 镜像构建成功"
else
    echo "❌ 镜像构建失败"
    exit 1
fi

# 测试字体支持
echo ""
echo "2. 测试容器中的字体支持..."
docker run --rm social-media-manager_backend python test/test_chinese_fonts.py

if [ $? -eq 0 ]; then
    echo "✅ 容器字体测试成功"
else
    echo "❌ 容器字体测试失败"
    exit 1
fi

# 测试饼图生成
echo ""
echo "3. 测试饼图生成功能..."
docker run --rm -v $(pwd)/test:/app/test_output social-media-manager_backend python test/test_pie_chart_real_data.py

if [ $? -eq 0 ]; then
    echo "✅ 饼图生成测试成功"
else
    echo "❌ 饼图生成测试失败"
    exit 1
fi

# 检查字体文件
echo ""
echo "4. 检查容器中的字体文件..."
docker run --rm social-media-manager_backend bash -c "
    echo '检查字体目录:'
    ls -la /usr/share/fonts/truetype/ | head -10
    echo ''
    echo '检查中文字体:'
    fc-list | grep -i 'zh\|chinese\|cjk\|hei\|noto' | head -5
    echo ''
    echo '字体缓存状态:'
    fc-cache -v | head -3
"

echo ""
echo "🎉 所有测试完成！"
echo ""
echo "📋 测试总结:"
echo "- ✅ Docker镜像构建成功"
echo "- ✅ 中文字体安装正确"
echo "- ✅ matplotlib中文显示正常"
echo "- ✅ 饼图生成功能正常"
echo ""
echo "🚀 现在可以部署到服务器了！"
