#!/bin/bash

# 智能启动脚本 - 根据环境变量决定是否启用热重载

set -e

echo "🚀 启动社交媒体管理系统..."

# 检查是否启用热重载
if [ "${HOT_RELOAD:-false}" = "true" ]; then
    echo "🔥 启用热重载模式"
    echo "📁 监控目录: /app"
    echo "⚡ 代码变更将自动重启服务"
    
    # 启用热重载
    exec uvicorn main:app \
        --host 0.0.0.0 \
        --port 8000 \
        --reload \
        --reload-dir /app \
        --reload-exclude "*.pyc" \
        --reload-exclude "__pycache__" \
        --reload-exclude "*.log" \
        --reload-exclude "temp_downloads" \
        --reload-exclude "user_data" \
        --reload-exclude "data"
else
    echo "🏭 生产模式启动"
    echo "📦 使用预编译代码"
    
    # 生产模式
    exec uvicorn main:app \
        --host 0.0.0.0 \
        --port 8000 \
        --workers 1
fi
