#!/usr/bin/env python3
"""
微信视频号数据下载功能使用示例
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.wechat_channels_service import WeChatChannelsService
from app.services.data_download_service import DataDownloadService


async def example_single_account_download():
    """示例：单个视频号账号数据下载"""
    print("=== 单个视频号账号数据下载示例 ===")
    
    # 创建视频号服务实例
    account_id = 123  # 替换为实际的账号ID
    service = WeChatChannelsService(account_id=account_id, headless=True)
    
    try:
        # 1. 加载登录状态
        print("1. 加载登录状态...")
        if await service.load_login_state():
            print("✅ 登录状态加载成功")
            
            # 2. 检查登录是否有效
            if await service.check_existing_login():
                print("✅ 登录状态有效")
                
                # 3. 下载数据
                print("3. 开始下载单篇视频数据...")
                excel_data = await service.download_single_video_data(
                    start_date="2024-01-01",
                    end_date="2024-01-31"
                )
                
                if excel_data:
                    print(f"✅ 数据下载成功，文件大小: {len(excel_data)} bytes")
                    
                    # 4. 保存文件
                    from app.utils.excel_parser import ExcelDataParser
                    parser = ExcelDataParser()
                    filename = "example_wechat_channels_data.xlsx"
                    
                    if parser.save_excel_file("./", filename, excel_data):
                        print(f"✅ 文件保存成功: {filename}")
                    else:
                        print("❌ 文件保存失败")
                else:
                    print("❌ 数据下载失败")
            else:
                print("❌ 登录状态无效，请重新登录")
        else:
            print("❌ 登录状态加载失败，请先登录")
            
    except Exception as e:
        print(f"❌ 下载过程中出错: {e}")
    finally:
        await service.close()


def example_batch_download_request():
    """示例：批量下载请求（通过API）"""
    print("\n=== 批量下载请求示例 ===")
    
    # 模拟API请求数据
    request_data = {
        "start_date": "2024-01-01",
        "end_date": "2024-01-31",
        "account_ids": [123, 456],  # 包含微信公众号和视频号账号
        # 注意：不指定data_types，系统会自动根据账号类型选择
    }
    
    print("请求数据:")
    print(f"  开始日期: {request_data['start_date']}")
    print(f"  结束日期: {request_data['end_date']}")
    print(f"  账号ID列表: {request_data['account_ids']}")
    print("  数据类型: 自动选择（根据账号平台类型）")
    
    # 展示不同平台支持的数据类型
    print("\n支持的数据类型:")
    mp_types = DataDownloadService.get_platform_data_types("wechat_mp")
    channels_types = DataDownloadService.get_platform_data_types("wechat_channels")
    
    print(f"  微信公众号: {mp_types}")
    print(f"  微信视频号: {channels_types}")
    
    # 展示文件命名规则
    print("\n文件命名示例:")
    mp_filename = DataDownloadService.generate_filename(
        "示例公众号", "content_trend", "2024-01-01", "2024-01-31", "wechat_mp"
    )
    channels_filename = DataDownloadService.generate_filename(
        "示例视频号", "single_video", "2024-01-01", "2024-01-31", "wechat_channels"
    )
    
    print(f"  公众号文件: {mp_filename}")
    print(f"  视频号文件: {channels_filename}")


def example_api_usage():
    """示例：API使用方法"""
    print("\n=== API使用示例 ===")
    
    print("1. 启动数据下载任务:")
    print("POST /data-download/start")
    print("请求体:")
    print("""{
    "start_date": "2024-01-01",
    "end_date": "2024-01-31",
    "account_ids": [123, 456]
    // 注意：data_types字段可以省略，系统会自动选择
}""")
    
    print("\n2. 查询任务状态:")
    print("GET /data-download/status/{task_id}")
    
    print("\n3. 下载结果文件:")
    print("GET /data-download/download/{task_id}")
    
    print("\n4. 获取可用账号:")
    print("GET /data-download/accounts")
    print("返回包含wechat_channels平台的账号")
    
    print("\n5. 获取数据类型:")
    print("GET /data-download/data-types")
    print("返回所有平台支持的数据类型")


def example_configuration():
    """示例：配置说明"""
    print("\n=== 配置说明 ===")
    
    print("1. 数据类型配置:")
    all_config = DataDownloadService.DATA_TYPE_CONFIG
    for key, name in all_config.items():
        print(f"  {key}: {name}")
    
    print("\n2. 平台支持:")
    platforms = ["wechat_mp", "wechat_service", "wechat_channels"]
    for platform in platforms:
        types = DataDownloadService.get_platform_data_types(platform)
        print(f"  {platform}: {types}")
    
    print("\n3. 文件命名规则:")
    print("  微信公众号: wechat_data_{账号名}+{数据类型}+{开始日期}_to_{结束日期}.xlsx")
    print("  微信视频号: wechat_channels_data_{账号名}+{数据类型}+{开始日期}_to_{结束日期}.xlsx")
    
    print("\n4. 请求拦截机制:")
    print("  - 拦截包含'download'或'export'的POST请求")
    print("  - 替换请求中的startTime和endTime参数")
    print("  - 支持Excel文件格式的响应")


async def main():
    """主函数"""
    print("微信视频号数据下载功能使用示例\n")
    
    # 配置说明
    example_configuration()
    
    # API使用示例
    example_api_usage()
    
    # 批量下载请求示例
    example_batch_download_request()
    
    # 注意：实际的单账号下载需要有效的登录状态
    print("\n注意：实际使用时需要:")
    print("1. 有效的视频号登录状态")
    print("2. 正确的账号ID")
    print("3. 网络连接正常")
    print("4. 视频号平台页面结构稳定")
    
    # 如果需要测试实际下载，取消下面的注释
    # await example_single_account_download()


if __name__ == "__main__":
    asyncio.run(main())
