#!/usr/bin/env python3
"""
测试AntV图表功能
"""

import os
import json

def test_antv_installation():
    """测试AntV安装"""
    print("=== 测试AntV安装 ===")
    
    try:
        package_json_path = "frontend/package.json"
        
        if os.path.exists(package_json_path):
            print(f"✅ package.json文件存在")
            
            with open(package_json_path, 'r', encoding='utf-8') as f:
                package_data = json.load(f)
                
                dependencies = package_data.get('dependencies', {})
                
                if '@ant-design/plots' in dependencies:
                    version = dependencies['@ant-design/plots']
                    print(f"✅ @ant-design/plots 已安装，版本: {version}")
                    return True
                else:
                    print(f"❌ @ant-design/plots 未安装")
                    return False
        else:
            print(f"❌ package.json文件不存在")
            return False
        
    except Exception as e:
        print(f"❌ AntV安装测试失败: {e}")
        return False


def test_chart_imports():
    """测试图表组件导入"""
    print("\n=== 测试图表组件导入 ===")
    
    try:
        datadetails_file = "frontend/src/pages/DataDetails.tsx"
        
        if os.path.exists(datadetails_file):
            print(f"✅ DataDetails文件存在")
            
            with open(datadetails_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # 检查导入语句
                import_checks = [
                    ("AntV图表导入", "from '@ant-design/plots'"),
                    ("饼图组件", "Pie"),
                    ("折线图组件", "Line"),
                    ("布局组件Row", "Row"),
                    ("布局组件Col", "Col")
                ]
                
                all_passed = True
                for check_name, check_content in import_checks:
                    if check_content in content:
                        print(f"✅ {check_name}: 通过")
                    else:
                        print(f"❌ {check_name}: 失败")
                        all_passed = False
                
                return all_passed
        else:
            print(f"❌ DataDetails文件不存在")
            return False
        
    except Exception as e:
        print(f"❌ 图表组件导入测试失败: {e}")
        return False


def test_chart_data_functions():
    """测试图表数据处理函数"""
    print("\n=== 测试图表数据处理函数 ===")
    
    try:
        datadetails_file = "frontend/src/pages/DataDetails.tsx"
        
        with open(datadetails_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
            # 检查数据处理函数
            function_checks = [
                ("饼图数据函数", "const preparePieData = ()"),
                ("折线图数据函数", "const prepareLineData = ()"),
                ("饼图数据结构", "type: item.account_name"),
                ("饼图数值字段", "value: item[latestDateCol]"),
                ("折线图数据结构", "date: dateCol"),
                ("折线图分类字段", "category: account.account_name"),
                ("数据过滤应用", "filterDataByPlatform(accountSummary.data)"),
                ("最新日期获取", "accountSummary.date_columns[0]")
            ]
            
            all_passed = True
            for check_name, check_content in function_checks:
                if check_content in content:
                    print(f"✅ {check_name}: 通过")
                else:
                    print(f"❌ {check_name}: 失败")
                    all_passed = False
            
            return all_passed
        
    except Exception as e:
        print(f"❌ 图表数据处理函数测试失败: {e}")
        return False


def test_chart_components():
    """测试图表组件配置"""
    print("\n=== 测试图表组件配置 ===")
    
    try:
        datadetails_file = "frontend/src/pages/DataDetails.tsx"
        
        with open(datadetails_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
            # 检查图表组件配置
            component_checks = [
                ("图表区域布局", "Row gutter={16}"),
                ("左侧饼图列", "Col span={12}"),
                ("右侧折线图列", "Col span={12}"),
                ("饼图组件", "<Pie"),
                ("折线图组件", "<Line"),
                ("饼图标题", "账号关注数占比"),
                ("折线图标题", "关注数趋势"),
                ("饼图数据绑定", "data={preparePieData()}"),
                ("折线图数据绑定", "data={prepareLineData()}"),
                ("饼图角度字段", "angleField=\"value\""),
                ("饼图颜色字段", "colorField=\"type\""),
                ("折线图X轴", "xField=\"date\""),
                ("折线图Y轴", "yField=\"value\""),
                ("折线图分组", "seriesField=\"category\""),
                ("图表高度", "height={300}"),
                ("图例位置", "position: 'bottom'")
            ]
            
            all_passed = True
            for check_name, check_content in component_checks:
                if check_content in content:
                    print(f"✅ {check_name}: 通过")
                else:
                    print(f"❌ {check_name}: 失败")
                    all_passed = False
            
            return all_passed
        
    except Exception as e:
        print(f"❌ 图表组件配置测试失败: {e}")
        return False


def test_chart_integration():
    """测试图表集成"""
    print("\n=== 测试图表集成 ===")
    
    try:
        datadetails_file = "frontend/src/pages/DataDetails.tsx"
        
        with open(datadetails_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
            # 检查图表集成
            integration_checks = [
                ("图表区域注释", "图表区域"),
                ("数据表格注释", "数据表格"),
                ("React Fragment", "<>"),
                ("Fragment闭合", "</>"),
                ("图表与表格分离", "marginBottom: 24"),
                ("条件渲染", "{accountSummary && ("),
                ("数据过滤应用", content.count("filterDataByPlatform") >= 4),
                ("图表响应式", "gutter={16}")
            ]
            
            all_passed = True
            for check_name, check_content in integration_checks:
                if isinstance(check_content, bool):
                    result = check_content
                else:
                    result = check_content in content
                
                if result:
                    print(f"✅ {check_name}: 通过")
                else:
                    print(f"❌ {check_name}: 失败")
                    all_passed = False
            
            return all_passed
        
    except Exception as e:
        print(f"❌ 图表集成测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 测试AntV图表功能")
    
    tests = [
        ("AntV安装", test_antv_installation),
        ("图表组件导入", test_chart_imports),
        ("图表数据处理函数", test_chart_data_functions),
        ("图表组件配置", test_chart_components),
        ("图表集成", test_chart_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            if result:
                print(f"✅ {test_name} - 通过")
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"AntV图表功能测试总结: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 AntV图表功能完成！")
        print("\n📋 功能总结:")
        print("  ✅ 成功安装@ant-design/plots")
        print("  ✅ 导入Pie和Line图表组件")
        print("  ✅ 实现饼图数据处理函数")
        print("  ✅ 实现折线图数据处理函数")
        print("  ✅ 配置图表组件属性")
        print("  ✅ 集成到社媒账号数据汇总")
        print("\n🚀 图表特性:")
        print("  📊 左侧饼图：显示各账号关注数占比")
        print("  📈 右侧折线图：显示关注数趋势")
        print("  🎯 响应式布局：自适应屏幕大小")
        print("  🔄 实时更新：跟随过滤器变化")
        print("  🎨 美观样式：统一的设计风格")
        print("\n🔧 技术实现:")
        print("  - 饼图：Pie组件，按最新日期数据显示占比")
        print("  - 折线图：Line组件，显示时间序列趋势")
        print("  - 数据处理：preparePieData、prepareLineData函数")
        print("  - 布局：Row/Col响应式网格系统")
        print("  - 集成：与现有过滤器无缝配合")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return False


if __name__ == "__main__":
    main()
