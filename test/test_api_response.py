#!/usr/bin/env python3
"""
测试登录状态维持API响应
"""
import asyncio
import sys
import os
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_api_response():
    """测试API响应结构"""
    print("🔍 测试登录状态维持API响应结构...")
    
    try:
        from app.background.login_state_keeper import get_login_keeper_scheduler
        from app.config.keeper_config import is_keeper_enabled
        
        # 获取调度器
        scheduler = get_login_keeper_scheduler()
        
        # 初始化调度器
        scheduler.initialize()
        
        # 获取状态
        job_status = scheduler.get_job_status()
        scheduler_info = scheduler.get_scheduler_info()
        
        # 模拟API响应
        api_response = {
            "success": True,
            "enabled": is_keeper_enabled(),
            "job_status": job_status,
            "scheduler_info": scheduler_info
        }
        
        print("📊 API响应结构:")
        print(json.dumps(api_response, indent=2, ensure_ascii=False, default=str))
        
        # 检查关键字段
        if "job_status" in api_response and "keeper_service_status" in api_response["job_status"]:
            keeper_status = api_response["job_status"]["keeper_service_status"]
            print("\n✅ keeper_service_status 字段存在")
            
            required_fields = ["is_running", "config", "stats", "enabled"]
            for field in required_fields:
                if field in keeper_status:
                    print(f"✅ {field} 字段存在")
                else:
                    print(f"❌ {field} 字段缺失")
            
            if "config" in keeper_status and keeper_status["config"]:
                config = keeper_status["config"]
                config_fields = ["interval_minutes", "enabled_platforms", "max_retries"]
                for field in config_fields:
                    if field in config:
                        print(f"✅ config.{field} 字段存在")
                    else:
                        print(f"❌ config.{field} 字段缺失")
        else:
            print("❌ keeper_service_status 字段缺失")
        
        print("\n🎯 前端期望的数据结构:")
        expected_structure = {
            "is_running": False,
            "last_run_time": None,
            "config": {
                "interval_minutes": 30,
                "enabled_platforms": [],
                "max_retries": 3,
                "concurrent_accounts": 3,
                "browser_timeout": 60
            },
            "stats": {
                "total_runs": 0,
                "successful_maintains": 0,
                "failed_maintains": 0,
                "accounts_processed": 0
            },
            "enabled": False
        }
        print(json.dumps(expected_structure, indent=2, ensure_ascii=False))
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_api_response())
