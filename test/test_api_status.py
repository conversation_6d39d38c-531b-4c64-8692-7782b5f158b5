#!/usr/bin/env python3
"""
测试API状态返回
"""
import asyncio
import sys
import os
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_api_status():
    """测试API状态返回"""
    print("🔍 测试API状态返回...")
    
    try:
        from app.background.login_state_keeper import get_login_keeper_scheduler
        from app.config.keeper_config import is_keeper_enabled
        
        scheduler = get_login_keeper_scheduler()
        
        print("📊 初始化前的状态:")
        job_status = scheduler.get_job_status()
        print(json.dumps(job_status, indent=2, ensure_ascii=False, default=str))
        
        print("\n🚀 初始化调度器...")
        scheduler.initialize()
        
        print("\n📊 初始化后的状态:")
        job_status = scheduler.get_job_status()
        print(json.dumps(job_status, indent=2, ensure_ascii=False, default=str))
        
        print("\n▶️ 启动调度器...")
        scheduler.start()
        
        print("\n📊 启动后的状态:")
        job_status = scheduler.get_job_status()
        scheduler_info = scheduler.get_scheduler_info()
        
        api_response = {
            "success": True,
            "enabled": is_keeper_enabled(),
            "job_status": job_status,
            "scheduler_info": scheduler_info
        }
        
        print("完整API响应:")
        print(json.dumps(api_response, indent=2, ensure_ascii=False, default=str))
        
        # 分析前端期望的状态
        if api_response["success"] and api_response["job_status"] and api_response["job_status"]["keeper_service_status"]:
            job_status_data = api_response["job_status"]
            keeper_status = api_response["job_status"]["keeper_service_status"]
            
            # 前端的is_running逻辑
            is_running = job_status_data["scheduler_running"] and job_status_data["job_exists"] and not job_status_data["job_paused"]
            
            print(f"\n🎯 前端状态解析:")
            print(f"  - scheduler_running: {job_status_data['scheduler_running']}")
            print(f"  - job_exists: {job_status_data['job_exists']}")
            print(f"  - job_paused: {job_status_data['job_paused']}")
            print(f"  - 计算的is_running: {is_running}")
            print(f"  - enabled: {api_response['enabled']}")
            print(f"  - interval_minutes: {keeper_status['config']['interval_minutes']}")
        
        print("\n⏹️ 停止调度器...")
        scheduler.stop()
        
        print("\n📊 停止后的状态:")
        job_status = scheduler.get_job_status()
        print(json.dumps(job_status, indent=2, ensure_ascii=False, default=str))
        
        # 测试修改间隔
        print("\n🔧 测试修改间隔...")
        scheduler.start()  # 重新启动
        success = scheduler.modify_job_interval(45)
        print(f"修改间隔结果: {success}")
        
        if success:
            print("\n📊 修改间隔后的状态:")
            job_status = scheduler.get_job_status()
            print(json.dumps(job_status, indent=2, ensure_ascii=False, default=str))
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_api_status())
