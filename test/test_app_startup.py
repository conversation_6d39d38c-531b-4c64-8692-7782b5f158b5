#!/usr/bin/env python3
"""
测试应用启动
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_app_import():
    """测试应用导入"""
    try:
        print("🔍 测试应用导入...")
        
        # 测试main模块导入
        import main
        print("✅ main模块导入成功")
        
        # 测试FastAPI应用
        app = main.app
        print(f"✅ FastAPI应用创建成功: {app.title}")
        
        return True
        
    except Exception as e:
        print(f"❌ 应用导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_app_import()
    if success:
        print("🎉 应用导入测试通过")
    else:
        print("💥 应用导入测试失败")
        sys.exit(1)
