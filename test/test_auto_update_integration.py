#!/usr/bin/env python3
"""
测试自动更新集成到数据更新管理界面
"""

import os

def test_auto_update_service():
    """测试自动更新服务文件"""
    print("=== 测试自动更新服务文件 ===")
    
    try:
        service_file = "frontend/src/services/autoUpdateService.ts"
        
        if os.path.exists(service_file):
            print(f"✅ 自动更新服务文件存在")
            
            with open(service_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # 检查关键接口和类型
                checks = [
                    ("AutoUpdateConfig接口", "export interface AutoUpdateConfig"),
                    ("AutoUpdateStatus接口", "export interface AutoUpdateStatus"),
                    ("AutoUpdateResponse接口", "export interface AutoUpdateResponse"),
                    ("getConfig方法", "async getConfig()"),
                    ("updateConfig方法", "async updateConfig("),
                    ("getStatus方法", "async getStatus()"),
                    ("startScheduler方法", "async startScheduler()"),
                    ("stopScheduler方法", "async stopScheduler()"),
                    ("testUpdate方法", "async testUpdate()"),
                    ("服务导出", "export const autoUpdateService")
                ]
                
                all_passed = True
                for check_name, check_content in checks:
                    if check_content in content:
                        print(f"✅ {check_name}: 通过")
                    else:
                        print(f"❌ {check_name}: 失败")
                        all_passed = False
                
                return all_passed
        else:
            print(f"❌ 自动更新服务文件不存在")
            return False
        
    except Exception as e:
        print(f"❌ 自动更新服务测试失败: {e}")
        return False


def test_data_update_integration():
    """测试数据更新页面集成"""
    print("\n=== 测试数据更新页面集成 ===")
    
    try:
        page_file = "frontend/src/pages/DataUpdate.tsx"
        
        if os.path.exists(page_file):
            print(f"✅ 数据更新页面文件存在")
            
            with open(page_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # 检查导入
                import_checks = [
                    ("自动更新服务导入", "from '../services/autoUpdateService'"),
                    ("Switch组件导入", "Switch"),
                    ("TimePicker组件导入", "TimePicker"),
                    ("InputNumber组件导入", "InputNumber"),
                    ("Form组件导入", "Form"),
                    ("Tooltip组件导入", "Tooltip"),
                    ("设置图标导入", "SettingOutlined"),
                    ("时钟图标导入", "ClockCircleOutlined")
                ]
                
                all_passed = True
                for check_name, check_content in import_checks:
                    if check_content in content:
                        print(f"✅ {check_name}: 通过")
                    else:
                        print(f"❌ {check_name}: 失败")
                        all_passed = False
                
                return all_passed
        else:
            print(f"❌ 数据更新页面文件不存在")
            return False
        
    except Exception as e:
        print(f"❌ 数据更新页面集成测试失败: {e}")
        return False


def test_auto_update_state_management():
    """测试自动更新状态管理"""
    print("\n=== 测试自动更新状态管理 ===")
    
    try:
        page_file = "frontend/src/pages/DataUpdate.tsx"
        
        with open(page_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
            # 检查状态管理
            state_checks = [
                ("自动更新配置状态", "autoUpdateConfig, setAutoUpdateConfig"),
                ("自动更新状态", "autoUpdateStatus, setAutoUpdateStatus"),
                ("自动更新加载状态", "autoUpdateLoading, setAutoUpdateLoading"),
                ("表单实例", "const [form] = Form.useForm()"),
                ("获取配置函数", "const fetchAutoUpdateConfig"),
                ("获取状态函数", "const fetchAutoUpdateStatus"),
                ("保存配置函数", "const saveAutoUpdateConfig"),
                ("测试更新函数", "const testAutoUpdate")
            ]
            
            all_passed = True
            for check_name, check_content in state_checks:
                if check_content in content:
                    print(f"✅ {check_name}: 通过")
                else:
                    print(f"❌ {check_name}: 失败")
                    all_passed = False
            
            return all_passed
        
    except Exception as e:
        print(f"❌ 自动更新状态管理测试失败: {e}")
        return False


def test_auto_update_ui_components():
    """测试自动更新UI组件"""
    print("\n=== 测试自动更新UI组件 ===")
    
    try:
        page_file = "frontend/src/pages/DataUpdate.tsx"
        
        with open(page_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
            # 检查UI组件
            ui_checks = [
                ("自动更新配置卡片", "自动更新配置"),
                ("启用开关", "启用自动更新"),
                ("时间选择器", "TimePicker"),
                ("天数输入", "时间跨度（天）"),
                ("保存按钮", "保存配置"),
                ("测试按钮", "测试更新"),
                ("状态显示", "调度器运行中"),
                ("状态信息区域", "backgroundColor: '#f5f5f5'"),
                ("下次运行时间", "下次运行时间"),
                ("表单验证", "rules={[")
            ]
            
            all_passed = True
            for check_name, check_content in ui_checks:
                if check_content in content:
                    print(f"✅ {check_name}: 通过")
                else:
                    print(f"❌ {check_name}: 失败")
                    all_passed = False
            
            return all_passed
        
    except Exception as e:
        print(f"❌ 自动更新UI组件测试失败: {e}")
        return False


def test_useeffect_integration():
    """测试useEffect集成"""
    print("\n=== 测试useEffect集成 ===")
    
    try:
        page_file = "frontend/src/pages/DataUpdate.tsx"
        
        with open(page_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
            # 检查useEffect中是否包含自动更新相关调用
            useeffect_checks = [
                ("获取自动更新配置", "fetchAutoUpdateConfig()"),
                ("获取自动更新状态", "fetchAutoUpdateStatus()")
            ]
            
            all_passed = True
            for check_name, check_content in useeffect_checks:
                if check_content in content:
                    print(f"✅ {check_name}: 通过")
                else:
                    print(f"❌ {check_name}: 失败")
                    all_passed = False
            
            return all_passed
        
    except Exception as e:
        print(f"❌ useEffect集成测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 测试自动更新集成到数据更新管理界面")
    
    tests = [
        ("自动更新服务", test_auto_update_service),
        ("数据更新页面集成", test_data_update_integration),
        ("自动更新状态管理", test_auto_update_state_management),
        ("自动更新UI组件", test_auto_update_ui_components),
        ("useEffect集成", test_useeffect_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            if result:
                print(f"✅ {test_name} - 通过")
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"自动更新集成测试总结: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 自动更新集成完成！")
        print("\n📋 集成总结:")
        print("  ✅ 创建了自动更新服务")
        print("  ✅ 集成到数据更新管理界面")
        print("  ✅ 添加了完整的状态管理")
        print("  ✅ 实现了UI组件和交互")
        print("  ✅ 集成到页面生命周期")
        print("\n🚀 功能特性:")
        print("  ⚙️  自动更新配置卡片")
        print("  🔄 启用/禁用开关")
        print("  ⏰ 更新时间选择器")
        print("  📅 时间跨度输入框")
        print("  💾 配置保存功能")
        print("  🧪 测试更新功能")
        print("  📊 实时状态显示")
        print("  🕐 下次运行时间显示")
        print("\n💡 用户体验:")
        print("  - 在数据更新页面统一管理")
        print("  - 直观的配置界面")
        print("  - 实时状态反馈")
        print("  - 表单验证和错误提示")
        print("  - 一键测试功能")
        print("\n🔗 访问地址:")
        print("  http://localhost:3000/data-update")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return False


if __name__ == "__main__":
    main()
