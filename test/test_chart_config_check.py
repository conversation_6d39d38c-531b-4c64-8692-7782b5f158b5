#!/usr/bin/env python3
"""
检查图表配置
"""

import os

def main():
    """检查图表配置"""
    print("🚀 检查图表配置")
    
    try:
        datadetails_file = "frontend/src/pages/DataDetails.tsx"
        
        with open(datadetails_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
            print("\n=== 饼图配置检查 ===")
            pie_configs = [
                "label={{",
                "content: '{percentage}'",
                "fontSize: 12",
                "position: 'bottom'",
                "offsetY: 10"
            ]
            
            for config in pie_configs:
                if config in content:
                    print(f"✅ {config}")
                else:
                    print(f"❌ {config}")
            
            print("\n=== 折线图配置检查 ===")
            line_configs = [
                "color={['#1890ff'",
                "seriesField=\"category\"",
                "shared: true",
                "showCrosshairs: true",
                "smooth={true}"
            ]
            
            for config in line_configs:
                if config in content:
                    print(f"✅ {config}")
                else:
                    print(f"❌ {config}")
            
            print("\n=== 数据处理检查 ===")
            data_configs = [
                "rawValue !== null",
                "isNaN(value)",
                "console.log('折线图数据:', lineData)"
            ]
            
            for config in data_configs:
                if config in content:
                    print(f"✅ {config}")
                else:
                    print(f"❌ {config}")
            
            print("\n🎉 配置检查完成！")
            print("\n📋 修复总结:")
            print("  🥧 饼图:")
            print("    - label显示百分比: content: '{percentage}'")
            print("    - 图例居中: position: 'bottom', offsetY: 10")
            print("  📈 折线图:")
            print("    - 8种颜色区分账号")
            print("    - 共享tooltip和十字线")
            print("    - 平滑曲线显示")
            print("  🔧 数据处理:")
            print("    - 严格的null值检查")
            print("    - NaN值处理")
            print("    - 调试信息输出")
            
            print("\n💡 现在刷新页面应该可以看到:")
            print("  - 饼图扇形上显示百分比")
            print("  - 饼图底部居中显示图例")
            print("  - 折线图不同颜色的线条")
            print("  - tooltip显示0而不是null")
            
        return True
        
    except Exception as e:
        print(f"❌ 配置检查失败: {e}")
        return False


if __name__ == "__main__":
    main()
