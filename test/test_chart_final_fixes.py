#!/usr/bin/env python3
"""
测试图表最终修复
"""

import os

def test_pie_chart_final_fixes():
    """测试饼图最终修复"""
    print("=== 测试饼图最终修复 ===")
    
    try:
        datadetails_file = "frontend/src/pages/DataDetails.tsx"
        
        with open(datadetails_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
            # 检查饼图修复
            pie_fixes = [
                ("启用label显示", "label={{" in content),
                ("label内容配置", "content: '{percentage}'"),
                ("label样式", "fontSize: 12"),
                ("图例位置", "position: 'bottom'"),
                ("图例偏移", "offsetY: 10"),
                ("图例样式", "itemName: {"),
                ("tooltip配置", "tooltip={{"),
                ("数据绑定", "data={preparePieData()}"),
                ("角度字段", "angleField=\"value\""),
                ("颜色字段", "colorField=\"type\"")
            ]
            
            all_passed = True
            for check_name, check_content in pie_fixes:
                if check_content in content:
                    print(f"✅ {check_name}: 通过")
                else:
                    print(f"❌ {check_name}: 失败")
                    all_passed = False
            
            return all_passed
        
    except Exception as e:
        print(f"❌ 饼图最终修复测试失败: {e}")
        return False


def test_line_chart_final_fixes():
    """测试折线图最终修复"""
    print("\n=== 测试折线图最终修复 ===")
    
    try:
        datadetails_file = "frontend/src/pages/DataDetails.tsx"
        
        with open(datadetails_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
            # 检查折线图修复
            line_fixes = [
                ("颜色数组配置", "color={['#1890ff', '#52c41a'"),
                ("分组字段", "seriesField=\"category\""),
                ("共享tooltip", "shared: true"),
                ("十字线", "showCrosshairs: true"),
                ("tooltip格式化", "formatter: (datum: any)"),
                ("null值处理", "value === null || value === undefined"),
                ("数值转换", "Number(value)"),
                ("图例配置", "legend={{"),
                ("图例偏移", "offsetY: 10"),
                ("平滑曲线", "smooth={true}")
            ]
            
            all_passed = True
            for check_name, check_content in line_fixes:
                if check_content in content:
                    print(f"✅ {check_name}: 通过")
                else:
                    print(f"❌ {check_name}: 失败")
                    all_passed = False
            
            return all_passed
        
    except Exception as e:
        print(f"❌ 折线图最终修复测试失败: {e}")
        return False


def test_data_processing_improvements():
    """测试数据处理改进"""
    print("\n=== 测试数据处理改进 ===")
    
    try:
        datadetails_file = "frontend/src/pages/DataDetails.tsx"
        
        with open(datadetails_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
            # 检查数据处理改进
            data_improvements = [
                ("空值检查", "rawValue !== null && rawValue !== undefined"),
                ("空字符串检查", "rawValue !== ''"),
                ("NaN检查", "isNaN(value)"),
                ("数值转换", "Number(rawValue)"),
                ("默认值设置", "value = 0"),
                ("调试输出", "console.log('折线图数据:', lineData)"),
                ("数据结构", "date: dateCol"),
                ("分类字段", "category: account.account_name")
            ]
            
            all_passed = True
            for check_name, check_content in data_improvements:
                if check_content in content:
                    print(f"✅ {check_name}: 通过")
                else:
                    print(f"❌ {check_name}: 失败")
                    all_passed = False
            
            return all_passed
        
    except Exception as e:
        print(f"❌ 数据处理改进测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 测试图表最终修复")
    
    tests = [
        ("饼图最终修复", test_pie_chart_final_fixes),
        ("折线图最终修复", test_line_chart_final_fixes),
        ("数据处理改进", test_data_processing_improvements)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            if result:
                print(f"✅ {test_name} - 通过")
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"图表最终修复测试总结: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 图表最终修复完成！")
        print("\n📋 修复总结:")
        print("  ✅ 饼图显示百分比label")
        print("  ✅ 饼图图例居中显示")
        print("  ✅ 折线图颜色区分账号")
        print("  ✅ 折线图tooltip不显示null")
        print("  ✅ 数据处理更加健壮")
        print("\n🚀 现在的图表特性:")
        print("  🥧 饼图:")
        print("    - 扇形上显示百分比标签")
        print("    - 底部居中显示图例")
        print("    - 鼠标悬停显示详细信息")
        print("    - 不同颜色区分账号")
        print("  📈 折线图:")
        print("    - 8种颜色区分不同账号")
        print("    - 底部显示图例")
        print("    - 共享tooltip和十字线")
        print("    - 平滑曲线显示")
        print("    - null值正确显示为0")
        print("  🔧 数据处理:")
        print("    - 严格的null/undefined检查")
        print("    - NaN值处理")
        print("    - 空字符串处理")
        print("    - 调试信息输出")
        print("\n💡 用户体验:")
        print("  - 饼图标签清晰显示占比")
        print("  - 折线图颜色丰富易区分")
        print("  - tooltip信息准确无误")
        print("  - 图例布局美观整齐")
        print("  - 响应过滤器变化")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return False


if __name__ == "__main__":
    main()
