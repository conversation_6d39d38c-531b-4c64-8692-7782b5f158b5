#!/usr/bin/env python3
"""
测试图表修复效果
"""

import os

def test_pie_chart_fixes():
    """测试饼图修复"""
    print("=== 测试饼图修复 ===")
    
    try:
        datadetails_file = "frontend/src/pages/DataDetails.tsx"
        
        with open(datadetails_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
            # 检查饼图修复
            pie_fixes = [
                ("移除模板字符串语法", "'{name}: {percentage}'" not in content),
                ("使用函数式label", "content: (data: any) =>"),
                ("百分比计算", "(data.percent * 100).toFixed(1)"),
                ("数据类型转换", "Number(item[latestDateCol])"),
                ("调试日志", "console.log('饼图数据:', pieData)"),
                ("数据过滤", ".filter(item => item.value > 0)")
            ]
            
            all_passed = True
            for check_name, check_content in pie_fixes:
                if isinstance(check_content, bool):
                    result = check_content
                else:
                    result = check_content in content
                
                if result:
                    print(f"✅ {check_name}: 通过")
                else:
                    print(f"❌ {check_name}: 失败")
                    all_passed = False
            
            return all_passed
        
    except Exception as e:
        print(f"❌ 饼图修复测试失败: {e}")
        return False


def test_line_chart_fixes():
    """测试折线图修复"""
    print("\n=== 测试折线图修复 ===")
    
    try:
        datadetails_file = "frontend/src/pages/DataDetails.tsx"
        
        with open(datadetails_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
            # 检查折线图修复
            line_fixes = [
                ("颜色配置", "color={['#1890ff', '#52c41a', '#faad14'"),
                ("tooltip修复", "(datum.value || 0).toLocaleString()"),
                ("数据类型转换", "Number(account[dateCol])"),
                ("调试日志", "console.log('折线图数据:', lineData)"),
                ("图例配置", "legend={{"),
                ("图例位置", "position: 'bottom'"),
                ("seriesField配置", "seriesField=\"category\""),
                ("point配置", "shape: 'diamond'")
            ]
            
            all_passed = True
            for check_name, check_content in line_fixes:
                if check_content in content:
                    print(f"✅ {check_name}: 通过")
                else:
                    print(f"❌ {check_name}: 失败")
                    all_passed = False
            
            return all_passed
        
    except Exception as e:
        print(f"❌ 折线图修复测试失败: {e}")
        return False


def test_data_processing_fixes():
    """测试数据处理修复"""
    print("\n=== 测试数据处理修复 ===")
    
    try:
        datadetails_file = "frontend/src/pages/DataDetails.tsx"
        
        with open(datadetails_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
            # 检查数据处理修复
            data_fixes = [
                ("饼图数据类型转换", "Number(item[latestDateCol])"),
                ("折线图数据类型转换", "Number(account[dateCol])"),
                ("空值处理", "|| 0"),
                ("数据验证", "if (!accountSummary?.data"),
                ("过滤器应用", "filterDataByPlatform(accountSummary.data)"),
                ("调试输出", "console.log"),
                ("数据结构", "type: item.account_name"),
                ("平台信息", "platform: item.platform")
            ]
            
            all_passed = True
            for check_name, check_content in data_fixes:
                if check_content in content:
                    print(f"✅ {check_name}: 通过")
                else:
                    print(f"❌ {check_name}: 失败")
                    all_passed = False
            
            return all_passed
        
    except Exception as e:
        print(f"❌ 数据处理修复测试失败: {e}")
        return False


def test_syntax_fixes():
    """测试语法修复"""
    print("\n=== 测试语法修复 ===")
    
    try:
        datadetails_file = "frontend/src/pages/DataDetails.tsx"
        
        with open(datadetails_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
            # 检查语法修复
            syntax_fixes = [
                ("移除错误模板语法", "'{name}: {percentage}'" not in content),
                ("正确的JSX语法", "<Pie" in content),
                ("正确的函数语法", "content: (data: any) =>" in content),
                ("正确的对象语法", "legend={{" in content),
                ("正确的数组语法", "color={[" in content),
                ("正确的条件语法", "datum.value || 0" in content),
                ("React Fragment", "<>" in content),
                ("Fragment闭合", "</>" in content)
            ]
            
            all_passed = True
            for check_name, check_content in syntax_fixes:
                if isinstance(check_content, bool):
                    result = check_content
                else:
                    result = check_content in content
                
                if result:
                    print(f"✅ {check_name}: 通过")
                else:
                    print(f"❌ {check_name}: 失败")
                    all_passed = False
            
            return all_passed
        
    except Exception as e:
        print(f"❌ 语法修复测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 测试图表修复效果")
    
    tests = [
        ("饼图修复", test_pie_chart_fixes),
        ("折线图修复", test_line_chart_fixes),
        ("数据处理修复", test_data_processing_fixes),
        ("语法修复", test_syntax_fixes)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            if result:
                print(f"✅ {test_name} - 通过")
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"图表修复测试总结: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 图表修复完成！")
        print("\n📋 修复总结:")
        print("  ✅ 修复饼图模板字符串语法错误")
        print("  ✅ 添加折线图颜色配置")
        print("  ✅ 修复tooltip显示null问题")
        print("  ✅ 确保图例正常显示")
        print("  ✅ 优化数据类型转换")
        print("  ✅ 添加调试日志")
        print("\n🚀 修复内容:")
        print("  🥧 饼图:")
        print("    - 使用函数式label替代模板字符串")
        print("    - 正确计算和显示百分比")
        print("    - 数据类型转换确保数值正确")
        print("  📈 折线图:")
        print("    - 添加8种颜色区分不同账号")
        print("    - 修复tooltip显示null为0")
        print("    - 确保图例正常显示")
        print("    - 数据类型转换确保数值正确")
        print("  🔧 数据处理:")
        print("    - Number()转换确保数值类型")
        print("    - || 0 处理空值情况")
        print("    - console.log 添加调试信息")
        print("\n💡 现在应该可以看到:")
        print("  - 饼图正常显示各账号占比")
        print("  - 折线图有颜色区分和图例")
        print("  - tooltip显示0而不是null")
        print("  - 图表响应过滤器变化")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return False


if __name__ == "__main__":
    main()
