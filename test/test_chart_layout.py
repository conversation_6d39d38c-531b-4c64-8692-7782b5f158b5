#!/usr/bin/env python3
"""
测试图表标签布局的脚本
"""

import matplotlib.pyplot as plt
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端

def test_pie_chart_layout():
    """测试饼图标签布局"""
    
    print("=== 测试饼图标签布局 ===")
    
    # 创建测试数据
    test_data = [
        {'channel': '搜一搜', 'count': 1200, 'percentage': 35.3},
        {'channel': '朋友在看', 'count': 800, 'percentage': 23.5},
        {'channel': '看一看', 'count': 600, 'percentage': 17.6},
        {'channel': '问题', 'count': 400, 'percentage': 11.8},
        {'channel': '其他', 'count': 250, 'percentage': 7.4},
        {'channel': '直接访问', 'count': 100, 'percentage': 2.9},
        {'channel': '分享', 'count': 50, 'percentage': 1.5},
    ]
    
    try:
        # 生成饼图
        print("📊 生成测试饼图...")
        
        # 准备数据
        channels = [item['channel'] for item in test_data]
        values = [item['count'] for item in test_data]
        percentages = [item['percentage'] for item in test_data]
        
        # 创建图表
        fig, ax = plt.subplots(figsize=(12, 8))
        
        # 定义颜色
        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8']
        
        # 创建环形图
        wedges, texts = ax.pie(values, labels=None, colors=colors, 
                              wedgeprops=dict(width=0.3, edgecolor='white', linewidth=2),
                              startangle=90, counterclock=False)
        
        # 应用智能标签布局算法 - 四周分布
        def calculate_smart_label_positions(wedges, channels, values, percentages):
            """计算智能标签位置，四周分布避免重叠"""

            # 环形图的半径
            outer_radius = 1.0  # 外圆半径（连接线起点）
            label_radius = 1.5  # 标签距离中心的半径

            # 收集所有楔形的信息
            wedge_info = []
            for i, (wedge, channel, value, pct) in enumerate(zip(wedges, channels, values, percentages)):
                angle = (wedge.theta2 + wedge.theta1) / 2
                wedge_info.append({
                    'index': i,
                    'angle': angle,
                    'channel': channel,
                    'value': value,
                    'pct': pct,
                    'wedge': wedge
                })

            # 按角度排序
            wedge_info.sort(key=lambda x: x['angle'])

            # 分为四个象限
            top_right = []    # 0° <= angle < 90°
            top_left = []     # 90° <= angle < 180°
            bottom_left = []  # 180° <= angle < 270°
            bottom_right = [] # 270° <= angle < 360°

            for info in wedge_info:
                angle = info['angle']
                if 0 <= angle < 90:
                    top_right.append(info)
                elif 90 <= angle < 180:
                    top_left.append(info)
                elif 180 <= angle < 270:
                    bottom_left.append(info)
                else:  # 270 <= angle < 360
                    bottom_right.append(info)
            
            # 计算四个象限的标签分布
            def distribute_labels_in_quadrant(labels, quadrant):
                """在指定象限分布标签，避免重叠"""
                if not labels:
                    return []

                positioned_labels = []

                for i, label_info in enumerate(labels):
                    angle_rad = np.radians(label_info['angle'])

                    # 连接线起点（圆环外沿）
                    x1 = outer_radius * np.cos(angle_rad)
                    y1 = outer_radius * np.sin(angle_rad)

                    # 根据象限确定标签位置和对齐方式
                    if quadrant == 'top_right':
                        # 右上象限：标签在右侧，垂直分布
                        base_x = label_radius * 0.8
                        base_y = label_radius * 0.6
                        x_label = base_x + i * 0.1  # 稍微错开避免重叠
                        y_label = base_y - i * 0.25
                        ha = 'left'

                    elif quadrant == 'top_left':
                        # 左上象限：标签在左侧，垂直分布
                        base_x = -label_radius * 0.8
                        base_y = label_radius * 0.6
                        x_label = base_x - i * 0.1
                        y_label = base_y - i * 0.25
                        ha = 'right'

                    elif quadrant == 'bottom_left':
                        # 左下象限：标签在左侧，垂直分布
                        base_x = -label_radius * 0.8
                        base_y = -label_radius * 0.6
                        x_label = base_x - i * 0.1
                        y_label = base_y + i * 0.25
                        ha = 'right'

                    else:  # bottom_right
                        # 右下象限：标签在右侧，垂直分布
                        base_x = label_radius * 0.8
                        base_y = -label_radius * 0.6
                        x_label = base_x + i * 0.1
                        y_label = base_y + i * 0.25
                        ha = 'left'

                    # 连接线中间点（更自然的弯曲）
                    x_mid = (x1 + x_label) * 0.6
                    y_mid = (y1 + y_label) * 0.6

                    positioned_labels.append({
                        **label_info,
                        'x1': x1, 'y1': y1,  # 连接线起点
                        'x_mid': x_mid, 'y_mid': y_mid,  # 中间点
                        'x_label': x_label, 'y_label': y_label,  # 标签位置
                        'ha': ha
                    })

                return positioned_labels

            # 分别处理四个象限的标签
            top_right_positioned = distribute_labels_in_quadrant(top_right, 'top_right')
            top_left_positioned = distribute_labels_in_quadrant(top_left, 'top_left')
            bottom_left_positioned = distribute_labels_in_quadrant(bottom_left, 'bottom_left')
            bottom_right_positioned = distribute_labels_in_quadrant(bottom_right, 'bottom_right')

            return (top_right_positioned + top_left_positioned +
                   bottom_left_positioned + bottom_right_positioned)
        
        # 计算智能标签位置
        positioned_labels = calculate_smart_label_positions(wedges, channels, values, percentages)
        
        # 绘制标签和连接线
        bbox_props = dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.9, 
                        edgecolor="#cccccc", linewidth=0.8)
        
        for label_info in positioned_labels:
            # 绘制连接线
            # 第一段：从圆环外沿到中间点
            ax.plot([label_info['x1'], label_info['x_mid']], 
                   [label_info['y1'], label_info['y_mid']], 
                   color='#666666', linewidth=1.0, alpha=0.7)
            
            # 第二段：水平线到标签
            ax.plot([label_info['x_mid'], label_info['x_label']], 
                   [label_info['y_mid'], label_info['y_label']], 
                   color='#666666', linewidth=1.0, alpha=0.7)
            
            # 添加标签
            label_text = f"{label_info['channel']}\n{label_info['value']}次 {label_info['pct']:.1f}%"
            
            ax.text(label_info['x_label'], label_info['y_label'], label_text,
                   fontsize=9, ha=label_info['ha'], va='center',
                   bbox=bbox_props, zorder=10)
        
        # 设置图表属性
        ax.set_xlim(-2, 2)
        ax.set_ylim(-1.5, 1.5)
        ax.set_aspect('equal')
        ax.axis('off')
        
        # 添加标题
        plt.title('外贸物流社区 内容流量来源分布\n2025-07-01 至 2025-07-31', 
                 fontsize=14, fontweight='bold', pad=20)
        
        # 保存图表
        output_path = 'test_pie_chart_layout.png'
        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight', 
                   facecolor='white', edgecolor='none')
        
        print(f"✅ 测试饼图已保存到: {output_path}")
        print("📋 布局改进:")
        print("  - 标签分布在四个象限，构图平衡")
        print("  - 连接线从圆环外沿开始，无重叠")
        print("  - 每个象限内标签智能排列")
        print("  - 参考优秀设计，视觉效果更佳")
        
        plt.close()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    
    print("🎨 开始测试图表标签布局...")
    
    # 测试饼图布局
    pie_ok = test_pie_chart_layout()
    
    # 总结
    print(f"\n{'='*50}")
    print("🎨 图表布局测试结果:")
    print(f"饼图标签布局: {'✅ 成功' if pie_ok else '❌ 失败'}")
    
    if pie_ok:
        print("\n🎉 所有测试通过！")
        print("新的标签布局算法已经实现：")
        print("1. 四象限分布：标签分布在四周，构图平衡")
        print("2. 连接线优化：从圆环外沿开始，避免重叠")
        print("3. 智能排列：每个象限内标签自动调整位置")
        print("4. 视觉平衡：参考优秀设计，整体效果更佳")
        return 0
    else:
        print("\n⚠️  测试失败，请检查代码")
        return 1

if __name__ == "__main__":
    exit(main())
