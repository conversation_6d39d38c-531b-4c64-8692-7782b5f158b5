#!/usr/bin/env python3
"""
测试容器网络连接的脚本
"""

import subprocess
import time
import requests

def test_container_status():
    """检查容器状态"""
    
    print("=== 检查容器状态 ===")
    
    try:
        result = subprocess.run(['docker', 'compose', '-f', 'docker-compose.dev.yml', 'ps'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("📊 容器状态:")
            print(result.stdout)
            
            # 检查是否有容器在运行
            if "Up" in result.stdout:
                print("✅ 有容器正在运行")
                return True
            else:
                print("❌ 没有容器在运行")
                return False
        else:
            print("❌ 无法获取容器状态")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 检查容器状态时发生错误: {e}")
        return False

def test_backend_direct():
    """直接测试后端容器"""
    
    print("\n=== 直接测试后端容器 ===")
    
    try:
        response = requests.get("http://localhost:8000/", timeout=10)
        if response.status_code == 200:
            print("✅ 后端容器直接访问成功")
            print(f"   响应: {response.json()}")
            return True
        else:
            print(f"⚠️  后端容器响应状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 后端容器直接访问失败: {e}")
        return False

def test_frontend_direct():
    """直接测试前端容器"""
    
    print("\n=== 直接测试前端容器 ===")
    
    try:
        response = requests.get("http://localhost:3000/", timeout=10)
        if response.status_code == 200:
            print("✅ 前端容器直接访问成功")
            return True
        else:
            print(f"⚠️  前端容器响应状态码: {response.status_code}")
            if "Invalid Host header" in response.text:
                print("❌ 仍然存在Host header问题")
            return False
    except Exception as e:
        print(f"❌ 前端容器直接访问失败: {e}")
        return False

def test_container_network():
    """测试容器间网络连接"""
    
    print("\n=== 测试容器间网络连接 ===")
    
    try:
        # 从前端容器测试访问后端容器
        print("🔍 从前端容器访问后端容器...")
        result = subprocess.run([
            'docker', 'compose', '-f', 'docker-compose.dev.yml', 'exec', '-T', 'frontend',
            'wget', '-q', '--spider', 'http://backend:8000/'
        ], capture_output=True, text=True, timeout=15)
        
        if result.returncode == 0:
            print("✅ 前端容器可以访问后端容器")
            return True
        else:
            print("❌ 前端容器无法访问后端容器")
            print(f"错误: {result.stderr}")
            
            # 尝试使用curl
            print("🔍 尝试使用curl...")
            curl_result = subprocess.run([
                'docker', 'compose', '-f', 'docker-compose.dev.yml', 'exec', '-T', 'frontend',
                'curl', '-f', 'http://backend:8000/'
            ], capture_output=True, text=True, timeout=15)
            
            if curl_result.returncode == 0:
                print("✅ 使用curl访问成功")
                return True
            else:
                print(f"❌ curl也失败: {curl_result.stderr}")
                return False
            
    except Exception as e:
        print(f"❌ 测试容器网络时发生错误: {e}")
        return False

def test_proxy_logs():
    """检查代理日志"""
    
    print("\n=== 检查前端代理日志 ===")
    
    try:
        # 获取前端容器日志
        result = subprocess.run([
            'docker', 'compose', '-f', 'docker-compose.dev.yml', 'logs', '--tail', '20', 'frontend'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("📝 前端容器日志:")
            print(result.stdout)
            
            if "Proxy Error" in result.stdout:
                print("⚠️  发现代理错误")
            if "setupProxy" in result.stdout:
                print("✅ setupProxy.js 被加载")
            
            return True
        else:
            print("❌ 无法获取前端日志")
            return False
            
    except Exception as e:
        print(f"❌ 检查日志时发生错误: {e}")
        return False

def test_api_endpoint():
    """测试具体的API端点"""
    
    print("\n=== 测试API端点 ===")
    
    # 测试不同的访问方式
    test_urls = [
        ("后端直接", "http://localhost:8000/api/auth/login"),
        ("前端代理(localhost)", "http://localhost:3000/api/auth/login"),
    ]
    
    for name, url in test_urls:
        try:
            print(f"\n🔍 测试 {name}: {url}")
            
            # 发送POST请求（模拟登录）
            response = requests.post(url, 
                                   json={"username": "test", "password": "test"}, 
                                   timeout=10)
            
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 404:
                print("❌ 404 - API端点未找到")
            elif response.status_code == 422:
                print("✅ 422 - API端点存在（验证错误是正常的）")
            elif response.status_code == 401:
                print("✅ 401 - API端点存在（认证错误是正常的）")
            else:
                print(f"⚠️  其他状态码: {response.status_code}")
                
        except requests.exceptions.ConnectionError:
            print(f"❌ {name} - 连接失败")
        except Exception as e:
            print(f"❌ {name} - 错误: {e}")

def main():
    """主函数"""
    
    print("🔧 开始测试容器网络连接...")
    
    # 等待容器启动
    print("⏳ 等待5秒让容器完全启动...")
    time.sleep(5)
    
    # 执行测试
    results = []
    results.append(test_container_status())
    results.append(test_backend_direct())
    results.append(test_frontend_direct())
    results.append(test_container_network())
    results.append(test_proxy_logs())
    
    # 测试API端点
    test_api_endpoint()
    
    # 总结
    print(f"\n{'='*60}")
    print("🔧 网络连接测试结果:")
    print(f"容器状态: {'✅ 正常' if results[0] else '❌ 异常'}")
    print(f"后端直接访问: {'✅ 正常' if results[1] else '❌ 异常'}")
    print(f"前端直接访问: {'✅ 正常' if results[2] else '❌ 异常'}")
    print(f"容器间网络: {'✅ 正常' if results[3] else '❌ 异常'}")
    print(f"代理日志: {'✅ 正常' if results[4] else '❌ 异常'}")
    
    if all(results):
        print("\n🎉 所有网络连接测试通过！")
        print("如果API仍然返回404，可能是路由配置问题")
        return 0
    else:
        print("\n⚠️  部分网络连接测试失败")
        print("\n🔧 故障排除建议:")
        print("1. 重启开发环境: docker compose -f docker-compose.dev.yml down && ./scripts/dev.sh")
        print("2. 检查容器日志: docker compose -f docker-compose.dev.yml logs -f")
        print("3. 检查网络: docker network ls")
        return 1

if __name__ == "__main__":
    exit(main())
