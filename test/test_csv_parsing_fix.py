#!/usr/bin/env python3
"""
测试CSV文件解析修复
"""

import sys
import os
import pandas as pd
import io

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import SessionLocal
from app.models import WeChatChannelsVideoData, PlatformAccount
from app.services.data_details_service import DataDetailsService


def test_csv_parsing():
    """测试CSV文件解析"""
    print("=== 测试CSV文件解析 ===")
    
    try:
        # 创建一个模拟的视频号CSV文件
        csv_content = """视频描述,视频ID,发布时间,完播率,平均播放时长,播放量,推荐,喜欢,评论量,分享量,关注量,转发聊天和朋友圈,设为铃声,设为状态,设为朋友圈封面
测试CSV视频1,csv_video_001,2025-01-15 10:00:00,85.5%,120,1000,500,100,20,30,10,15,5,8,2
测试CSV视频2,csv_video_002,2025-01-15 15:30:00,92.3%,180,2500,1200,250,45,60,25,35,10,15,5
测试CSV视频3,csv_video_003,2025-01-15 20:45:00,78.9%,95,800,400,80,15,25,8,12,3,6,1"""
        
        # 转换为字节数据
        csv_bytes = csv_content.encode('utf-8-sig')  # 使用BOM编码
        
        print(f"✅ 创建测试CSV文件成功，大小: {len(csv_bytes)} bytes")
        
        # 测试解析CSV文件
        db = SessionLocal()
        try:
            # 查找测试账号
            test_account = db.query(PlatformAccount).filter(
                PlatformAccount.platform == 'wechat_channels'
            ).first()
            
            if not test_account:
                print("❌ 没有找到测试账号")
                return False
            
            print(f"使用测试账号: {test_account.name} (ID: {test_account.id})")
            
            # 测试数据导入（这会触发CSV解析）
            result = DataDetailsService.import_excel_data(
                db=db,
                account_id=test_account.id,
                data_type='single_video',
                excel_content=csv_bytes
            )
            
            if result["success"]:
                print(f"✅ CSV解析和数据导入成功:")
                print(f"  新增记录: {result['imported_count']}")
                print(f"  更新记录: {result['updated_count']}")
                print(f"  总处理数: {result['total_processed']}")
                
                # 验证数据是否正确导入
                imported_records = db.query(WeChatChannelsVideoData).filter(
                    WeChatChannelsVideoData.account_id == test_account.id,
                    WeChatChannelsVideoData.video_id.in_(['csv_video_001', 'csv_video_002', 'csv_video_003'])
                ).all()
                
                if len(imported_records) >= 3:
                    print(f"✅ 数据验证成功，找到 {len(imported_records)} 条测试记录")
                    for record in imported_records:
                        print(f"  - {record.video_id}: {record.video_description}")
                        print(f"    播放量: {record.play_count}, 喜欢: {record.like_count}")
                else:
                    print(f"❌ 数据验证失败，只找到 {len(imported_records)} 条记录")
                    return False
                
                return True
            else:
                print(f"❌ CSV解析失败: {result['error']}")
                return False
                
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ CSV解析测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_csv_detection():
    """测试CSV文件检测逻辑"""
    print("\n=== 测试CSV文件检测逻辑 ===")
    
    try:
        # 测试CSV内容检测
        csv_content = "列1,列2,列3\n值1,值2,值3\n值4,值5,值6"
        csv_bytes = csv_content.encode('utf-8')
        
        # 尝试解码和检测
        try:
            content_str = csv_bytes.decode('utf-8-sig')
            is_csv = ',' in content_str and '\n' in content_str
            print(f"✅ CSV检测逻辑正确: {is_csv}")
            
            if is_csv:
                # 尝试解析
                df = pd.read_csv(io.StringIO(content_str))
                print(f"✅ CSV解析成功，形状: {df.shape}")
                print(f"  列名: {list(df.columns)}")
                return True
            else:
                print("❌ CSV检测失败")
                return False
                
        except Exception as e:
            print(f"❌ CSV检测异常: {e}")
            return False
            
    except Exception as e:
        print(f"❌ CSV检测测试失败: {e}")
        return False


def test_excel_fallback():
    """测试Excel回退机制"""
    print("\n=== 测试Excel回退机制 ===")
    
    try:
        # 创建一个真正的Excel文件
        test_data = {
            '视频描述': ['Excel测试视频1', 'Excel测试视频2'],
            '视频ID': ['excel_video_001', 'excel_video_002'],
            '发布时间': ['2025-01-15 10:00:00', '2025-01-15 15:30:00'],
            '完播率': ['88.5%', '91.2%'],
            '平均播放时长': [150, 200],
            '播放量': [1500, 2800],
            '推荐': [750, 1400],
            '喜欢': [150, 280],
            '评论量': [30, 56],
            '分享量': [45, 84],
            '关注量': [15, 28],
            '转发聊天和朋友圈': [22, 42],
            '设为铃声': [8, 14],
            '设为状态': [12, 21],
            '设为朋友圈封面': [3, 7]
        }
        
        # 创建DataFrame并转换为Excel字节数据
        df = pd.DataFrame(test_data)
        excel_buffer = io.BytesIO()
        df.to_excel(excel_buffer, index=False, engine='openpyxl')
        excel_content = excel_buffer.getvalue()
        
        print(f"✅ 创建测试Excel文件成功，大小: {len(excel_content)} bytes")
        
        # 测试解析Excel文件（应该回退到Excel解析器）
        db = SessionLocal()
        try:
            test_account = db.query(PlatformAccount).filter(
                PlatformAccount.platform == 'wechat_channels'
            ).first()
            
            if not test_account:
                print("❌ 没有找到测试账号")
                return False
            
            # 测试数据导入
            result = DataDetailsService.import_excel_data(
                db=db,
                account_id=test_account.id,
                data_type='single_video',
                excel_content=excel_content
            )
            
            if result["success"]:
                print(f"✅ Excel回退机制工作正常:")
                print(f"  新增记录: {result['imported_count']}")
                print(f"  更新记录: {result['updated_count']}")
                return True
            else:
                print(f"❌ Excel回退失败: {result['error']}")
                return False
                
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ Excel回退测试失败: {e}")
        return False


def test_mixed_file_handling():
    """测试混合文件处理"""
    print("\n=== 测试混合文件处理 ===")
    
    try:
        # 测试无效文件
        invalid_content = b"This is neither CSV nor Excel"
        
        db = SessionLocal()
        try:
            test_account = db.query(PlatformAccount).filter(
                PlatformAccount.platform == 'wechat_channels'
            ).first()
            
            if not test_account:
                print("❌ 没有找到测试账号")
                return False
            
            # 尝试导入无效文件
            result = DataDetailsService.import_excel_data(
                db=db,
                account_id=test_account.id,
                data_type='single_video',
                excel_content=invalid_content
            )
            
            if not result["success"]:
                print(f"✅ 正确处理了无效文件，错误信息: {result['error']}")
                return True
            else:
                print("❌ 应该失败但却成功了")
                return False
                
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ 混合文件处理测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试CSV文件解析修复")
    
    tests = [
        ("CSV文件解析", test_csv_parsing),
        ("CSV文件检测逻辑", test_csv_detection),
        ("Excel回退机制", test_excel_fallback),
        ("混合文件处理", test_mixed_file_handling)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        try:
            if test_func():
                print(f"✅ {test_name} - 通过")
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n{'='*50}")
    print(f"CSV解析修复测试总结: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有CSV解析修复测试通过！")
        print("📋 修复内容:")
        print("  ✅ 支持CSV文件自动检测和解析")
        print("  ✅ Excel文件回退机制正常")
        print("  ✅ 错误处理完善")
        print("  ✅ 数据导入功能正常")
        print("\n🚀 视频号CSV文件解析问题已修复！")
        return True
    else:
        print("⚠️  部分CSV解析修复测试失败，请检查相关功能。")
        return False


if __name__ == "__main__":
    main()
