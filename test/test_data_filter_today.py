#!/usr/bin/env python3
"""
测试数据过滤当天逻辑
"""

import os
import re

def test_pie_data_filter():
    """测试饼图数据过滤逻辑"""
    print("=== 测试饼图数据过滤逻辑 ===")
    
    try:
        page_file = "frontend/src/pages/DataDetails.tsx"
        
        if os.path.exists(page_file):
            print(f"✅ DataDetails页面文件存在")
            
            with open(page_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # 检查饼图数据过滤逻辑
                pie_filter_checks = [
                    ("获取今天日期", "const today = new Date().toISOString().split('T')[0]"),
                    ("过滤当天数据", "accountSummary.date_columns.filter"),
                    ("过滤条件", "dateCol !== today"),
                    ("检查可用日期", "if (availableDates.length === 0) return []"),
                    ("使用过滤后的日期", "const latestDateCol = availableDates[0]"),
                    ("类型注解", "(dateCol: string)")
                ]
                
                all_passed = True
                for check_name, check_content in pie_filter_checks:
                    if check_content in content:
                        print(f"✅ 饼图 - {check_name}: 存在")
                    else:
                        print(f"❌ 饼图 - {check_name}: 缺失")
                        all_passed = False
                
                return all_passed
        else:
            print(f"❌ DataDetails页面文件不存在")
            return False
        
    except Exception as e:
        print(f"❌ 测试饼图数据过滤逻辑失败: {e}")
        return False


def test_line_data_filter():
    """测试折线图数据过滤逻辑"""
    print("\n=== 测试折线图数据过滤逻辑 ===")
    
    try:
        page_file = "frontend/src/pages/DataDetails.tsx"
        
        with open(page_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
            # 检查折线图数据过滤逻辑
            line_filter_checks = [
                ("获取今天日期", "const today = new Date().toISOString().split('T')[0]"),
                ("过滤当天数据", "accountSummary.date_columns.filter"),
                ("过滤条件", "dateCol !== today"),
                ("使用过滤后的日期", "availableDates.forEach"),
                ("类型注解", "(dateCol: string)")
            ]
            
            all_passed = True
            for check_name, check_content in line_filter_checks:
                if check_content in content:
                    print(f"✅ 折线图 - {check_name}: 存在")
                else:
                    print(f"❌ 折线图 - {check_name}: 缺失")
                    all_passed = False
            
            return all_passed
        
    except Exception as e:
        print(f"❌ 测试折线图数据过滤逻辑失败: {e}")
        return False


def test_filter_logic_placement():
    """测试过滤逻辑位置"""
    print("\n=== 测试过滤逻辑位置 ===")
    
    try:
        page_file = "frontend/src/pages/DataDetails.tsx"
        
        with open(page_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
            # 检查过滤逻辑在正确的函数中
            placement_checks = [
                ("preparePieData函数中的过滤", "preparePieData.*today.*availableDates"),
                ("prepareLineData函数中的过滤", "prepareLineData.*today.*availableDates")
            ]
            
            all_passed = True
            for check_name, pattern in placement_checks:
                if re.search(pattern, content, re.DOTALL):
                    print(f"✅ {check_name}: 位置正确")
                else:
                    print(f"❌ {check_name}: 位置错误")
                    all_passed = False
            
            return all_passed
        
    except Exception as e:
        print(f"❌ 测试过滤逻辑位置失败: {e}")
        return False


def test_date_format_consistency():
    """测试日期格式一致性"""
    print("\n=== 测试日期格式一致性 ===")
    
    try:
        page_file = "frontend/src/pages/DataDetails.tsx"
        
        with open(page_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
            # 检查日期格式处理
            format_checks = [
                ("ISO日期格式", "toISOString().split('T')[0]"),
                ("YYYY-MM-DD格式注释", "// 获取今天的日期 YYYY-MM-DD"),
                ("日期比较逻辑", "dateCol !== today")
            ]
            
            all_passed = True
            for check_name, check_content in format_checks:
                if check_content in content:
                    print(f"✅ {check_name}: 正确")
                else:
                    print(f"❌ {check_name}: 错误")
                    all_passed = False
            
            return all_passed
        
    except Exception as e:
        print(f"❌ 测试日期格式一致性失败: {e}")
        return False


def test_edge_cases():
    """测试边界情况处理"""
    print("\n=== 测试边界情况处理 ===")
    
    try:
        page_file = "frontend/src/pages/DataDetails.tsx"
        
        with open(page_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
            # 检查边界情况处理
            edge_case_checks = [
                ("空数据检查", "if (!accountSummary?.data || !accountSummary?.date_columns) return []"),
                ("可用日期为空检查", "if (availableDates.length === 0) return []"),
                ("数据过滤", "filterDataByPlatform"),
                ("空值处理", "account.account_name === 'null'")
            ]
            
            all_passed = True
            for check_name, check_content in edge_case_checks:
                if check_content in content:
                    print(f"✅ {check_name}: 存在")
                else:
                    print(f"❌ {check_name}: 缺失")
                    all_passed = False
            
            return all_passed
        
    except Exception as e:
        print(f"❌ 测试边界情况处理失败: {e}")
        return False


def test_function_structure():
    """测试函数结构完整性"""
    print("\n=== 测试函数结构完整性 ===")
    
    try:
        page_file = "frontend/src/pages/DataDetails.tsx"
        
        with open(page_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
            # 检查函数结构
            structure_checks = [
                ("preparePieData函数", "const preparePieData = () => {"),
                ("prepareLineData函数", "const prepareLineData = () => {"),
                ("饼图数据映射", "const pieData = filteredData.map"),
                ("折线图数据循环", "filteredData.forEach(account =>"),
                ("数据排序", "lineData.sort"),
                ("控制台日志", "console.log")
            ]
            
            all_passed = True
            for check_name, check_content in structure_checks:
                if check_content in content:
                    print(f"✅ {check_name}: 存在")
                else:
                    print(f"❌ {check_name}: 缺失")
                    all_passed = False
            
            return all_passed
        
    except Exception as e:
        print(f"❌ 测试函数结构完整性失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 测试数据过滤当天逻辑")
    
    tests = [
        ("饼图数据过滤逻辑", test_pie_data_filter),
        ("折线图数据过滤逻辑", test_line_data_filter),
        ("过滤逻辑位置", test_filter_logic_placement),
        ("日期格式一致性", test_date_format_consistency),
        ("边界情况处理", test_edge_cases),
        ("函数结构完整性", test_function_structure)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            if result:
                print(f"✅ {test_name} - 通过")
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"数据过滤当天逻辑测试总结: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 数据过滤当天逻辑修改完成！")
        print("\n📋 修改总结:")
        print("  ✅ 饼图数据过滤当天数据")
        print("  ✅ 折线图数据过滤当天数据")
        print("  ✅ 日期格式处理正确")
        print("  ✅ 边界情况处理完善")
        print("  ✅ 函数结构保持完整")
        print("\n🎯 过滤逻辑:")
        print("  📅 获取当前日期 (YYYY-MM-DD格式)")
        print("  🔍 过滤掉与当前日期相同的数据列")
        print("  📊 使用过滤后的最新日期作为数据源")
        print("  ⚠️  处理无可用日期的边界情况")
        print("\n💡 业务逻辑:")
        print("  - 周五的数据要在周六才能生成完整")
        print("  - 当天的数据可能不完整，需要过滤")
        print("  - 确保图表显示的是完整可靠的数据")
        print("  - 避免显示不准确的当天数据")
        print("\n🔧 技术实现:")
        print("  - 使用 new Date().toISOString().split('T')[0] 获取当前日期")
        print("  - 使用 filter 方法过滤日期列")
        print("  - 保持原有的数据处理逻辑")
        print("  - 添加 TypeScript 类型注解")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return False


if __name__ == "__main__":
    main()
