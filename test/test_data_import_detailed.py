#!/usr/bin/env python3
"""
详细测试数据导入过程
"""

import sys
import os
import pandas as pd
from io import BytesIO

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import SessionLocal
from app.services.data_details_service import DataDetailsService


def create_test_excel_content_trend():
    """创建测试的内容数据趋势明细Excel内容"""
    print("=== 创建测试内容数据趋势明细Excel ===")
    
    # 创建测试数据
    test_data = {
        '日期': ['2025-08-01', '2025-08-02', '2025-08-03'],
        '阅读次数': [1000, 1200, 800],
        '阅读人数': [800, 900, 600],
        '分享次数': [50, 60, 40],
        '分享人数': [45, 55, 35],
        '阅读原文次数': [20, 25, 15],
        '阅读原文人数': [18, 22, 13],
        '收藏次数': [30, 35, 25],
        '收藏人数': [28, 32, 23],
        '群发篇数': [1, 1, 1],
        '渠道': ['公众号', '公众号', '公众号']
    }
    
    df = pd.DataFrame(test_data)
    
    # 转换为Excel字节内容
    buffer = BytesIO()
    df.to_excel(buffer, index=False, engine='openpyxl')
    excel_content = buffer.getvalue()
    buffer.close()
    
    print(f"✅ 创建了包含 {len(test_data['日期'])} 行数据的Excel文件")
    print(f"   文件大小: {len(excel_content)} bytes")
    
    return excel_content


def create_test_excel_content_source():
    """创建测试的内容流量来源明细Excel内容"""
    print("\n=== 创建测试内容流量来源明细Excel ===")
    
    # 创建测试数据
    test_data = {
        '传播渠道': ['会话', '朋友圈', '历史消息'],
        '内容标题': ['测试文章1', '测试文章2', '测试文章3'],
        '发表日期': ['2025-08-01', '2025-08-02', '2025-08-03'],
        '阅读次数': [500, 300, 200],
        '阅读次数占比': ['50%', '30%', '20%']
    }
    
    df = pd.DataFrame(test_data)
    
    # 转换为Excel字节内容
    buffer = BytesIO()
    df.to_excel(buffer, index=False, engine='openpyxl')
    excel_content = buffer.getvalue()
    buffer.close()
    
    print(f"✅ 创建了包含 {len(test_data['传播渠道'])} 行数据的Excel文件")
    print(f"   文件大小: {len(excel_content)} bytes")
    
    return excel_content


def create_test_excel_content_detail():
    """创建测试的内容已通知内容明细Excel内容"""
    print("\n=== 创建测试内容已通知内容明细Excel ===")
    
    # 创建测试数据
    test_data = {
        '内容标题': ['测试推送1', '测试推送2', '测试推送3'],
        '发表时间': ['2025-08-01 10:00:00', '2025-08-02 15:30:00', '2025-08-03 09:15:00'],
        '送达人数': [1000, 1200, 800],
        '阅读人数': [600, 700, 450],
        '阅读次数': [800, 900, 600],
        '分享人数': [50, 60, 40],
        '分享次数': [55, 65, 45],
        '收藏人数': [30, 35, 25],
        '在看人数': [20, 25, 15]
    }
    
    df = pd.DataFrame(test_data)
    
    # 转换为Excel字节内容
    buffer = BytesIO()
    df.to_excel(buffer, index=False, engine='openpyxl')
    excel_content = buffer.getvalue()
    buffer.close()
    
    print(f"✅ 创建了包含 {len(test_data['内容标题'])} 行数据的Excel文件")
    print(f"   文件大小: {len(excel_content)} bytes")
    
    return excel_content


def test_data_import(data_type: str, excel_content: bytes, account_id: int = 10):
    """测试数据导入"""
    print(f"\n=== 测试 {data_type} 数据导入 ===")
    
    db = SessionLocal()
    try:
        # 调用数据导入服务
        result = DataDetailsService.import_excel_data(
            db=db,
            account_id=account_id,
            data_type=data_type,
            excel_content=excel_content
        )
        
        print(f"导入结果: {result}")
        
        if result["success"]:
            print(f"✅ 数据导入成功")
            print(f"   新增记录: {result['imported_count']}")
            print(f"   更新记录: {result['updated_count']}")
            
            # 提交事务
            db.commit()
            return True
        else:
            print(f"❌ 数据导入失败: {result['error']}")
            db.rollback()
            return False
        
    except Exception as e:
        print(f"❌ 数据导入异常: {e}")
        import traceback
        traceback.print_exc()
        db.rollback()
        return False
    finally:
        db.close()


def verify_imported_data():
    """验证导入的数据"""
    print("\n=== 验证导入的数据 ===")
    
    db = SessionLocal()
    try:
        from app.models import WeChatMPContentTrend, WeChatMPContentSource, WeChatMPContentDetail
        from sqlalchemy import func
        
        # 检查各个表的记录数
        tables = [
            ("内容数据趋势明细", WeChatMPContentTrend),
            ("内容流量来源明细", WeChatMPContentSource),
            ("内容已通知内容明细", WeChatMPContentDetail)
        ]
        
        for table_name, model_class in tables:
            count = db.query(func.count(model_class.id)).scalar()
            print(f"  {table_name}: {count} 条记录")
            
            if count > 0:
                # 显示最新的记录
                latest_record = db.query(model_class).order_by(model_class.id.desc()).first()
                print(f"    最新记录ID: {latest_record.id}, 账号ID: {latest_record.account_id}")
                
                # 显示记录的一些关键字段
                if hasattr(latest_record, 'date'):
                    print(f"    日期: {latest_record.date}")
                if hasattr(latest_record, 'title'):
                    print(f"    标题: {getattr(latest_record, 'title', 'N/A')[:50]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证数据失败: {e}")
        return False
    finally:
        db.close()


def test_excel_parsing():
    """测试Excel解析"""
    print("\n=== 测试Excel解析 ===")
    
    try:
        # 测试内容数据趋势明细
        excel_content = create_test_excel_content_trend()
        
        # 直接解析Excel内容
        df = pd.read_excel(BytesIO(excel_content), engine='openpyxl')
        print(f"✅ Excel解析成功")
        print(f"   数据形状: {df.shape}")
        print(f"   列名: {list(df.columns)}")
        print(f"   前3行数据:")
        print(df.head(3))
        
        # 测试日期解析
        for i, row in df.iterrows():
            date_value = DataDetailsService._parse_date(row.get('日期'))
            print(f"   行 {i}: 日期 '{row.get('日期')}' -> {date_value}")
            if i >= 2:  # 只显示前3行
                break
        
        return True
        
    except Exception as e:
        print(f"❌ Excel解析失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🧪 详细测试数据导入过程")
    
    # 测试Excel解析
    if not test_excel_parsing():
        print("❌ Excel解析测试失败，停止后续测试")
        return False
    
    # 创建测试数据
    test_cases = [
        ("content_trend", create_test_excel_content_trend()),
        ("content_source", create_test_excel_content_source()),
        ("content_detail", create_test_excel_content_detail())
    ]
    
    success_count = 0
    
    for data_type, excel_content in test_cases:
        if test_data_import(data_type, excel_content):
            success_count += 1
    
    # 验证导入的数据
    verify_imported_data()
    
    print(f"\n{'='*50}")
    print(f"测试结果: {success_count}/{len(test_cases)} 个数据类型导入成功")
    print('='*50)
    
    if success_count == len(test_cases):
        print("🎉 所有数据类型导入测试通过！")
        print("\n💡 如果实际使用中仍然没有数据，可能的原因:")
        print("1. Excel文件格式与预期不符")
        print("2. Excel文件中的列名与代码中的不匹配")
        print("3. 数据导入过程中发生异常但未正确处理")
        print("4. 事务未正确提交")
        print("\n🔧 建议:")
        print("- 检查实际下载的Excel文件内容和格式")
        print("- 在数据导入方法中添加更详细的日志")
        print("- 确保数据库事务正确提交")
    else:
        print("⚠️  部分数据类型导入失败")
        print("\n🔧 需要检查:")
        print("- 数据库连接是否正常")
        print("- 数据表结构是否正确")
        print("- 数据解析逻辑是否有问题")
    
    return success_count == len(test_cases)


if __name__ == "__main__":
    main()
