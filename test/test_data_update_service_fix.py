#!/usr/bin/env python3
"""
测试数据更新服务的视频号支持修复
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import SessionLocal
from app.models import PlatformAccount
from app.services.data_update_service import DataUpdateService
from app.services.wechat_channels_service import WeChatChannelsService


def test_platform_service_selection():
    """测试平台服务选择逻辑"""
    print("=== 测试平台服务选择逻辑 ===")
    
    db = SessionLocal()
    try:
        # 查找视频号账号
        channels_account = db.query(PlatformAccount).filter(
            PlatformAccount.platform == 'wechat_channels'
        ).first()
        
        if not channels_account:
            print("❌ 没有找到视频号账号")
            return False
        
        print(f"找到视频号账号: {channels_account.name} (ID: {channels_account.id})")
        
        # 测试服务实例化逻辑
        if channels_account.platform == "wechat_channels":
            service = WeChatChannelsService(account_id=channels_account.id)
            print(f"✅ 正确创建了WeChatChannelsService实例")
            
            # 检查服务是否有必要的方法
            if hasattr(service, 'load_login_state'):
                print("✅ 服务有load_login_state方法")
            else:
                print("❌ 服务缺少load_login_state方法")
                return False
                
            if hasattr(service, 'download_single_video_data'):
                print("✅ 服务有download_single_video_data方法")
            else:
                print("❌ 服务缺少download_single_video_data方法")
                return False
        else:
            print(f"❌ 账号平台类型错误: {channels_account.platform}")
            return False
        
        return True
        
    finally:
        db.close()


def test_login_state_file_path():
    """测试登录状态文件路径"""
    print("\n=== 测试登录状态文件路径 ===")
    
    db = SessionLocal()
    try:
        channels_account = db.query(PlatformAccount).filter(
            PlatformAccount.platform == 'wechat_channels'
        ).first()
        
        if not channels_account:
            print("❌ 没有找到视频号账号")
            return False
        
        # 创建服务实例
        service = WeChatChannelsService(account_id=channels_account.id)
        
        # 检查用户目录路径
        user_dir = service._get_user_data_dir()
        print(f"用户数据目录: {user_dir}")
        
        # 检查路径是否正确
        expected_pattern = f"wechat_channels_account_{channels_account.id}"
        if expected_pattern in user_dir:
            print(f"✅ 用户目录路径正确，包含: {expected_pattern}")
        else:
            print(f"❌ 用户目录路径错误，应包含: {expected_pattern}")
            return False
        
        # 检查登录状态文件路径
        login_state_file = os.path.join(user_dir, "login_state.json")
        print(f"登录状态文件路径: {login_state_file}")
        
        # 检查目录是否存在
        if os.path.exists(user_dir):
            print(f"✅ 用户目录存在")
            
            if os.path.exists(login_state_file):
                print(f"✅ 登录状态文件存在")
            else:
                print(f"ℹ️  登录状态文件不存在（这是正常的，如果账号未登录）")
        else:
            print(f"ℹ️  用户目录不存在（这是正常的，如果账号未使用过）")
        
        return True
        
    finally:
        db.close()


def test_data_types_configuration():
    """测试数据类型配置"""
    print("\n=== 测试数据类型配置 ===")
    
    try:
        # 检查微信公众号数据类型
        mp_types = DataUpdateService.WECHAT_MP_DATA_TYPES
        print(f"微信公众号数据类型: {mp_types}")
        
        expected_mp_types = ['content_trend', 'content_source', 'content_detail', 'user_channel']
        if mp_types == expected_mp_types:
            print("✅ 微信公众号数据类型配置正确")
        else:
            print(f"❌ 微信公众号数据类型配置错误，期望: {expected_mp_types}")
            return False
        
        # 检查视频号数据类型
        channels_types = DataUpdateService.WECHAT_CHANNELS_DATA_TYPES
        print(f"视频号数据类型: {channels_types}")
        
        expected_channels_types = ['single_video']
        if channels_types == expected_channels_types:
            print("✅ 视频号数据类型配置正确")
        else:
            print(f"❌ 视频号数据类型配置错误，期望: {expected_channels_types}")
            return False
        
        # 检查兼容性
        legacy_types = DataUpdateService.DATA_TYPES
        if legacy_types == mp_types:
            print("✅ 兼容性配置正确")
        else:
            print(f"❌ 兼容性配置错误")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 数据类型配置测试失败: {e}")
        return False


def test_download_result_format():
    """测试下载结果格式"""
    print("\n=== 测试下载结果格式 ===")
    
    try:
        # 模拟视频号下载成功的结果格式
        mock_success_result = {
            "success": True,
            "message": "视频号数据下载成功",
            "downloaded_files": [{"data_type": "single_video", "filename": "single_video_data.xlsx"}],
            "failed_files": []
        }
        
        # 检查结果格式
        required_keys = ["success", "message", "downloaded_files", "failed_files"]
        missing_keys = [key for key in required_keys if key not in mock_success_result]
        
        if missing_keys:
            print(f"❌ 下载结果格式缺少字段: {missing_keys}")
            return False
        else:
            print("✅ 下载结果格式正确")
        
        # 模拟视频号下载失败的结果格式
        mock_failure_result = {
            "success": False,
            "message": "视频号数据下载失败",
            "downloaded_files": [],
            "failed_files": [{"data_type": "single_video", "error": "下载失败"}]
        }
        
        missing_keys = [key for key in required_keys if key not in mock_failure_result]
        
        if missing_keys:
            print(f"❌ 失败结果格式缺少字段: {missing_keys}")
            return False
        else:
            print("✅ 失败结果格式正确")
        
        return True
        
    except Exception as e:
        print(f"❌ 下载结果格式测试失败: {e}")
        return False


def test_service_method_compatibility():
    """测试服务方法兼容性"""
    print("\n=== 测试服务方法兼容性 ===")
    
    try:
        # 检查WeChatChannelsService的方法
        from app.services.wechat_channels_service import WeChatChannelsService
        
        # 创建一个测试实例（不需要真实的account_id）
        service = WeChatChannelsService(account_id=999)
        
        # 检查必要的方法
        required_methods = [
            'load_login_state',
            'download_single_video_data',
            '_get_user_data_dir'
        ]
        
        missing_methods = []
        for method in required_methods:
            if not hasattr(service, method):
                missing_methods.append(method)
        
        if missing_methods:
            print(f"❌ WeChatChannelsService缺少方法: {missing_methods}")
            return False
        else:
            print("✅ WeChatChannelsService所有必要方法都存在")
        
        # 检查方法签名
        import inspect
        
        # 检查download_single_video_data方法签名
        sig = inspect.signature(service.download_single_video_data)
        params = list(sig.parameters.keys())
        expected_params = ['start_date', 'end_date', 'auto_import']
        
        if all(param in params for param in expected_params):
            print("✅ download_single_video_data方法签名正确")
        else:
            print(f"❌ download_single_video_data方法签名错误，期望参数: {expected_params}, 实际: {params}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 服务方法兼容性测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试数据更新服务的视频号支持修复")
    
    tests = [
        ("平台服务选择逻辑", test_platform_service_selection),
        ("登录状态文件路径", test_login_state_file_path),
        ("数据类型配置", test_data_types_configuration),
        ("下载结果格式", test_download_result_format),
        ("服务方法兼容性", test_service_method_compatibility)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        try:
            if test_func():
                print(f"✅ {test_name} - 通过")
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n{'='*50}")
    print(f"修复测试总结: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有修复测试通过！")
        print("📋 修复内容:")
        print("  ✅ 平台服务选择逻辑正确")
        print("  ✅ 登录状态文件路径正确")
        print("  ✅ 数据类型配置正确")
        print("  ✅ 下载结果格式统一")
        print("  ✅ 服务方法兼容性良好")
        print("\n🚀 视频号数据更新功能已修复！")
        return True
    else:
        print("⚠️  部分修复测试失败，请检查相关功能。")
        return False


if __name__ == "__main__":
    main()
