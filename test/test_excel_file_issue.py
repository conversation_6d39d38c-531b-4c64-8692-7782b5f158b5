#!/usr/bin/env python3
"""
测试Excel文件下载和解析问题
"""

import sys
import os
import pandas as pd
from io import BytesIO

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def analyze_file_content(file_content: bytes, description: str = "文件"):
    """分析文件内容"""
    print(f"\n=== 分析{description}内容 ===")
    
    if not file_content:
        print("❌ 文件内容为空")
        return False
    
    print(f"✅ 文件大小: {len(file_content)} bytes")
    
    # 检查文件头
    if len(file_content) >= 4:
        file_header = file_content[:4]
        print(f"文件头 (hex): {file_header.hex()}")
        print(f"文件头 (bytes): {file_header}")
        
        # 检查是否是ZIP文件（Excel文件实际上是ZIP格式）
        if file_header[:2] == b'PK':
            print("✅ 文件头显示这是一个ZIP/Excel文件")
        else:
            print("❌ 文件头不是ZIP格式，可能不是有效的Excel文件")
    
    # 检查前100个字节的内容
    preview_length = min(100, len(file_content))
    preview = file_content[:preview_length]
    
    print(f"前{preview_length}字节内容:")
    try:
        # 尝试作为文本解码
        text_preview = preview.decode('utf-8', errors='ignore')
        print(f"  文本内容: {repr(text_preview)}")
        
        # 检查是否是HTML错误页面
        if '<html>' in text_preview.lower() or '<!doctype' in text_preview.lower():
            print("❌ 检测到HTML内容，可能是错误页面而不是Excel文件")
            return False
        elif 'error' in text_preview.lower():
            print("❌ 检测到错误信息")
            return False
            
    except Exception as e:
        print(f"  无法解码为文本: {e}")
    
    # 尝试用pandas解析
    try:
        print("尝试用pandas解析Excel...")
        df = pd.read_excel(BytesIO(file_content), engine='openpyxl')
        print(f"✅ Excel解析成功")
        print(f"  数据形状: {df.shape}")
        print(f"  列名: {list(df.columns)}")
        if not df.empty:
            print(f"  前3行数据:")
            print(df.head(3))
        return True
        
    except Exception as e:
        print(f"❌ Excel解析失败: {e}")
        
        # 尝试CSV解析
        try:
            print("尝试用pandas解析CSV...")
            content_str = file_content.decode('utf-8-sig')
            df = pd.read_csv(BytesIO(file_content))
            print(f"✅ CSV解析成功")
            print(f"  数据形状: {df.shape}")
            print(f"  列名: {list(df.columns)}")
            return True
        except Exception as csv_e:
            print(f"❌ CSV解析也失败: {csv_e}")
    
    return False


def test_excel_parsing_with_different_engines():
    """测试不同的Excel解析引擎"""
    print("\n=== 测试不同的Excel解析引擎 ===")
    
    # 创建一个简单的测试Excel文件
    test_data = {
        '日期': ['2025-08-01', '2025-08-02'],
        '阅读次数': [1000, 1200],
        '阅读人数': [800, 900]
    }
    
    df = pd.DataFrame(test_data)
    
    # 测试不同的引擎
    engines = ['openpyxl', 'xlsxwriter']
    
    for engine in engines:
        try:
            print(f"\n测试引擎: {engine}")
            buffer = BytesIO()
            
            if engine == 'xlsxwriter':
                # xlsxwriter只能写，不能读
                df.to_excel(buffer, index=False, engine=engine)
                excel_content = buffer.getvalue()
                buffer.close()
                
                # 用openpyxl读取
                test_df = pd.read_excel(BytesIO(excel_content), engine='openpyxl')
                print(f"✅ {engine} 写入 + openpyxl 读取成功")
                print(f"  数据形状: {test_df.shape}")
            else:
                # openpyxl可以读写
                df.to_excel(buffer, index=False, engine=engine)
                excel_content = buffer.getvalue()
                buffer.close()
                
                test_df = pd.read_excel(BytesIO(excel_content), engine=engine)
                print(f"✅ {engine} 读写成功")
                print(f"  数据形状: {test_df.shape}")
                
        except Exception as e:
            print(f"❌ {engine} 失败: {e}")


def create_debug_version_of_import():
    """创建调试版本的导入方法"""
    print("\n=== 创建调试版本的导入方法 ===")
    
    debug_code = '''
# 在 _import_excel_to_database 方法开始处添加调试代码
async def _import_excel_to_database(self, excel_content: bytes, data_type: str):
    """将Excel数据导入到数据库（调试版本）"""
    try:
        print(f"开始导入数据，数据类型: {data_type}")
        print(f"Excel内容大小: {len(excel_content)} bytes")
        
        # 分析文件内容
        if len(excel_content) >= 4:
            file_header = excel_content[:4]
            print(f"文件头: {file_header.hex()}")
            
            if file_header[:2] != b'PK':
                print("警告: 文件头不是ZIP格式，可能不是有效的Excel文件")
                # 保存文件内容到临时文件进行分析
                import tempfile
                with tempfile.NamedTemporaryFile(mode='wb', suffix='.debug', delete=False) as f:
                    f.write(excel_content)
                    print(f"调试文件已保存到: {f.name}")
        
        # 继续原有的导入逻辑...
        from app.services.data_details_service import DataDetailsService
        from app.database import SessionLocal
        
        db = SessionLocal()
        try:
            result = DataDetailsService.import_excel_data(
                db=db,
                account_id=self.account_id,
                data_type=data_type,
                excel_content=excel_content
            )
            
            if result["success"]:
                db.commit()
                print(f"数据导入成功: 新增 {result['imported_count']} 条，更新 {result['updated_count']} 条")
            else:
                db.rollback()
                print(f"数据导入失败: {result['error']}")
                
        except Exception as e:
            db.rollback()
            print(f"数据导入过程中发生异常: {e}")
            import traceback
            traceback.print_exc()
        finally:
            db.close()
            
    except Exception as e:
        print(f"数据导入过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
'''
    
    print("建议在微信服务中添加以下调试代码:")
    print(debug_code)
    
    return True


def main():
    """主测试函数"""
    print("🔍 测试Excel文件下载和解析问题")
    
    tests = [
        ("不同Excel解析引擎", test_excel_parsing_with_different_engines),
        ("调试版本导入方法", create_debug_version_of_import)
    ]
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        try:
            test_func()
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
    
    print(f"\n{'='*50}")
    print("Excel文件问题诊断建议")
    print('='*50)
    
    print("\n🔍 可能的问题原因:")
    print("1. 下载的文件不是真正的Excel文件")
    print("2. 服务器返回了HTML错误页面")
    print("3. 文件在传输过程中被截断或损坏")
    print("4. 微信服务器的响应格式发生了变化")
    
    print("\n🔧 调试步骤:")
    print("1. 在_import_excel_to_database方法中添加文件内容分析")
    print("2. 检查下载的文件头是否为'PK'（ZIP格式）")
    print("3. 将下载的内容保存到临时文件进行手动检查")
    print("4. 检查微信公众号的登录状态是否正常")
    print("5. 检查下载URL和参数是否正确")
    
    print("\n💡 临时解决方案:")
    print("- 添加文件格式检查，如果不是Excel格式则记录详细错误信息")
    print("- 在下载失败时尝试重新登录")
    print("- 添加重试机制")
    
    return True


if __name__ == "__main__":
    main()
