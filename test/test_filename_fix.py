#!/usr/bin/env python3
"""
测试文件命名修复
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.data_download_service import DataDownloadService


def test_filename_generation():
    """测试文件名生成逻辑"""
    print("=== 测试文件名生成逻辑 ===")
    
    try:
        # 测试不同平台的文件名生成
        test_cases = [
            {
                "platform": "xiaohongshu",
                "account_name": "赵永明",
                "data_type": "笔记数据",
                "start_date": "2025-08-01",
                "end_date": "2025-08-21",
                "expected_prefix": "xiaohongshu_data"
            },
            {
                "platform": "wechat",
                "account_name": "测试账号",
                "data_type": "粉丝数据",
                "start_date": "2025-08-01",
                "end_date": "2025-08-21",
                "expected_prefix": "wechat_data"
            },
            {
                "platform": "wechat_channels",
                "account_name": "视频号",
                "data_type": "内容数据",
                "start_date": "2025-08-01",
                "end_date": "2025-08-21",
                "expected_prefix": "wechat_channels_data"
            }
        ]
        
        all_passed = True
        for case in test_cases:
            filename = DataDownloadService.generate_filename(
                case["platform"],
                case["account_name"],
                case["data_type"],
                case["start_date"],
                case["end_date"]
            )
            
            if filename.startswith(case["expected_prefix"]):
                print(f"✅ {case['platform']} 平台文件名正确: {filename}")
            else:
                print(f"❌ {case['platform']} 平台文件名错误: {filename}")
                print(f"   期望前缀: {case['expected_prefix']}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 测试文件名生成逻辑失败: {e}")
        return False


def test_xiaohongshu_specific_naming():
    """测试小红书特定命名"""
    print("\n=== 测试小红书特定命名 ===")
    
    try:
        # 测试小红书平台的具体文件名
        filename = DataDownloadService.generate_filename(
            "xiaohongshu",
            "赵永明",
            "笔记数据",
            "2025-08-01",
            "2025-08-21"
        )
        
        expected_filename = "xiaohongshu_data_赵永明+笔记数据+2025-08-01_to_2025-08-21.xlsx"
        
        if filename == expected_filename:
            print(f"✅ 小红书文件名完全正确: {filename}")
            return True
        else:
            print(f"❌ 小红书文件名不正确")
            print(f"   实际: {filename}")
            print(f"   期望: {expected_filename}")
            return False
        
    except Exception as e:
        print(f"❌ 测试小红书特定命名失败: {e}")
        return False


def test_platform_prefix_logic():
    """测试平台前缀逻辑"""
    print("\n=== 测试平台前缀逻辑 ===")
    
    try:
        service_file = "app/services/data_download_service.py"
        
        if os.path.exists(service_file):
            print(f"✅ 数据下载服务文件存在")
            
            with open(service_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # 检查平台前缀逻辑
                prefix_checks = [
                    ("微信视频号前缀", 'if platform == "wechat_channels":'),
                    ("微信视频号前缀设置", 'prefix = "wechat_channels_data"'),
                    ("微信公众号前缀", 'elif platform == "wechat":'),
                    ("微信公众号前缀设置", 'prefix = "wechat_data"'),
                    ("小红书前缀", 'elif platform == "xiaohongshu":'),
                    ("小红书前缀设置", 'prefix = "xiaohongshu_data"'),
                    ("其他平台前缀", 'prefix = f"{platform}_data"')
                ]
                
                all_passed = True
                for check_name, check_content in prefix_checks:
                    if check_content in content:
                        print(f"✅ {check_name}: 存在")
                    else:
                        print(f"❌ {check_name}: 缺失")
                        all_passed = False
                
                return all_passed
        else:
            print(f"❌ 数据下载服务文件不存在")
            return False
        
    except Exception as e:
        print(f"❌ 测试平台前缀逻辑失败: {e}")
        return False


def test_edge_cases():
    """测试边界情况"""
    print("\n=== 测试边界情况 ===")
    
    try:
        # 测试未知平台
        filename = DataDownloadService.generate_filename(
            "unknown_platform",
            "测试账号",
            "测试数据",
            "2025-08-01",
            "2025-08-21"
        )
        
        if filename.startswith("unknown_platform_data"):
            print(f"✅ 未知平台处理正确: {filename}")
        else:
            print(f"❌ 未知平台处理错误: {filename}")
            return False
        
        # 测试特殊字符处理
        filename = DataDownloadService.generate_filename(
            "xiaohongshu",
            "测试/账号",
            "数据*类型",
            "2025-08-01",
            "2025-08-21"
        )
        
        # 检查是否包含正确的前缀
        if filename.startswith("xiaohongshu_data"):
            print(f"✅ 特殊字符处理正确: {filename}")
        else:
            print(f"❌ 特殊字符处理错误: {filename}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试边界情况失败: {e}")
        return False


def test_backward_compatibility():
    """测试向后兼容性"""
    print("\n=== 测试向后兼容性 ===")
    
    try:
        # 确保现有的微信平台文件名不受影响
        wechat_filename = DataDownloadService.generate_filename(
            "wechat",
            "微信账号",
            "粉丝数据",
            "2025-08-01",
            "2025-08-21"
        )
        
        wechat_channels_filename = DataDownloadService.generate_filename(
            "wechat_channels",
            "视频号账号",
            "内容数据",
            "2025-08-01",
            "2025-08-21"
        )
        
        if wechat_filename.startswith("wechat_data"):
            print(f"✅ 微信公众号文件名保持兼容: {wechat_filename}")
        else:
            print(f"❌ 微信公众号文件名兼容性问题: {wechat_filename}")
            return False
        
        if wechat_channels_filename.startswith("wechat_channels_data"):
            print(f"✅ 微信视频号文件名保持兼容: {wechat_channels_filename}")
        else:
            print(f"❌ 微信视频号文件名兼容性问题: {wechat_channels_filename}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试向后兼容性失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 测试文件命名修复")
    
    tests = [
        ("文件名生成逻辑", test_filename_generation),
        ("小红书特定命名", test_xiaohongshu_specific_naming),
        ("平台前缀逻辑", test_platform_prefix_logic),
        ("边界情况", test_edge_cases),
        ("向后兼容性", test_backward_compatibility)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            if result:
                print(f"✅ {test_name} - 通过")
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"文件命名修复测试总结: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 文件命名修复完成！")
        print("\n📋 修复总结:")
        print("  ✅ 修复了小红书文件命名错误")
        print("  ✅ 保持了微信平台的兼容性")
        print("  ✅ 添加了其他平台的支持")
        print("  ✅ 处理了边界情况")
        print("\n🔧 修复内容:")
        print("  - xiaohongshu 平台: xiaohongshu_data_前缀")
        print("  - wechat 平台: wechat_data_前缀")
        print("  - wechat_channels 平台: wechat_channels_data_前缀")
        print("  - 其他平台: {platform}_data_前缀")
        print("\n📁 文件命名示例:")
        print("  修复前: wechat_data_赵永明+笔记数据+2025-08-01_to_2025-08-21.xlsx")
        print("  修复后: xiaohongshu_data_赵永明+笔记数据+2025-08-01_to_2025-08-21.xlsx")
        print("\n💡 现在各平台文件名:")
        print("  📱 小红书: xiaohongshu_data_{账号名}+{数据类型}+{日期范围}.xlsx")
        print("  📰 微信公众号: wechat_data_{账号名}+{数据类型}+{日期范围}.xlsx")
        print("  📺 微信视频号: wechat_channels_data_{账号名}+{数据类型}+{日期范围}.xlsx")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return False


if __name__ == "__main__":
    main()
