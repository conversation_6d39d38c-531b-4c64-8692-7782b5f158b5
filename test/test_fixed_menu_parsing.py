#!/usr/bin/env python3
"""
测试修复后的菜单解析逻辑
"""

def test_fixed_menu_parsing():
    """测试修复后的菜单解析逻辑"""
    print("=== 测试修复后的菜单解析逻辑 ===")
    
    # 模拟修复后的解析逻辑
    def parse_menu_key_fixed(key):
        if key.startswith('wechat_mp_'):
            platform = 'wechat_mp'
            data_type = key[len('wechat_mp_'):]
        elif key.startswith('wechat_channels_'):
            platform = 'wechat_channels'
            data_type = key[len('wechat_channels_'):]
        elif key.startswith('xiaohongshu_'):
            platform = 'xiaohongshu'
            data_type = key[len('xiaohongshu_'):]
        else:
            return None, None
        
        return platform, data_type
    
    # 测试用例
    test_cases = [
        ('wechat_mp_content_trend', 'wechat_mp', 'content_trend'),
        ('wechat_mp_content_source', 'wechat_mp', 'content_source'),
        ('wechat_mp_content_detail', 'wechat_mp', 'content_detail'),
        ('wechat_mp_user_channel', 'wechat_mp', 'user_channel'),
        ('wechat_mp_user_source', 'wechat_mp', 'user_source'),
        ('wechat_mp_overview', 'wechat_mp', 'overview'),
        ('wechat_channels_single_video', 'wechat_channels', 'single_video'),
        ('xiaohongshu_note_data', 'xiaohongshu', 'note_data'),
    ]
    
    print("测试修复后的菜单key解析:")
    all_passed = True
    
    for key, expected_platform, expected_data_type in test_cases:
        platform, data_type = parse_menu_key_fixed(key)
        
        if platform == expected_platform and data_type == expected_data_type:
            print(f"  ✅ {key} -> {platform}, {data_type}")
        else:
            print(f"  ❌ {key} -> {platform}, {data_type} (期望: {expected_platform}, {expected_data_type})")
            all_passed = False
    
    return all_passed


def test_complete_flow_fixed():
    """测试修复后的完整流程"""
    print("\n=== 测试修复后的完整流程 ===")
    
    def parse_menu_key_fixed(key):
        if key.startswith('wechat_mp_'):
            platform = 'wechat_mp'
            data_type = key[len('wechat_mp_'):]
        elif key.startswith('wechat_channels_'):
            platform = 'wechat_channels'
            data_type = key[len('wechat_channels_'):]
        elif key.startswith('xiaohongshu_'):
            platform = 'xiaohongshu'
            data_type = key[len('xiaohongshu_'):]
        else:
            return None, None
        
        return platform, data_type
    
    def construct_api_path(selected_data_type):
        if selected_data_type == 'single_video':
            return f'/data-details/wechat-channels/{selected_data_type}'
        elif selected_data_type == 'note_data':
            return f'/data-details/xiaohongshu/{selected_data_type}'
        else:
            return f'/data-details/wechat-mp/{selected_data_type}'
    
    # 测试完整的菜单点击到API请求流程
    test_cases = [
        ('wechat_mp_content_trend', '/data-details/wechat-mp/content_trend'),
        ('wechat_channels_single_video', '/data-details/wechat-channels/single_video'),
        ('xiaohongshu_note_data', '/data-details/xiaohongshu/note_data'),
        ('wechat_mp_overview', '/data-details/wechat-mp/overview'),
    ]
    
    print("测试修复后的完整流程 (菜单点击 -> API请求):")
    all_passed = True
    
    for menu_key, expected_api_path in test_cases:
        # 1. 解析菜单key
        platform, data_type = parse_menu_key_fixed(menu_key)
        
        # 2. 构造API路径
        api_path = construct_api_path(data_type)
        
        print(f"  菜单: {menu_key}")
        print(f"    解析: platform={platform}, data_type={data_type}")
        print(f"    API路径: {api_path}")
        
        if api_path == expected_api_path:
            print(f"    ✅ 正确")
        else:
            print(f"    ❌ 错误，期望: {expected_api_path}")
            all_passed = False
        print()
    
    return all_passed


def test_edge_cases():
    """测试边界情况"""
    print("\n=== 测试边界情况 ===")
    
    def parse_menu_key_fixed(key):
        if key.startswith('wechat_mp_'):
            platform = 'wechat_mp'
            data_type = key[len('wechat_mp_'):]
        elif key.startswith('wechat_channels_'):
            platform = 'wechat_channels'
            data_type = key[len('wechat_channels_'):]
        elif key.startswith('xiaohongshu_'):
            platform = 'xiaohongshu'
            data_type = key[len('xiaohongshu_'):]
        else:
            return None, None
        
        return platform, data_type
    
    # 边界情况测试
    edge_cases = [
        ('wechat_mp_', 'wechat_mp', ''),  # 空数据类型
        ('xiaohongshu_', 'xiaohongshu', ''),  # 空数据类型
        ('invalid_key', None, None),  # 无效key
        ('wechat_mp', None, None),  # 不完整的key
        ('xiaohongshu', None, None),  # 不完整的key
        ('wechat_mp_multi_part_data_type', 'wechat_mp', 'multi_part_data_type'),  # 多部分数据类型
    ]
    
    print("测试边界情况:")
    all_passed = True
    
    for key, expected_platform, expected_data_type in edge_cases:
        platform, data_type = parse_menu_key_fixed(key)
        
        if platform == expected_platform and data_type == expected_data_type:
            print(f"  ✅ {key} -> {platform}, {data_type}")
        else:
            print(f"  ❌ {key} -> {platform}, {data_type} (期望: {expected_platform}, {expected_data_type})")
            all_passed = False
    
    return all_passed


def main():
    """主测试函数"""
    print("🚀 开始测试修复后的菜单解析逻辑")
    
    tests = [
        ("修复后的菜单解析逻辑", test_fixed_menu_parsing),
        ("修复后的完整流程", test_complete_flow_fixed),
        ("边界情况", test_edge_cases)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        try:
            if test_func():
                print(f"✅ {test_name} - 通过")
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"修复后菜单解析测试总结: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有测试通过！")
        print("\n📋 修复内容:")
        print("  ✅ 菜单key解析逻辑已修复")
        print("  ✅ xiaohongshu_note_data 正确解析为 xiaohongshu + note_data")
        print("  ✅ 完整流程正确：小红书菜单 -> xiaohongshu API")
        print("  ✅ 边界情况处理正确")
        print("\n🚀 现在小红书菜单应该正确请求 xiaohongshu API！")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关逻辑。")
        return False


if __name__ == "__main__":
    main()
