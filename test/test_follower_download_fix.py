#!/usr/bin/env python3
"""
测试关注者数据下载修复
"""

import sys
import os
import asyncio
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import SessionLocal
from app.models import PlatformAccount
from app.services.wechat_channels_service import WeChatChannelsService


def test_service_methods():
    """测试服务方法是否存在"""
    print("=== 测试服务方法 ===")
    
    try:
        # 检查修复后的方法
        service_methods = dir(WeChatChannelsService)
        
        required_methods = [
            'download_follower_data',  # 新的下载方法
            'get_follower_data',       # 保留的原方法
            '_save_csv_to_downloads_from_bytes',
            '_import_follower_from_bytes',
            '_parse_follower_csv',
            '_import_follower_to_database'
        ]
        
        missing_methods = []
        for method in required_methods:
            if method in service_methods:
                print(f"✅ 方法存在: {method}")
            else:
                missing_methods.append(method)
                print(f"❌ 方法缺失: {method}")
        
        if missing_methods:
            print(f"❌ 缺失方法: {missing_methods}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 服务方法测试失败: {e}")
        return False


def test_temp_downloads_directory():
    """测试temp_downloads目录创建"""
    print("\n=== 测试temp_downloads目录 ===")
    
    try:
        downloads_dir = "temp_downloads"
        
        # 创建目录
        os.makedirs(downloads_dir, exist_ok=True)
        
        if os.path.exists(downloads_dir) and os.path.isdir(downloads_dir):
            print(f"✅ temp_downloads目录存在: {os.path.abspath(downloads_dir)}")
            
            # 测试写入权限
            test_file = os.path.join(downloads_dir, "test.txt")
            with open(test_file, 'w') as f:
                f.write("test")
            
            if os.path.exists(test_file):
                print("✅ temp_downloads目录可写")
                os.remove(test_file)
                return True
            else:
                print("❌ temp_downloads目录不可写")
                return False
        else:
            print("❌ temp_downloads目录创建失败")
            return False
        
    except Exception as e:
        print(f"❌ temp_downloads目录测试失败: {e}")
        return False


async def test_download_event_handling():
    """测试下载事件处理逻辑"""
    print("\n=== 测试下载事件处理 ===")
    
    try:
        # 创建服务实例
        service = WeChatChannelsService(account_id=999, headless=True)
        
        # 初始化浏览器
        await service._init_browser()
        context = await service._create_persistent_context()
        page = await context.new_page()
        
        # 模拟下载事件处理
        download_data = None
        download_event = asyncio.Event()
        
        # 创建模拟下载对象
        class MockDownload:
            def __init__(self):
                self.suggested_filename = "test_follower_data.csv"
                self._path = None
            
            async def path(self):
                # 创建临时测试文件
                import tempfile
                with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
                    f.write("日期,净增关注,新增关注,取消关注,关注者总数\n")
                    f.write("2024-01-01,10,15,5,1000\n")
                    f.write("2024-01-02,5,12,7,1005\n")
                    self._path = f.name
                return self._path
        
        # 测试下载处理函数
        async def handle_download(download):
            nonlocal download_data
            try:
                print(f"检测到下载事件: {download.suggested_filename}")
                
                # 等待下载完成
                download_path = await download.path()
                if download_path:
                    # 读取文件内容
                    with open(download_path, 'rb') as f:
                        download_data = f.read()
                    print(f"✅ 成功获取下载文件，大小: {len(download_data)} bytes")
                    
                    # 保存到temp_downloads目录
                    await service._save_csv_to_downloads_from_bytes(download_data, download.suggested_filename)
                
                download_event.set()
            except Exception as e:
                print(f"处理下载失败: {e}")
                download_event.set()
        
        # 模拟下载事件
        mock_download = MockDownload()
        await handle_download(mock_download)
        
        # 等待处理完成
        await asyncio.wait_for(download_event.wait(), timeout=5.0)
        
        if download_data and len(download_data) > 0:
            print("✅ 下载事件处理成功")
            
            # 检查文件是否保存到temp_downloads
            downloads_dir = "temp_downloads"
            if os.path.exists(downloads_dir):
                files = os.listdir(downloads_dir)
                csv_files = [f for f in files if f.endswith('.csv')]
                if csv_files:
                    print(f"✅ 文件已保存到temp_downloads: {csv_files}")
                    return True
                else:
                    print("❌ 未找到保存的CSV文件")
                    return False
            else:
                print("❌ temp_downloads目录不存在")
                return False
        else:
            print("❌ 下载数据为空")
            return False
        
        # 清理资源
        await service.close()
        
        # 清理临时文件
        try:
            if mock_download._path:
                os.remove(mock_download._path)
        except:
            pass
        
    except Exception as e:
        print(f"❌ 下载事件处理测试失败: {e}")
        return False


async def test_csv_parsing_improvements():
    """测试CSV解析改进"""
    print("\n=== 测试CSV解析改进 ===")
    
    try:
        # 创建测试CSV文件（模拟微信视频号格式）
        import tempfile
        
        test_csv_content = """标题行1
标题行2
标题行3
日期,净增关注,新增关注,取消关注,关注者总数
2024-01-01,10,15,5,1000
2024-01-02,5,12,7,1005
2024-01-03,-2,8,10,1003
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8') as f:
            f.write(test_csv_content)
            temp_path = f.name
        
        # 创建服务实例
        service = WeChatChannelsService(account_id=999, headless=True)
        
        # 测试CSV解析
        async def test_parse():
            data = await service._parse_follower_csv(temp_path)
            return data

        # 直接await而不是使用asyncio.run
        parsed_data = await test_parse()
        
        # 清理临时文件
        os.remove(temp_path)
        
        if parsed_data and len(parsed_data) == 3:
            print(f"✅ CSV解析成功，解析出 {len(parsed_data)} 条记录")
            
            # 检查数据内容
            first_record = parsed_data[0]
            expected_fields = ['date', 'net_follower_increase', 'new_followers', 'unfollowers', 'total_followers']
            
            missing_fields = []
            for field in expected_fields:
                if field not in first_record:
                    missing_fields.append(field)
            
            if not missing_fields:
                print("✅ 所有必需字段都存在")
                print(f"示例记录: {first_record}")
                return True
            else:
                print(f"❌ 缺失字段: {missing_fields}")
                return False
        else:
            print(f"❌ CSV解析失败，期望3条记录，实际: {len(parsed_data) if parsed_data else 0}")
            return False
        
    except Exception as e:
        print(f"❌ CSV解析测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 开始测试关注者数据下载修复")
    
    tests = [
        ("服务方法", test_service_methods, False),
        ("temp_downloads目录", test_temp_downloads_directory, False),
        ("下载事件处理", test_download_event_handling, True),
        ("CSV解析改进", test_csv_parsing_improvements, True)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func, is_async in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        try:
            if is_async:
                result = await test_func()
            else:
                result = test_func()
                
            if result:
                print(f"✅ {test_name} - 通过")
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"关注者数据下载修复测试总结: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有测试通过！")
        print("\n📋 修复总结:")
        print("  ✅ 新增download_follower_data方法")
        print("  ✅ 参考download_single_video_data实现")
        print("  ✅ 使用下载事件监听机制")
        print("  ✅ 文件自动保存到temp_downloads目录")
        print("  ✅ CSV解析跳过前3行标题")
        print("  ✅ 支持自动导入数据库")
        print("\n🚀 关注者数据下载功能已修复！")
        print("\n🔧 主要改进:")
        print("  1. 使用Playwright下载事件监听")
        print("  2. 先设置监听器再点击下载")
        print("  3. 文件自动保存到用户下载目录")
        print("  4. 改进CSV解析逻辑")
        print("  5. 完善错误处理机制")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return False


if __name__ == "__main__":
    asyncio.run(main())
