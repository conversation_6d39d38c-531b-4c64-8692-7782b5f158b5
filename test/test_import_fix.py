#!/usr/bin/env python3
"""
测试导入修复
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_auto_update_router_import():
    """测试自动更新路由导入"""
    print("=== 测试自动更新路由导入 ===")
    
    try:
        from app.routers import auto_update
        print("✅ 自动更新路由导入成功")
        
        # 检查路由器是否存在
        if hasattr(auto_update, 'router'):
            print("✅ 路由器对象存在")
        else:
            print("❌ 路由器对象不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 自动更新路由导入失败: {e}")
        return False


def test_main_app_import():
    """测试主应用导入"""
    print("\n=== 测试主应用导入 ===")
    
    try:
        from main import app
        print("✅ 主应用导入成功")
        
        # 检查应用是否是FastAPI实例
        from fastapi import FastAPI
        if isinstance(app, FastAPI):
            print("✅ 应用是FastAPI实例")
        else:
            print("❌ 应用不是FastAPI实例")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 主应用导入失败: {e}")
        return False


def test_auth_import():
    """测试认证导入"""
    print("\n=== 测试认证导入 ===")
    
    try:
        from app.routers.auth import get_current_user
        print("✅ get_current_user 导入成功")
        
        # 检查是否是函数
        if callable(get_current_user):
            print("✅ get_current_user 是可调用对象")
        else:
            print("❌ get_current_user 不是可调用对象")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 认证导入失败: {e}")
        return False


def test_auto_update_service_import():
    """测试自动更新服务导入"""
    print("\n=== 测试自动更新服务导入 ===")
    
    try:
        from app.services.auto_update_service import AutoUpdateService
        print("✅ AutoUpdateService 导入成功")
        
        # 检查关键方法是否存在
        methods = [
            'get_config',
            'create_or_update_config',
            'start_scheduler',
            'stop_scheduler',
            'get_status'
        ]
        
        for method in methods:
            if hasattr(AutoUpdateService, method):
                print(f"✅ 方法 {method} 存在")
            else:
                print(f"❌ 方法 {method} 不存在")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 自动更新服务导入失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 测试导入修复")
    
    tests = [
        ("自动更新路由导入", test_auto_update_router_import),
        ("主应用导入", test_main_app_import),
        ("认证导入", test_auth_import),
        ("自动更新服务导入", test_auto_update_service_import)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            if result:
                print(f"✅ {test_name} - 通过")
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"导入修复测试总结: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 导入修复完成！")
        print("\n📋 修复总结:")
        print("  ✅ 修复了 app.auth 导入错误")
        print("  ✅ 使用正确的 app.routers.auth 导入")
        print("  ✅ 所有模块导入正常")
        print("  ✅ 应用可以正常启动")
        print("\n🚀 现在可以:")
        print("  - 正常启动应用服务器")
        print("  - 使用自动更新API接口")
        print("  - 配置和管理自动更新")
        print("  - 监控自动更新状态")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return False


if __name__ == "__main__":
    main()
