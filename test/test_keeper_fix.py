#!/usr/bin/env python3
"""
测试登录状态维持服务修复
"""
import asyncio
import sys
import os
import signal

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_keeper_service():
    """测试维持服务的启动和停止"""
    print("🔍 测试登录状态维持服务修复...")
    
    try:
        from app.background.login_state_keeper import LoginStateKeeperScheduler
        
        # 创建调度器
        scheduler = LoginStateKeeperScheduler()
        
        print("📋 初始化调度器...")
        scheduler.initialize()
        
        print("🚀 启动调度器...")
        scheduler.start()
        
        # 获取状态
        status = scheduler.get_job_status()
        print(f"✅ 调度器状态: {status}")
        
        # 等待一小段时间
        print("⏳ 等待5秒...")
        await asyncio.sleep(5)
        
        # 手动触发一次任务（如果有账号的话）
        print("🔄 尝试手动触发任务...")
        try:
            result = await scheduler.trigger_job_now()
            print(f"✅ 手动触发结果: {result.get('success', False)}")
        except Exception as e:
            print(f"⚠️  手动触发失败（可能没有账号）: {e}")
        
        print("🛑 停止调度器...")
        scheduler.stop()
        
        print("✅ 测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def signal_handler(signum, frame):
    """信号处理器"""
    print("\n🛑 收到停止信号，正在退出...")
    sys.exit(0)

async def main():
    """主函数"""
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    await test_keeper_service()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    except Exception as e:
        print(f"💥 测试异常: {e}")
        sys.exit(1)
