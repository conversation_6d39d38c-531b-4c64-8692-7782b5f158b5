#!/usr/bin/env python3
"""
测试界面布局调整
"""

import os
import re

def test_layout_structure():
    """测试界面布局结构"""
    print("=== 测试界面布局结构 ===")
    
    try:
        page_file = "frontend/src/pages/DataUpdate.tsx"
        
        if os.path.exists(page_file):
            print(f"✅ DataUpdate页面文件存在")
            
            with open(page_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # 检查布局顺序
                auto_update_pos = content.find("自动更新配置")
                manual_update_pos = content.find("手动更新")
                
                if auto_update_pos != -1 and manual_update_pos != -1:
                    if auto_update_pos < manual_update_pos:
                        print(f"✅ 自动更新配置在手动更新之前")
                    else:
                        print(f"❌ 布局顺序错误：自动更新应该在手动更新之前")
                        return False
                else:
                    print(f"❌ 找不到自动更新配置或手动更新块")
                    return False
                
                return True
        else:
            print(f"❌ DataUpdate页面文件不存在")
            return False
        
    except Exception as e:
        print(f"❌ 界面布局结构测试失败: {e}")
        return False


def test_auto_update_block():
    """测试自动更新块"""
    print("\n=== 测试自动更新块 ===")
    
    try:
        page_file = "frontend/src/pages/DataUpdate.tsx"
        
        with open(page_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
            # 检查自动更新块的组件
            auto_update_checks = [
                ("自动更新配置标题", "自动更新配置"),
                ("设置图标", "SettingOutlined"),
                ("启用开关", "启用自动更新"),
                ("时间选择器", "TimePicker"),
                ("天数输入", "时间跨度（天）"),
                ("保存按钮", "保存配置"),
                ("测试按钮", "测试更新"),
                ("状态显示区域", "backgroundColor: '#f5f5f5'"),
                ("调度器状态", "调度器运行中"),
                ("下次运行时间", "下次运行时间")
            ]
            
            all_passed = True
            for check_name, check_content in auto_update_checks:
                if check_content in content:
                    print(f"✅ {check_name}: 通过")
                else:
                    print(f"❌ {check_name}: 失败")
                    all_passed = False
            
            return all_passed
        
    except Exception as e:
        print(f"❌ 自动更新块测试失败: {e}")
        return False


def test_manual_update_block():
    """测试手动更新块"""
    print("\n=== 测试手动更新块 ===")
    
    try:
        page_file = "frontend/src/pages/DataUpdate.tsx"
        
        with open(page_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
            # 检查手动更新块的组件
            manual_update_checks = [
                ("手动更新标题", "手动更新"),
                ("播放图标", "PlayCircleOutlined"),
                ("上次更新标题", "上次更新"),
                ("数据更新操作标题", "数据更新操作"),
                ("当前任务进度标题", "当前任务进度"),
                ("历史更新记录标题", "历史更新记录"),
                ("历史图标", "HistoryOutlined"),
                ("日期范围选择", "RangePicker"),
                ("开始更新按钮", "开始更新"),
                ("进度条", "Progress"),
                ("数据描述", "Descriptions"),
                ("历史表格", "Table"),
                ("分页", "pagination"),
                ("分隔线", "Divider")
            ]
            
            all_passed = True
            for check_name, check_content in manual_update_checks:
                if check_content in content:
                    print(f"✅ {check_name}: 通过")
                else:
                    print(f"❌ {check_name}: 失败")
                    all_passed = False
            
            return all_passed
        
    except Exception as e:
        print(f"❌ 手动更新块测试失败: {e}")
        return False


def test_jsx_structure():
    """测试JSX结构完整性"""
    print("\n=== 测试JSX结构完整性 ===")
    
    try:
        page_file = "frontend/src/pages/DataUpdate.tsx"
        
        with open(page_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
            # 检查JSX结构
            jsx_checks = [
                ("组件返回", "return ("),
                ("主容器div", "<div style={{ padding: '24px' }}>"),
                ("页面标题", "<Title level={2}>数据更新</Title>"),
                ("自动更新Card", "自动更新配置"),
                ("手动更新Card", "手动更新"),
                ("组件导出", "export default DataUpdate")
            ]
            
            all_passed = True
            for check_name, check_content in jsx_checks:
                if check_content in content:
                    print(f"✅ {check_name}: 通过")
                else:
                    print(f"❌ {check_name}: 失败")
                    all_passed = False
            
            # 检查括号匹配
            open_braces = content.count('{')
            close_braces = content.count('}')
            open_parens = content.count('(')
            close_parens = content.count(')')
            
            if open_braces == close_braces:
                print(f"✅ 大括号匹配: {open_braces} 对")
            else:
                print(f"❌ 大括号不匹配: 开 {open_braces}, 闭 {close_braces}")
                all_passed = False
            
            if open_parens == close_parens:
                print(f"✅ 小括号匹配: {open_parens} 对")
            else:
                print(f"❌ 小括号不匹配: 开 {open_parens}, 闭 {close_parens}")
                all_passed = False
            
            return all_passed
        
    except Exception as e:
        print(f"❌ JSX结构完整性测试失败: {e}")
        return False


def test_component_hierarchy():
    """测试组件层次结构"""
    print("\n=== 测试组件层次结构 ===")
    
    try:
        page_file = "frontend/src/pages/DataUpdate.tsx"
        
        with open(page_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
            # 检查组件层次
            hierarchy_checks = [
                ("主容器", "div style={{ padding: '24px' }}"),
                ("页面标题", "Title level={2}"),
                ("自动更新Card", "自动更新配置"),
                ("手动更新Card", "手动更新"),
                ("子标题", "Title level={4}"),
                ("分隔线", "Divider"),
                ("表单", "Form"),
                ("表格", "Table")
            ]
            
            all_passed = True
            for check_name, check_content in hierarchy_checks:
                if check_content in content:
                    print(f"✅ {check_name}: 通过")
                else:
                    print(f"❌ {check_name}: 失败")
                    all_passed = False
            
            return all_passed
        
    except Exception as e:
        print(f"❌ 组件层次结构测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 测试界面布局调整")
    
    tests = [
        ("界面布局结构", test_layout_structure),
        ("自动更新块", test_auto_update_block),
        ("手动更新块", test_manual_update_block),
        ("JSX结构完整性", test_jsx_structure),
        ("组件层次结构", test_component_hierarchy)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            if result:
                print(f"✅ {test_name} - 通过")
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"界面布局调整测试总结: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 界面布局调整完成！")
        print("\n📋 布局总结:")
        print("  ✅ 自动更新配置块在最上面")
        print("  ✅ 手动更新块包含所有手动更新功能")
        print("  ✅ JSX结构完整正确")
        print("  ✅ 组件层次清晰")
        print("\n🎯 新布局结构:")
        print("  ┌─ 数据更新 ─────────────────────┐")
        print("  │                               │")
        print("  │ ┌─ 自动更新配置 ─────────────┐ │")
        print("  │ │ • 启用开关                 │ │")
        print("  │ │ • 更新时间                 │ │")
        print("  │ │ • 时间跨度                 │ │")
        print("  │ │ • 状态显示                 │ │")
        print("  │ └───────────────────────────┘ │")
        print("  │                               │")
        print("  │ ┌─ 手动更新 ─────────────────┐ │")
        print("  │ │ • 上次更新                 │ │")
        print("  │ │ • 数据更新操作             │ │")
        print("  │ │ • 当前任务进度             │ │")
        print("  │ │ • 历史更新记录             │ │")
        print("  │ └───────────────────────────┘ │")
        print("  └───────────────────────────────┘")
        print("\n💡 用户体验:")
        print("  - 自动更新配置优先显示")
        print("  - 手动更新功能统一管理")
        print("  - 清晰的功能分区")
        print("  - 一致的视觉层次")
        print("\n🔗 访问地址:")
        print("  http://localhost:3000/data-update")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return False


if __name__ == "__main__":
    main()
