#!/usr/bin/env python3
"""
测试修复后的视频号登录功能
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.wechat_channels_service import WeChatChannelsService


async def test_login_flow():
    """测试完整的登录流程"""
    print("🎬 测试修复后的视频号登录功能")
    print("=" * 50)
    
    # 创建服务实例（使用可视模式便于调试）
    service = WeChatChannelsService(account_id=999, headless=False)
    
    try:
        print("📱 步骤1: 获取登录二维码")
        print("-" * 30)
        
        qr_code = await service.get_login_qrcode()
        
        if qr_code == "already_logged_in":
            print("✅ 检测到已有有效的登录状态")
            return True
        elif qr_code and qr_code.startswith("data:image/png;base64,"):
            print(f"✅ 成功获取二维码，数据长度: {len(qr_code)} 字符")
            print("🖥️ 浏览器窗口已打开，请观察页面状态")
            
            print("\n📱 步骤2: 等待用户扫码")
            print("-" * 30)
            print("请使用微信扫描页面上的二维码...")
            
            # 等待用户扫码
            max_wait_time = 120  # 2分钟
            check_interval = 3   # 每3秒检查一次
            
            for i in range(0, max_wait_time, check_interval):
                print(f"⏰ 等待扫码中... ({i+check_interval}/{max_wait_time}秒)")
                
                # 检查登录状态
                is_logged_in = await service.check_login_status(wait_for_redirect=True, timeout=check_interval)
                
                if is_logged_in:
                    print("🎉 登录成功！")
                    
                    # 保存登录状态
                    print("💾 保存登录状态...")
                    save_result = await service.save_login_state()
                    if save_result:
                        print("✅ 登录状态已保存")
                    else:
                        print("❌ 登录状态保存失败")
                    
                    # 获取cookies
                    cookies = await service.get_cookies()
                    if cookies:
                        print(f"🍪 成功获取cookies，长度: {len(cookies)} 字符")
                    
                    return True
                
                await asyncio.sleep(check_interval)
            
            print("⏰ 等待扫码超时")
            return False
            
        else:
            print("❌ 获取二维码失败")
            if qr_code:
                print(f"返回内容: {qr_code[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 不要立即关闭，让用户有时间观察
        print("\n⏸️ 测试完成，浏览器将在10秒后关闭...")
        await asyncio.sleep(10)
        await service.close()


async def test_login_status_check():
    """测试登录状态检查功能"""
    print("\n🔍 测试登录状态检查功能")
    print("=" * 50)
    
    service = WeChatChannelsService(account_id=999, headless=False)
    
    try:
        # 尝试加载已保存的登录状态
        print("📂 加载已保存的登录状态...")
        load_result = await service.load_login_state()
        
        if load_result:
            print("✅ 成功加载已保存的登录状态")
            
            # 检查登录是否仍然有效
            print("🔍 检查登录状态是否仍然有效...")
            is_valid = await service.check_existing_login()
            
            if is_valid:
                print("✅ 现有登录状态仍然有效")
                return True
            else:
                print("⚠️ 现有登录状态已失效")
                return False
        else:
            print("ℹ️ 未找到已保存的登录状态")
            return False
            
    except Exception as e:
        print(f"❌ 检查登录状态时发生错误: {e}")
        return False
        
    finally:
        await service.close()


async def main():
    """主测试函数"""
    print("🚀 视频号登录功能修复测试")
    print("=" * 60)
    
    # 首先检查是否有现有的登录状态
    print("🔍 步骤1: 检查现有登录状态")
    existing_login = await test_login_status_check()
    
    if not existing_login:
        print("\n🔑 步骤2: 执行完整登录流程")
        # 如果没有有效的现有登录，进行完整的登录测试
        success = await test_login_flow()
        
        if success:
            print("\n🎉 视频号登录功能测试成功！")
            print("主要修复内容:")
            print("1. ✅ 获取二维码后保持页面状态")
            print("2. ✅ 改进登录状态检测逻辑")
            print("3. ✅ 增加更多成功指示器")
            print("4. ✅ 改进iframe检测机制")
            print("5. ✅ 添加详细的调试信息")
        else:
            print("\n❌ 视频号登录功能测试失败！")
            print("可能的原因:")
            print("1. 网络连接问题")
            print("2. 页面结构发生变化")
            print("3. 登录检测逻辑需要进一步调整")
            
        return success
    else:
        print("\n✅ 检测到有效的现有登录状态，无需重新登录")
        return True


if __name__ == "__main__":
    # 运行测试
    result = asyncio.run(main())
    
    if result:
        print("\n🏆 所有测试通过！")
        sys.exit(0)
    else:
        print("\n💥 测试失败！")
        sys.exit(1)
