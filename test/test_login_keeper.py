#!/usr/bin/env python3
"""
登录状态维持服务测试脚本
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from app.services.platform_pages_config import PlatformPagesConfig
    from app.config.keeper_config import get_config, is_keeper_enabled
    print("✅ 基础模块导入成功")
except ImportError as e:
    print(f"❌ 基础模块导入失败: {e}")
    sys.exit(1)

async def test_platform_config():
    """测试平台配置"""
    print("=== 测试平台配置 ===")
    
    platforms = PlatformPagesConfig.get_all_supported_platforms()
    print(f"支持的平台: {platforms}")
    
    for platform in platforms:
        print(f"\n平台: {platform}")
        
        # 获取平台信息
        info = PlatformPagesConfig.get_platform_info(platform)
        print(f"  名称: {info.get('name', 'N/A')}")
        print(f"  描述: {info.get('description', 'N/A')}")
        
        # 获取页面配置
        pages = PlatformPagesConfig.get_platform_pages(platform)
        print(f"  页面数量: {len(pages)}")
        
        # 获取验证规则
        rules = PlatformPagesConfig.get_validation_rules(platform)
        success_count = len(rules.get('success_indicators', []))
        login_count = len(rules.get('login_required_indicators', []))
        print(f"  成功指示器: {success_count} 个")
        print(f"  登录失效指示器: {login_count} 个")
        
        # 测试随机页面选择
        random_page = PlatformPagesConfig.get_weighted_random_page(platform)
        print(f"  随机页面: {random_page}")

def test_config():
    """测试配置"""
    print("\n=== 测试配置 ===")
    
    config = get_config()
    print(f"服务启用: {is_keeper_enabled()}")
    print(f"执行间隔: {config['interval_minutes']} 分钟")
    print(f"启用平台: {config['enabled_platforms']}")
    print(f"最大重试: {config['max_retries']} 次")
    print(f"并发账号: {config['concurrent_accounts']} 个")
    print(f"浏览器超时: {config['browser_timeout']} 秒")

async def test_keeper_service():
    """测试维持服务"""
    print("\n=== 测试维持服务 ===")
    print("⚠️  需要数据库连接，跳过此测试")

def test_scheduler():
    """测试调度器"""
    print("\n=== 测试调度器 ===")
    print("⚠️  需要数据库连接，跳过此测试")

async def main():
    """主测试函数"""
    print("登录状态维持服务测试")
    print("=" * 50)
    
    # 测试平台配置
    await test_platform_config()
    
    # 测试配置
    test_config()
    
    # 测试维持服务
    await test_keeper_service()
    
    # 测试调度器
    test_scheduler()
    
    print("\n" + "=" * 50)
    print("测试完成")

if __name__ == "__main__":
    asyncio.run(main())
