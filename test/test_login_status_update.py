#!/usr/bin/env python3
"""
测试登录状态更新功能
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_login_status_update():
    """测试登录状态更新功能"""
    print("🔍 测试登录状态更新功能...")
    
    try:
        from app.database import SessionLocal
        from app.models import PlatformAccount
        from app.services.login_keeper_service import LoginKeeperService
        
        # 查看数据库中的账号
        db = SessionLocal()
        try:
            accounts = db.query(PlatformAccount).all()
            print(f"📊 数据库中共有 {len(accounts)} 个账号:")
            
            for account in accounts:
                print(f"  - ID: {account.id}, 名称: {account.name}, 平台: {account.platform}")
                print(f"    登录状态: {account.login_status}, 最后登录: {account.last_login_time}")
                print(f"    创建时间: {account.created_at}")
                print()
            
            if not accounts:
                print("⚠️  数据库中没有账号，无法测试登录状态更新功能")
                return
            
            # 测试登录状态更新
            keeper = LoginKeeperService()
            
            # 选择第一个账号进行测试
            test_account = accounts[0]
            print(f"🧪 测试账号: {test_account.name} (ID: {test_account.id})")
            print(f"   当前登录状态: {test_account.login_status}")
            
            # 测试更新为失效状态
            print("\n📝 测试更新登录状态为失效...")
            await keeper._update_account_login_status(test_account.id, False)
            
            # 重新查询验证
            db.refresh(test_account)
            print(f"✅ 更新后状态: {test_account.login_status}")
            
            # 测试更新为有效状态
            print("\n📝 测试更新登录状态为有效...")
            await keeper._update_account_login_status(test_account.id, True)
            
            # 重新查询验证
            db.refresh(test_account)
            print(f"✅ 更新后状态: {test_account.login_status}")
            print(f"✅ 最后登录时间: {test_account.last_login_time}")
            
        finally:
            db.close()
        
        # 测试完整的维持流程
        print("\n🔄 测试完整的登录状态维持流程...")
        result = await keeper.maintain_all_login_states()
        
        print(f"📊 维持结果:")
        print(f"  - 成功: {result.get('success', False)}")
        print(f"  - 处理账号数: {result.get('accounts_processed', 0)}")
        print(f"  - 成功账号数: {result.get('successful_accounts', 0)}")
        print(f"  - 失败账号数: {result.get('failed_accounts', 0)}")
        
        if 'details' in result:
            print(f"  - 详细结果:")
            for detail in result['details']:
                print(f"    * 账号: {detail.get('account_name', 'N/A')}")
                print(f"      状态: {detail.get('success', False)}")
                print(f"      消息: {detail.get('message', 'N/A')}")
                print(f"      登录状态已更新: {detail.get('login_status_updated', False)}")
        
        # 再次检查数据库状态
        print("\n📊 最终数据库状态:")
        db = SessionLocal()
        try:
            accounts = db.query(PlatformAccount).all()
            for account in accounts:
                print(f"  - {account.name}: 登录状态={account.login_status}, 最后登录={account.last_login_time}")
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_login_status_update())
