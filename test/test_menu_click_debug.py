#!/usr/bin/env python3
"""
测试菜单点击调试
"""

def test_menu_key_parsing():
    """测试菜单key解析逻辑"""
    print("=== 测试菜单key解析逻辑 ===")
    
    # 模拟前端的解析逻辑
    def parse_menu_key(key):
        parts = key.split('_')
        if len(parts) >= 3:
            platform = '_'.join(parts[:2])  # 前两部分组成平台
            data_type = '_'.join(parts[2:])  # 剩余部分组成数据类型
            return platform, data_type
        return None, None
    
    # 测试用例
    test_cases = [
        ('wechat_mp_content_trend', 'wechat_mp', 'content_trend'),
        ('wechat_mp_content_source', 'wechat_mp', 'content_source'),
        ('wechat_channels_single_video', 'wechat_channels', 'single_video'),
        ('xiaohongshu_note_data', 'xiaohongshu', 'note_data'),
        ('wechat_mp_overview', 'wechat_mp', 'overview'),
    ]
    
    print("测试菜单key解析:")
    all_passed = True
    
    for key, expected_platform, expected_data_type in test_cases:
        platform, data_type = parse_menu_key(key)
        
        if platform == expected_platform and data_type == expected_data_type:
            print(f"  ✅ {key} -> {platform}, {data_type}")
        else:
            print(f"  ❌ {key} -> {platform}, {data_type} (期望: {expected_platform}, {expected_data_type})")
            all_passed = False
    
    return all_passed


def test_api_path_construction():
    """测试API路径构造逻辑"""
    print("\n=== 测试API路径构造逻辑 ===")
    
    # 模拟前端的API路径构造逻辑
    def construct_api_path(selected_data_type):
        if selected_data_type == 'single_video':
            return f'/data-details/wechat-channels/{selected_data_type}'
        elif selected_data_type == 'note_data':
            return f'/data-details/xiaohongshu/{selected_data_type}'
        else:
            return f'/data-details/wechat-mp/{selected_data_type}'
    
    # 测试用例
    test_cases = [
        ('content_trend', '/data-details/wechat-mp/content_trend'),
        ('content_source', '/data-details/wechat-mp/content_source'),
        ('single_video', '/data-details/wechat-channels/single_video'),
        ('note_data', '/data-details/xiaohongshu/note_data'),
        ('overview', '/data-details/wechat-mp/overview'),
        ('data', '/data-details/wechat-mp/data'),  # 这个会导致问题
        ('invalid', '/data-details/wechat-mp/invalid'),  # 这个也会导致问题
    ]
    
    print("测试API路径构造:")
    all_passed = True
    
    for data_type, expected_path in test_cases:
        path = construct_api_path(data_type)
        
        if path == expected_path:
            print(f"  ✅ {data_type} -> {path}")
        else:
            print(f"  ❌ {data_type} -> {path} (期望: {expected_path})")
            all_passed = False
    
    return all_passed


def test_complete_flow():
    """测试完整流程"""
    print("\n=== 测试完整流程 ===")
    
    def parse_menu_key(key):
        parts = key.split('_')
        if len(parts) >= 3:
            platform = '_'.join(parts[:2])
            data_type = '_'.join(parts[2:])
            return platform, data_type
        return None, None
    
    def construct_api_path(selected_data_type):
        if selected_data_type == 'single_video':
            return f'/data-details/wechat-channels/{selected_data_type}'
        elif selected_data_type == 'note_data':
            return f'/data-details/xiaohongshu/{selected_data_type}'
        else:
            return f'/data-details/wechat-mp/{selected_data_type}'
    
    # 测试完整的菜单点击到API请求流程
    test_cases = [
        ('wechat_mp_content_trend', '/data-details/wechat-mp/content_trend'),
        ('wechat_channels_single_video', '/data-details/wechat-channels/single_video'),
        ('xiaohongshu_note_data', '/data-details/xiaohongshu/note_data'),
    ]
    
    print("测试完整流程 (菜单点击 -> API请求):")
    all_passed = True
    
    for menu_key, expected_api_path in test_cases:
        # 1. 解析菜单key
        platform, data_type = parse_menu_key(menu_key)
        
        # 2. 构造API路径
        api_path = construct_api_path(data_type)
        
        print(f"  菜单: {menu_key}")
        print(f"    解析: platform={platform}, data_type={data_type}")
        print(f"    API路径: {api_path}")
        
        if api_path == expected_api_path:
            print(f"    ✅ 正确")
        else:
            print(f"    ❌ 错误，期望: {expected_api_path}")
            all_passed = False
        print()
    
    return all_passed


def test_problematic_scenarios():
    """测试问题场景"""
    print("\n=== 测试问题场景 ===")
    
    # 可能导致问题的场景
    scenarios = [
        {
            'description': '用户直接访问带有错误type参数的URL',
            'url_params': '?platform=wechat_mp&type=data',
            'expected_issue': '会导致API请求 /data-details/wechat-mp/data，返回400错误'
        },
        {
            'description': '前端状态异常，selectedDataType被设置为无效值',
            'selected_data_type': 'data',
            'expected_issue': '会构造错误的API路径'
        },
        {
            'description': '菜单key解析错误',
            'menu_key': 'invalid_key',
            'expected_issue': '解析失败，可能使用默认值'
        }
    ]
    
    print("问题场景分析:")
    for scenario in scenarios:
        print(f"  场景: {scenario['description']}")
        if 'url_params' in scenario:
            print(f"    URL参数: {scenario['url_params']}")
        if 'selected_data_type' in scenario:
            print(f"    数据类型: {scenario['selected_data_type']}")
        if 'menu_key' in scenario:
            print(f"    菜单key: {scenario['menu_key']}")
        print(f"    预期问题: {scenario['expected_issue']}")
        print()
    
    return True


def main():
    """主测试函数"""
    print("🚀 开始菜单点击调试测试")
    
    tests = [
        ("菜单key解析逻辑", test_menu_key_parsing),
        ("API路径构造逻辑", test_api_path_construction),
        ("完整流程", test_complete_flow),
        ("问题场景", test_problematic_scenarios)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        try:
            if test_func():
                print(f"✅ {test_name} - 通过")
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"菜单点击调试测试总结: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有测试通过！")
        print("\n📋 关键发现:")
        print("  ✅ 菜单key解析逻辑正确")
        print("  ✅ API路径构造逻辑正确")
        print("  ✅ 小红书菜单应该正确映射到xiaohongshu API")
        print("  ⚠️  问题可能在于:")
        print("    - URL参数中包含无效的type值")
        print("    - 前端状态管理异常")
        print("    - 浏览器缓存或历史记录问题")
        print("\n🔍 建议调试步骤:")
        print("  1. 检查浏览器控制台的调试输出")
        print("  2. 检查网络请求的实际URL")
        print("  3. 清除浏览器缓存和历史记录")
        print("  4. 检查前端状态变化")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关逻辑。")
        return False


if __name__ == "__main__":
    main()
