#!/usr/bin/env python3
"""
测试多平台总览功能
"""

import sys
import os
import asyncio
from datetime import datetime, date

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import SessionLocal
from app.models import PlatformAccount, WeChatMPUserChannel, WeChatChannelsFollowerData, XiaohongshuFansData
from app.services.data_details_service import DataDetailsService


def test_database_models():
    """测试数据库模型是否存在"""
    print("=== 测试数据库模型 ===")
    
    try:
        db = SessionLocal()
        
        # 测试查询各平台账号
        platforms = ["wechat_mp", "wechat_service", "wechat_channels", "xiaohongshu"]
        
        for platform in platforms:
            count = db.query(PlatformAccount).filter(
                PlatformAccount.platform == platform
            ).count()
            print(f"✅ {platform} 账号数量: {count}")
        
        # 测试数据表
        tables = [
            ("微信公众号用户数据", WeChatMPUserChannel),
            ("微信视频号关注者数据", WeChatChannelsFollowerData),
            ("小红书粉丝数据", XiaohongshuFansData)
        ]
        
        for table_name, model in tables:
            count = db.query(model).count()
            print(f"✅ {table_name} 记录数量: {count}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库模型测试失败: {e}")
        return False


def test_multi_platform_account_summary():
    """测试多平台账号汇总功能"""
    print("\n=== 测试多平台账号汇总功能 ===")
    
    try:
        db = SessionLocal()
        
        result = DataDetailsService.get_account_summary(db)
        
        if result['success']:
            print(f"✅ 多平台账号汇总获取成功")
            print(f"  数据条数: {len(result['data'])}")
            print(f"  日期列数: {len(result.get('date_columns', []))}")
            print(f"  数据日期范围: {result.get('data_date_range', 'N/A')}")
            
            # 检查是否包含多平台数据
            platforms_found = set()
            for record in result['data']:
                if 'platform' in record:
                    platforms_found.add(record['platform'])
                    
            print(f"  包含平台: {list(platforms_found)}")
            
            # 显示前几条数据
            for i, record in enumerate(result['data'][:3]):
                platform_name = {
                    'wechat_mp': '公众号',
                    'wechat_service': '公众号',
                    'wechat_channels': '视频号',
                    'xiaohongshu': '小红书'
                }.get(record.get('platform', ''), record.get('platform', ''))
                
                print(f"  记录 {i+1}: {record['account_name']} ({platform_name})")
            
            return True
        else:
            print(f"❌ 多平台账号汇总获取失败: {result.get('error', '未知错误')}")
            return False
        
        db.close()
        
    except Exception as e:
        print(f"❌ 多平台账号汇总测试失败: {e}")
        return False


def test_multi_platform_growth_summary():
    """测试多平台增长汇总功能"""
    print("\n=== 测试多平台增长汇总功能 ===")
    
    try:
        db = SessionLocal()
        
        result = DataDetailsService.get_growth_summary(db)
        
        if result['success']:
            print(f"✅ 多平台增长汇总获取成功")
            print(f"  数据条数: {len(result['data'])}")
            print(f"  用户来源类型数: {len(result.get('user_sources', []))}")
            print(f"  数据日期范围: {result.get('data_date_range', 'N/A')}")
            
            # 检查是否包含多平台数据
            platforms_found = set()
            for record in result['data']:
                if 'platform' in record:
                    platforms_found.add(record['platform'])
                    
            print(f"  包含平台: {list(platforms_found)}")
            
            # 显示前几条数据
            for i, record in enumerate(result['data'][:3]):
                platform_name = {
                    'wechat_mp': '公众号',
                    'wechat_service': '公众号',
                    'wechat_channels': '视频号',
                    'xiaohongshu': '小红书'
                }.get(record.get('platform', ''), record.get('platform', ''))
                
                print(f"  记录 {i+1}: {record['account_name']} ({platform_name})")
                print(f"    新增: {record.get('new_user', 0)}, 取消: {record.get('cancel_user', 0)}, 累积: {record.get('cumulate_user', 0)}")
            
            return True
        else:
            print(f"❌ 多平台增长汇总获取失败: {result.get('error', '未知错误')}")
            return False
        
        db.close()
        
    except Exception as e:
        print(f"❌ 多平台增长汇总测试失败: {e}")
        return False


def test_api_endpoints():
    """测试API端点"""
    print("\n=== 测试API端点 ===")
    
    try:
        import requests
        
        base_url = "http://localhost:8000/api/data-details"
        
        endpoints = [
            "/account-summary",
            "/growth-summary"
        ]
        
        results = []
        for endpoint in endpoints:
            try:
                response = requests.get(f"{base_url}{endpoint}", timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    if data.get('success'):
                        print(f"✅ {endpoint} API 正常")
                        results.append(True)
                    else:
                        print(f"❌ {endpoint} API 返回错误: {data.get('error', '未知错误')}")
                        results.append(False)
                elif response.status_code == 401:
                    print(f"⚠️  {endpoint} API 需要认证 (正常)")
                    results.append(True)  # 401是正常的，说明路由存在
                else:
                    print(f"❌ {endpoint} API 状态码: {response.status_code}")
                    results.append(False)
            except requests.exceptions.RequestException as e:
                print(f"❌ {endpoint} API 请求失败: {e}")
                results.append(False)
        
        return all(results)
        
    except Exception as e:
        print(f"❌ API端点测试失败: {e}")
        return False


def test_frontend_files():
    """测试前端文件"""
    print("\n=== 测试前端文件 ===")
    
    try:
        # 检查overviewService.ts
        overview_service_file = "frontend/src/services/overviewService.ts"
        
        if os.path.exists(overview_service_file):
            print(f"✅ 总览服务文件存在: {overview_service_file}")
            
            with open(overview_service_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
                required_elements = [
                    "getMultiPlatformAccountSummary",
                    "getMultiPlatformGrowthSummary",
                    "/data-details/account-summary",
                    "/data-details/growth-summary"
                ]
                
                missing_elements = []
                for element in required_elements:
                    if element not in content:
                        missing_elements.append(element)
                
                if not missing_elements:
                    print("✅ 总览服务文件包含所有必需元素")
                else:
                    print(f"❌ 总览服务文件缺失元素: {missing_elements}")
                    return False
        else:
            print(f"❌ 总览服务文件不存在: {overview_service_file}")
            return False
        
        # 检查DataDetails.tsx
        datadetails_file = "frontend/src/pages/DataDetails.tsx"
        
        if os.path.exists(datadetails_file):
            print(f"✅ DataDetails文件存在: {datadetails_file}")
            
            with open(datadetails_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
                required_elements = [
                    "overviewService",
                    "getMultiPlatformAccountSummary",
                    "getMultiPlatformGrowthSummary",
                    "平台",
                    "platformNames"
                ]
                
                missing_elements = []
                for element in required_elements:
                    if element not in content:
                        missing_elements.append(element)
                
                if not missing_elements:
                    print("✅ DataDetails文件包含所有必需元素")
                    return True
                else:
                    print(f"❌ DataDetails文件缺失元素: {missing_elements}")
                    return False
        else:
            print(f"❌ DataDetails文件不存在: {datadetails_file}")
            return False
        
    except Exception as e:
        print(f"❌ 前端文件测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 开始测试多平台总览功能")
    
    tests = [
        ("数据库模型", test_database_models, False),
        ("多平台账号汇总", test_multi_platform_account_summary, False),
        ("多平台增长汇总", test_multi_platform_growth_summary, False),
        ("API端点", test_api_endpoints, False),
        ("前端文件", test_frontend_files, False)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func, is_async in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        try:
            if is_async:
                result = await test_func()
            else:
                result = test_func()
                
            if result:
                print(f"✅ {test_name} - 通过")
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"多平台总览功能测试总结: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有测试通过！")
        print("\n📋 功能总结:")
        print("  ✅ 支持多平台数据汇总（公众号、视频号、小红书）")
        print("  ✅ 社媒账号数据汇总表显示所有平台")
        print("  ✅ 关注数合计净增长包含所有平台数据")
        print("  ✅ 前端界面支持多平台数据显示")
        print("  ✅ API端点正常工作")
        print("\n🚀 多平台总览功能已完成！")
        print("\n🔧 主要改进:")
        print("  1. 后端支持多平台数据查询")
        print("  2. 整合不同数据表的数据")
        print("  3. 前端显示平台标识")
        print("  4. 统一的数据汇总功能")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return False


if __name__ == "__main__":
    asyncio.run(main())
