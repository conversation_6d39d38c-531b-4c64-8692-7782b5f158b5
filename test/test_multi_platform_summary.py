#!/usr/bin/env python3
"""
简单验证多平台总览功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import SessionLocal
from app.services.data_details_service import DataDetailsService


def main():
    """验证多平台总览功能"""
    print("🚀 验证多平台总览功能")
    
    try:
        db = SessionLocal()
        
        print("\n=== 测试多平台账号汇总 ===")
        account_result = DataDetailsService.get_account_summary(db)
        
        if account_result['success']:
            print(f"✅ 账号汇总成功获取 {len(account_result['data'])} 条记录")
            
            # 统计各平台数量
            platform_counts = {}
            for record in account_result['data']:
                platform = record.get('platform', 'unknown')
                platform_counts[platform] = platform_counts.get(platform, 0) + 1
            
            print("📊 平台分布:")
            platform_names = {
                'wechat_mp': '微信公众号',
                'wechat_service': '微信公众号',
                'wechat_channels': '微信视频号',
                'xiaohongshu': '小红书'
            }
            
            for platform, count in platform_counts.items():
                name = platform_names.get(platform, platform)
                print(f"  {name}: {count} 个账号")
            
            print(f"📅 数据日期范围: {account_result.get('data_date_range', 'N/A')}")
            print(f"📊 日期列数: {len(account_result.get('date_columns', []))}")
        else:
            print(f"❌ 账号汇总失败: {account_result.get('error')}")
        
        print("\n=== 测试多平台增长汇总 ===")
        growth_result = DataDetailsService.get_growth_summary(db)
        
        if growth_result['success']:
            print(f"✅ 增长汇总成功获取 {len(growth_result['data'])} 条记录")
            
            # 统计各平台数量
            platform_counts = {}
            total_new = 0
            total_cancel = 0
            
            for record in growth_result['data']:
                platform = record.get('platform', 'unknown')
                platform_counts[platform] = platform_counts.get(platform, 0) + 1
                total_new += record.get('new_user', 0)
                total_cancel += record.get('cancel_user', 0)
            
            print("📊 平台分布:")
            for platform, count in platform_counts.items():
                name = platform_names.get(platform, platform)
                print(f"  {name}: {count} 个账号")
            
            print(f"📈 总新增用户: {total_new:,}")
            print(f"📉 总取消用户: {total_cancel:,}")
            print(f"📊 净增长: {total_new - total_cancel:,}")
            print(f"📅 数据日期范围: {growth_result.get('data_date_range', 'N/A')}")
        else:
            print(f"❌ 增长汇总失败: {growth_result.get('error')}")
        
        db.close()
        
        print("\n🎉 多平台总览功能验证完成！")
        print("\n📋 功能特点:")
        print("  ✅ 整合微信公众号、视频号、小红书数据")
        print("  ✅ 统一的数据汇总接口")
        print("  ✅ 支持多平台数据对比")
        print("  ✅ 自动识别不同平台的数据字段")
        print("\n🔧 数据来源:")
        print("  📱 微信公众号: WeChatMPUserChannel 表")
        print("  📺 微信视频号: WeChatChannelsFollowerData 表")
        print("  📝 小红书: XiaohongshuFansData 表")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False


if __name__ == "__main__":
    main()
