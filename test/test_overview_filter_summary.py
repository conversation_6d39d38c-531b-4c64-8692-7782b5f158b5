#!/usr/bin/env python3
"""
测试总览页面过滤和合计功能
"""

import os

def test_frontend_filter_functionality():
    """测试前端过滤功能"""
    print("=== 测试前端过滤功能 ===")
    
    try:
        datadetails_file = "frontend/src/pages/DataDetails.tsx"
        
        if os.path.exists(datadetails_file):
            print(f"✅ DataDetails文件存在")
            
            with open(datadetails_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # 检查过滤功能相关代码
                filter_checks = [
                    ("平台过滤状态", "platformFilter, setPlatformFilter"),
                    ("过滤函数", "filterDataByPlatform"),
                    ("过滤逻辑-全部", "platformFilter === 'all'"),
                    ("过滤逻辑-公众号", "wechat_mp' || item.platform === 'wechat_service'"),
                    ("过滤器UI", "账号类型："),
                    ("过滤选项-全部", "value: 'all', label: '全部'"),
                    ("过滤选项-公众号", "value: 'wechat_mp', label: '公众号'"),
                    ("过滤选项-视频号", "value: 'wechat_channels', label: '视频号'"),
                    ("过滤选项-小红书", "value: 'xiaohongshu', label: '小红书'")
                ]
                
                all_passed = True
                for check_name, check_content in filter_checks:
                    if check_content in content:
                        print(f"✅ {check_name}: 通过")
                    else:
                        print(f"❌ {check_name}: 失败")
                        all_passed = False
                
                return all_passed
        else:
            print(f"❌ DataDetails文件不存在")
            return False
        
    except Exception as e:
        print(f"❌ 前端过滤功能测试失败: {e}")
        return False


def test_frontend_summary_functionality():
    """测试前端合计功能"""
    print("\n=== 测试前端合计功能 ===")
    
    try:
        datadetails_file = "frontend/src/pages/DataDetails.tsx"
        
        with open(datadetails_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
            # 检查合计功能相关代码
            summary_checks = [
                ("表格合计属性", "summary={(pageData) =>"),
                ("合计行组件", "Table.Summary.Row"),
                ("合计单元格", "Table.Summary.Cell"),
                ("合计标题", "<strong>合计</strong>"),
                ("账号数量合计", "个账号</strong>"),
                ("数值合计计算", ".reduce((sum, item) => sum +"),
                ("数值格式化", ".toLocaleString()"),
                ("过滤数据应用", "filterDataByPlatform(accountSummary.data)"),
                ("增长数据合计", "totalNew.toLocaleString()"),
                ("用户来源合计", "source_${source}")
            ]
            
            all_passed = True
            for check_name, check_content in summary_checks:
                if check_content in content:
                    print(f"✅ {check_name}: 通过")
                else:
                    print(f"❌ {check_name}: 失败")
                    all_passed = False
            
            return all_passed
        
    except Exception as e:
        print(f"❌ 前端合计功能测试失败: {e}")
        return False


def test_ui_layout():
    """测试UI布局"""
    print("\n=== 测试UI布局 ===")
    
    try:
        datadetails_file = "frontend/src/pages/DataDetails.tsx"
        
        with open(datadetails_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
            # 检查UI布局相关代码
            layout_checks = [
                ("顶部布局", "display: 'flex', justifyContent: 'space-between'"),
                ("标题样式", "margin: 0"),
                ("过滤器容器", "display: 'flex', alignItems: 'center', gap: 8"),
                ("过滤器标签", "账号类型："),
                ("过滤器宽度", "width: 120"),
                ("过滤器选项", "options={["),
                ("表格应用过滤", "dataSource={filterDataByPlatform("),
                ("两个表格都应用", content.count("filterDataByPlatform(") >= 4)  # 至少4次调用
            ]
            
            all_passed = True
            for check_name, check_content in layout_checks:
                if isinstance(check_content, bool):
                    result = check_content
                else:
                    result = check_content in content
                
                if result:
                    print(f"✅ {check_name}: 通过")
                else:
                    print(f"❌ {check_name}: 失败")
                    all_passed = False
            
            return all_passed
        
    except Exception as e:
        print(f"❌ UI布局测试失败: {e}")
        return False


def test_data_integration():
    """测试数据整合"""
    print("\n=== 测试数据整合 ===")
    
    try:
        datadetails_file = "frontend/src/pages/DataDetails.tsx"
        
        with open(datadetails_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
            # 检查数据整合相关代码
            integration_checks = [
                ("过滤器状态管理", "useState<string>('all')"),
                ("过滤函数定义", "const filterDataByPlatform = (data: any[])"),
                ("公众号合并逻辑", "wechat_mp' || item.platform === 'wechat_service'"),
                ("账号汇总表过滤", "filterDataByPlatform(accountSummary.data)"),
                ("增长汇总表过滤", "filterDataByPlatform(growthSummary.data)"),
                ("账号汇总合计", "accountSummary.date_columns?.map"),
                ("增长汇总合计", "totalNew.toLocaleString()"),
                ("用户来源合计", "growthSummary.user_sources?.map")
            ]
            
            all_passed = True
            for check_name, check_content in integration_checks:
                if check_content in content:
                    print(f"✅ {check_name}: 通过")
                else:
                    print(f"❌ {check_name}: 失败")
                    all_passed = False
            
            return all_passed
        
    except Exception as e:
        print(f"❌ 数据整合测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 测试总览页面过滤和合计功能")
    
    tests = [
        ("前端过滤功能", test_frontend_filter_functionality),
        ("前端合计功能", test_frontend_summary_functionality),
        ("UI布局", test_ui_layout),
        ("数据整合", test_data_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            if result:
                print(f"✅ {test_name} - 通过")
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"总览过滤和合计功能测试总结: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 总览过滤和合计功能完成！")
        print("\n📋 功能总结:")
        print("  ✅ 顶部添加账号类型过滤器")
        print("  ✅ 支持全部、公众号、视频号、小红书过滤")
        print("  ✅ 两个表格同时应用过滤")
        print("  ✅ 底部显示各项合计")
        print("  ✅ 合计行实时更新")
        print("\n🚀 用户体验:")
        print("  📊 一键过滤查看特定平台数据")
        print("  📈 实时查看过滤后的数据汇总")
        print("  📋 清晰的合计信息展示")
        print("  🎯 统一的过滤控制")
        print("\n🔧 技术实现:")
        print("  - 状态管理：platformFilter")
        print("  - 过滤函数：filterDataByPlatform")
        print("  - 合计计算：Table.Summary组件")
        print("  - UI布局：Flex布局 + Select组件")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return False


if __name__ == "__main__":
    main()
