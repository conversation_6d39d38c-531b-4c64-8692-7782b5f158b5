#!/usr/bin/env python3
"""
测试总览菜单提取功能
"""

import os

def test_menu_structure():
    """测试菜单结构修改"""
    print("=== 测试菜单结构修改 ===")
    
    try:
        datadetails_file = "frontend/src/pages/DataDetails.tsx"
        
        if os.path.exists(datadetails_file):
            print(f"✅ DataDetails文件存在")
            
            with open(datadetails_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # 检查总览菜单是否提取到顶层
                checks = [
                    ("总览菜单独立", "key: 'overview',\n      label: '总览'"),
                    ("微信公众号菜单不包含总览", "{ key: 'wechat_mp_overview', label: '总览' }" not in content),
                    ("默认选择总览", "initialPlatform = searchParams.get('platform') || 'overview'"),
                    ("默认数据类型为总览", "initialDataType = searchParams.get('type') || 'overview'"),
                    ("菜单点击处理总览", "if (key === 'overview') {"),
                    ("总览状态设置", "setSelectedPlatform('overview')"),
                    ("菜单选中状态处理", "selectedPlatform === 'overview' ? ['overview']"),
                    ("总览视图渲染", "selectedDataType === 'overview'")
                ]
                
                all_passed = True
                for check_name, check_content in checks:
                    if isinstance(check_content, bool):
                        # 对于布尔检查，直接使用结果
                        result = check_content
                    else:
                        # 对于字符串检查，检查是否存在
                        result = check_content in content
                    
                    if result:
                        print(f"✅ {check_name}: 通过")
                    else:
                        print(f"❌ {check_name}: 失败")
                        all_passed = False
                
                return all_passed
        else:
            print(f"❌ DataDetails文件不存在")
            return False
        
    except Exception as e:
        print(f"❌ 菜单结构测试失败: {e}")
        return False


def test_menu_items_structure():
    """测试菜单项结构"""
    print("\n=== 测试菜单项结构 ===")
    
    try:
        datadetails_file = "frontend/src/pages/DataDetails.tsx"
        
        with open(datadetails_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
            # 检查菜单项的正确结构
            structure_checks = [
                ("总览菜单在顶部", content.find("key: 'overview'") < content.find("key: 'wechat_mp'")),
                ("微信公众号菜单存在", "key: 'wechat_mp'" in content),
                ("视频号菜单存在", "key: 'wechat_channels'" in content),
                ("小红书菜单存在", "key: 'xiaohongshu'" in content),
                ("微信公众号子菜单正确", "key: 'wechat_mp_content_trend'" in content),
                ("视频号子菜单正确", "key: 'wechat_channels_follower_data'" in content),
                ("小红书子菜单正确", "key: 'xiaohongshu_account_overview'" in content)
            ]
            
            all_passed = True
            for check_name, check_result in structure_checks:
                if check_result:
                    print(f"✅ {check_name}: 通过")
                else:
                    print(f"❌ {check_name}: 失败")
                    all_passed = False
            
            return all_passed
        
    except Exception as e:
        print(f"❌ 菜单项结构测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 测试总览菜单提取功能")
    
    tests = [
        ("菜单结构修改", test_menu_structure),
        ("菜单项结构", test_menu_items_structure)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            if result:
                print(f"✅ {test_name} - 通过")
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"总览菜单提取测试总结: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 总览菜单提取完成！")
        print("\n📋 修改总结:")
        print("  ✅ 总览菜单从微信公众号下提取到顶层")
        print("  ✅ 总览成为独立的顶级菜单项")
        print("  ✅ 默认选中总览菜单")
        print("  ✅ 菜单点击逻辑正确处理总览")
        print("  ✅ 菜单选中状态正确显示")
        print("  ✅ 总览视图正常渲染")
        print("\n🚀 现在的菜单结构:")
        print("  📊 总览 (顶级菜单，默认选中)")
        print("  📱 微信公众号")
        print("    - 内容数据趋势明细")
        print("    - 内容流量来源明细")
        print("    - 内容已通知内容明细")
        print("    - 用户增长明细")
        print("    - 用户来源明细")
        print("  📺 视频号")
        print("    - 关注者数据")
        print("    - 单篇视频数据")
        print("  📝 小红书")
        print("    - 账号概览")
        print("    - 粉丝数据")
        print("    - 笔记数据")
        print("\n💡 用户体验:")
        print("  - 进入页面默认显示总览")
        print("  - 总览菜单位于顶部，易于访问")
        print("  - 各平台数据明细分类清晰")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return False


if __name__ == "__main__":
    main()
