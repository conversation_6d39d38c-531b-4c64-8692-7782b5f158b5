#!/usr/bin/env python3
"""
测试暂停和恢复功能
"""
import asyncio
import sys
import os
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_pause_resume():
    """测试暂停和恢复功能"""
    print("🔍 测试暂停和恢复功能...")
    
    try:
        from app.background.login_state_keeper import get_login_keeper_scheduler
        from app.config.keeper_config import is_keeper_enabled
        
        scheduler = get_login_keeper_scheduler()
        
        print("🚀 初始化并启动调度器...")
        scheduler.initialize()
        scheduler.start()
        
        print("\n📊 启动后的状态:")
        job_status = scheduler.get_job_status()
        print(json.dumps(job_status, indent=2, ensure_ascii=False, default=str))
        
        # 分析前端状态
        is_running = job_status["scheduler_running"] and job_status["job_exists"] and not job_status["job_paused"]
        print(f"前端显示状态: {'运行中' if is_running else '已停止'}")
        
        print("\n⏸️ 暂停任务...")
        pause_success = scheduler.pause_job()
        print(f"暂停结果: {pause_success}")
        
        print("\n📊 暂停后的状态:")
        job_status = scheduler.get_job_status()
        print(json.dumps(job_status, indent=2, ensure_ascii=False, default=str))
        
        # 分析前端状态
        is_running = job_status["scheduler_running"] and job_status["job_exists"] and not job_status["job_paused"]
        print(f"前端显示状态: {'运行中' if is_running else '已停止'}")
        
        print("\n▶️ 测试启动服务API逻辑...")
        # 模拟启动服务API的逻辑
        if job_status["scheduler_running"] and job_status["job_exists"] and job_status["job_paused"]:
            print("检测到任务被暂停，将调用恢复任务...")
            resume_success = scheduler.resume_job()
            print(f"恢复结果: {resume_success}")
            
            print("\n📊 恢复后的状态:")
            job_status = scheduler.get_job_status()
            print(json.dumps(job_status, indent=2, ensure_ascii=False, default=str))
            
            # 分析前端状态
            is_running = job_status["scheduler_running"] and job_status["job_exists"] and not job_status["job_paused"]
            print(f"前端显示状态: {'运行中' if is_running else '已停止'}")
        else:
            print("将调用启动调度器...")
            scheduler.start()
        
        print("\n🛑 停止调度器...")
        scheduler.stop()
        
        print("\n📊 停止后的状态:")
        job_status = scheduler.get_job_status()
        print(json.dumps(job_status, indent=2, ensure_ascii=False, default=str))
        
        # 分析前端状态
        is_running = job_status["scheduler_running"] and job_status["job_exists"] and not job_status["job_paused"]
        print(f"前端显示状态: {'运行中' if is_running else '已停止'}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_pause_resume())
