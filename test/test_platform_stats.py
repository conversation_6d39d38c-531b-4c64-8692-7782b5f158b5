#!/usr/bin/env python3
"""
测试平台统计功能
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_platform_stats():
    """测试平台统计功能"""
    print("🔍 测试平台统计功能...")
    
    try:
        from app.services.login_keeper_service import LoginKeeperService
        
        # 创建维持服务
        keeper = LoginKeeperService()
        
        print("📊 测试获取平台统计...")
        stats = keeper.get_platform_stats(7)
        
        print(f"✅ 平台统计获取成功:")
        print(f"  - 统计天数: 7")
        print(f"  - 平台数量: {len(stats)}")
        
        for platform, platform_stats in stats.items():
            print(f"  - 平台 {platform}:")
            print(f"    * 总尝试: {platform_stats['total_attempts']}")
            print(f"    * 成功: {platform_stats['successful']}")
            print(f"    * 失败: {platform_stats['failed']}")
            print(f"    * 过期: {platform_stats['expired']}")
            print(f"    * 成功率: {platform_stats['success_rate']:.1f}%")
            print(f"    * 平均响应时间: {platform_stats['avg_response_time']:.2f}s")
        
        print("\n📊 测试获取账号统计...")
        # 测试账号统计（使用账号ID 5）
        account_stats = keeper.get_account_stats(5, 7)
        
        print(f"✅ 账号统计获取成功:")
        print(f"  - 账号ID: {account_stats.get('account_id', 'N/A')}")
        print(f"  - 统计天数: {account_stats.get('days', 'N/A')}")
        print(f"  - 总尝试: {account_stats.get('total_attempts', 0)}")
        print(f"  - 成功: {account_stats.get('successful', 0)}")
        print(f"  - 失败: {account_stats.get('failed', 0)}")
        print(f"  - 过期: {account_stats.get('expired', 0)}")
        print(f"  - 成功率: {account_stats.get('success_rate', 0):.1f}%")
        print(f"  - 平均响应时间: {account_stats.get('avg_response_time', 0):.2f}s")
        
        print("\n📊 测试获取最近记录...")
        records = keeper.get_recent_records(5)
        
        print(f"✅ 最近记录获取成功:")
        print(f"  - 记录数量: {len(records)}")
        
        for i, record in enumerate(records[:3]):  # 只显示前3条
            print(f"  - 记录 {i+1}:")
            print(f"    * 账号: {record.get('account_name', 'N/A')}")
            print(f"    * 平台: {record.get('platform', 'N/A')}")
            print(f"    * 状态: {record.get('status', 'N/A')}")
            print(f"    * 时间: {record.get('created_at', 'N/A')}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_platform_stats())
