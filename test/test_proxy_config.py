#!/usr/bin/env python3
"""
测试代理配置的脚本
"""

import requests
import time

def test_direct_backend():
    """测试直接访问后端"""
    
    print("=== 测试直接访问后端 ===")
    
    try:
        response = requests.get("http://localhost:8000/", timeout=10)
        if response.status_code == 200:
            print("✅ 后端直接访问成功")
            return True
        else:
            print(f"⚠️  后端访问状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 后端直接访问失败: {e}")
        return False

def test_frontend_proxy():
    """测试前端代理"""
    
    print("\n=== 测试前端代理 ===")
    
    # 测试通过前端代理访问API
    proxy_urls = [
        "http://localhost:3000/api/",
        "http://sm.dev.mynatapp.cc/api/",
    ]
    
    results = []
    for url in proxy_urls:
        try:
            print(f"\n🔍 测试代理: {url}")
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                print(f"✅ {url} - 代理成功")
                results.append(True)
            else:
                print(f"⚠️  {url} - 状态码: {response.status_code}")
                results.append(False)
                
        except requests.exceptions.ConnectionError:
            print(f"❌ {url} - 连接失败")
            results.append(False)
        except Exception as e:
            print(f"❌ {url} - 错误: {e}")
            results.append(False)
    
    return any(results)

def test_frontend_access():
    """测试前端页面访问"""
    
    print("\n=== 测试前端页面访问 ===")
    
    frontend_urls = [
        "http://localhost:3000",
        "http://sm.dev.mynatapp.cc",
    ]
    
    results = []
    for url in frontend_urls:
        try:
            print(f"\n🔍 测试前端: {url}")
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                print(f"✅ {url} - 前端访问成功")
                results.append(True)
            else:
                print(f"⚠️  {url} - 状态码: {response.status_code}")
                if "Invalid Host header" in response.text:
                    print("❌ 仍然存在Host header问题")
                results.append(False)
                
        except Exception as e:
            print(f"❌ {url} - 错误: {e}")
            results.append(False)
    
    return any(results)

def check_container_network():
    """检查容器网络连接"""
    
    print("\n=== 检查容器网络 ===")
    
    import subprocess
    
    try:
        # 检查容器状态
        result = subprocess.run(['docker', 'compose', 'ps'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("📊 容器状态:")
            print(result.stdout)
            
            # 检查网络连接
            print("\n🔍 测试容器间网络连接:")
            network_test = subprocess.run([
                'docker', 'compose', 'exec', '-T', 'frontend', 
                'wget', '-q', '--spider', 'http://backend:8000/'
            ], capture_output=True, text=True, timeout=10)
            
            if network_test.returncode == 0:
                print("✅ 前端容器可以访问后端容器")
                return True
            else:
                print("❌ 前端容器无法访问后端容器")
                print(f"错误: {network_test.stderr}")
                return False
        else:
            print("❌ 无法获取容器状态")
            return False
            
    except Exception as e:
        print(f"❌ 检查容器网络时发生错误: {e}")
        return False

def main():
    """主函数"""
    
    print("🔧 开始测试代理配置...")
    print("请确保开发环境已启动")
    
    # 等待服务启动
    print("\n⏳ 等待5秒让服务完全启动...")
    time.sleep(5)
    
    # 执行测试
    backend_ok = test_direct_backend()
    network_ok = check_container_network()
    frontend_ok = test_frontend_access()
    proxy_ok = test_frontend_proxy()
    
    # 总结
    print(f"\n{'='*60}")
    print("🔧 代理配置测试结果:")
    print(f"后端直接访问: {'✅ 正常' if backend_ok else '❌ 异常'}")
    print(f"容器网络连接: {'✅ 正常' if network_ok else '❌ 异常'}")
    print(f"前端页面访问: {'✅ 正常' if frontend_ok else '❌ 异常'}")
    print(f"前端API代理: {'✅ 正常' if proxy_ok else '❌ 异常'}")
    
    if all([backend_ok, network_ok, frontend_ok, proxy_ok]):
        print("\n🎉 所有代理配置测试通过！")
        print("\n📋 配置说明:")
        print("- 前端页面: http://localhost:3000 或 http://sm.dev.mynatapp.cc")
        print("- API代理: /api/* -> http://backend:8000/api/*")
        print("- Host检查: 已禁用，支持外部域名访问")
        return 0
    else:
        print("\n⚠️  部分配置测试失败")
        print("\n🔧 故障排除建议:")
        
        if not backend_ok:
            print("- 检查后端容器是否正常运行")
        if not network_ok:
            print("- 检查Docker网络配置")
        if not frontend_ok:
            print("- 检查前端Host配置")
        if not proxy_ok:
            print("- 检查setupProxy.js配置")
            print("- 确认package.json中没有错误的proxy配置")
        
        print("\n📝 重启建议:")
        print("docker compose -f docker-compose.dev.yml down")
        print("./scripts/dev.sh")
        
        return 1

if __name__ == "__main__":
    exit(main())
