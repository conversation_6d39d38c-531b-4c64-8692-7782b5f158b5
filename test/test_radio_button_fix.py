#!/usr/bin/env python3
"""
测试单选按钮修复
"""

import sys
import os
import asyncio
from datetime import datetime, date

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import SessionLocal
from app.models import PlatformAccount
from app.services.wechat_channels_service import WeChatChannelsService


async def test_radio_button_methods():
    """测试单选按钮点击方法"""
    print("=== 测试单选按钮点击方法 ===")
    
    try:
        # 创建服务实例
        service = WeChatChannelsService(account_id=999, headless=True)
        
        # 初始化浏览器
        await service._init_browser()
        context = await service._create_persistent_context()
        page = await context.new_page()
        
        # 创建一个测试页面，模拟隐藏的单选按钮
        test_html = """
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                .hidden-radio {
                    position: absolute;
                    left: -9999em;
                    opacity: 0;
                }
                .visible-label {
                    display: inline-block;
                    padding: 10px;
                    background: #f0f0f0;
                    cursor: pointer;
                    margin: 5px;
                }
                .visible-label:hover {
                    background: #e0e0e0;
                }
            </style>
        </head>
        <body>
            <div class="weui-desktop-radio-group radio-group">
                <input type="radio" name="timeRange" value="1" class="hidden-radio" id="radio1">
                <label for="radio1" class="visible-label">近7天</label>
                
                <input type="radio" name="timeRange" value="2" class="hidden-radio" id="radio2">
                <label for="radio2" class="visible-label">近30天</label>
                
                <input type="radio" name="timeRange" value="3" class="hidden-radio" id="radio3">
                <label for="radio3" class="visible-label">近90天</label>
            </div>
            
            <div id="result"></div>
            
            <script>
                // 监听单选按钮变化
                document.querySelectorAll('input[type="radio"]').forEach(radio => {
                    radio.addEventListener('change', function() {
                        document.getElementById('result').textContent = 
                            '选中的值: ' + this.value + ' (' + this.nextElementSibling.textContent + ')';
                    });
                });
            </script>
        </body>
        </html>
        """
        
        # 设置页面内容
        await page.set_content(test_html)
        await asyncio.sleep(1)
        
        print("✅ 测试页面创建成功")
        
        # 测试方法1: JavaScript设置
        print("测试方法1: JavaScript设置单选按钮")
        js_code = """
        () => {
            const radio = document.querySelector('input[type="radio"][value="2"]');
            if (radio) {
                radio.checked = true;
                radio.dispatchEvent(new Event('change', { bubbles: true }));
                return true;
            }
            return false;
        }
        """
        
        result1 = await page.evaluate(js_code)
        if result1:
            # 检查结果
            result_text = await page.evaluate('() => document.getElementById("result").textContent')
            if "近30天" in result_text:
                print("✅ JavaScript方法成功")
            else:
                print(f"❌ JavaScript方法失败，结果: {result_text}")
        else:
            print("❌ JavaScript方法失败")
        
        # 重置
        await page.evaluate('() => document.querySelectorAll("input[type=radio]").forEach(r => r.checked = false)')
        await page.evaluate('() => document.getElementById("result").textContent = ""')
        
        # 测试方法2: 点击标签
        print("测试方法2: 点击可见标签")
        try:
            label = await page.wait_for_selector('label[for="radio2"]', timeout=2000)
            await label.click()
            await asyncio.sleep(0.5)
            
            result_text = await page.evaluate('() => document.getElementById("result").textContent')
            if "近30天" in result_text:
                print("✅ 标签点击方法成功")
            else:
                print(f"❌ 标签点击方法失败，结果: {result_text}")
        except Exception as e:
            print(f"❌ 标签点击方法失败: {e}")
        
        # 重置
        await page.evaluate('() => document.querySelectorAll("input[type=radio]").forEach(r => r.checked = false)')
        await page.evaluate('() => document.getElementById("result").textContent = ""')
        
        # 测试方法3: 强制点击隐藏元素
        print("测试方法3: 强制点击隐藏元素")
        force_click_js = """
        () => {
            const radio = document.querySelector('input[type="radio"][value="2"]');
            if (radio) {
                const originalStyle = radio.style.cssText;
                radio.style.cssText = 'position: static !important; left: auto !important; opacity: 1 !important;';
                radio.click();
                radio.style.cssText = originalStyle;
                return true;
            }
            return false;
        }
        """
        
        result3 = await page.evaluate(force_click_js)
        if result3:
            await asyncio.sleep(0.5)
            result_text = await page.evaluate('() => document.getElementById("result").textContent')
            if "近30天" in result_text:
                print("✅ 强制点击方法成功")
            else:
                print(f"❌ 强制点击方法失败，结果: {result_text}")
        else:
            print("❌ 强制点击方法失败")
        
        # 清理资源
        await service.close()
        print("✅ 资源清理完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 单选按钮测试失败: {e}")
        return False


def test_service_methods():
    """测试服务方法是否存在"""
    print("\n=== 测试服务方法 ===")
    
    try:
        # 检查修复后的方法
        service_methods = dir(WeChatChannelsService)
        
        required_methods = [
            'get_follower_data',
            '_download_follower_csv',
            '_parse_follower_csv',
            '_import_follower_to_database',
            '_parse_number'
        ]
        
        missing_methods = []
        for method in required_methods:
            if method in service_methods:
                print(f"✅ 方法存在: {method}")
            else:
                missing_methods.append(method)
                print(f"❌ 方法缺失: {method}")
        
        if missing_methods:
            print(f"❌ 缺失方法: {missing_methods}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 服务方法测试失败: {e}")
        return False


def test_javascript_code_syntax():
    """测试JavaScript代码语法"""
    print("\n=== 测试JavaScript代码语法 ===")
    
    try:
        # 测试JavaScript代码片段
        js_snippets = [
            # 基本设置单选按钮
            """
            () => {
                const radio = document.querySelector('input[type="radio"][value="2"]');
                if (radio) {
                    radio.checked = true;
                    radio.dispatchEvent(new Event('change', { bubbles: true }));
                    return true;
                }
                return false;
            }
            """,
            
            # 强制点击
            """
            () => {
                const radio = document.querySelector('input[type="radio"][value="2"]');
                if (radio) {
                    const originalStyle = radio.style.cssText;
                    radio.style.cssText = 'position: static !important; left: auto !important; opacity: 1 !important;';
                    radio.click();
                    radio.style.cssText = originalStyle;
                    return true;
                }
                return false;
            }
            """
        ]
        
        for i, js_code in enumerate(js_snippets, 1):
            try:
                # 简单的语法检查（通过编译测试）
                compile(js_code, f'<js_snippet_{i}>', 'eval')
                print(f"✅ JavaScript代码片段 {i} 语法正确")
            except SyntaxError as e:
                print(f"❌ JavaScript代码片段 {i} 语法错误: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ JavaScript代码语法测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 开始测试单选按钮修复")
    
    tests = [
        ("服务方法", test_service_methods, False),
        ("JavaScript代码语法", test_javascript_code_syntax, False),
        ("单选按钮点击方法", test_radio_button_methods, True)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func, is_async in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        try:
            if is_async:
                result = await test_func()
            else:
                result = test_func()
                
            if result:
                print(f"✅ {test_name} - 通过")
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"单选按钮修复测试总结: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有测试通过！")
        print("\n📋 修复总结:")
        print("  ✅ 服务方法完整")
        print("  ✅ JavaScript代码语法正确")
        print("  ✅ 单选按钮点击方法有效")
        print("\n🚀 单选按钮修复完成！")
        print("\n🔧 修复方法:")
        print("  1. JavaScript直接设置 - 最可靠")
        print("  2. 点击可见标签 - 备用方案")
        print("  3. 强制点击隐藏元素 - 最后手段")
        print("\n💡 解决方案:")
        print("  - 使用JavaScript绕过CSS隐藏限制")
        print("  - 触发多种事件确保状态更新")
        print("  - 提供多种备用方案增加成功率")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return False


if __name__ == "__main__":
    asyncio.run(main())
