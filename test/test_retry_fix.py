#!/usr/bin/env python3
"""
测试重试机制修复
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_single_account():
    """测试单个账号的维持逻辑"""
    print("🔍 测试单个账号的维持逻辑...")
    
    try:
        from app.database import SessionLocal
        from app.models import PlatformAccount
        from app.services.login_keeper_service import LoginKeeperService
        
        # 获取一个测试账号（不管登录状态）
        db = SessionLocal()
        try:
            account = db.query(PlatformAccount).first()

            if not account:
                print("❌ 没有找到账号进行测试")
                return

            print(f"🧪 测试账号: {account.name} (ID: {account.id}, 平台: {account.platform})")
            print(f"   当前登录状态: {account.login_status}")

        finally:
            db.close()
        
        # 创建维持服务
        keeper = LoginKeeperService()
        
        print("📝 开始维持账号登录状态...")
        start_time = asyncio.get_event_loop().time()
        
        # 测试单个账号维持
        result = await keeper._maintain_account_login(account)
        
        end_time = asyncio.get_event_loop().time()
        duration = end_time - start_time
        
        print(f"⏱️  维持耗时: {duration:.2f} 秒")
        print(f"📊 维持结果:")
        print(f"  - 成功: {result.get('success', False)}")
        print(f"  - 消息: {result.get('message', 'N/A')}")
        print(f"  - 重试次数: {result.get('retry_count', 0)}")
        print(f"  - 登录状态已更新: {result.get('login_status_updated', False)}")
        print(f"  - 响应时间: {result.get('response_time', 0):.2f} 秒")
        
        # 检查数据库状态
        db = SessionLocal()
        try:
            # 重新查询账号而不是刷新
            updated_account = db.query(PlatformAccount).filter(
                PlatformAccount.id == account.id
            ).first()

            if updated_account:
                print(f"📊 数据库状态:")
                print(f"  - 登录状态: {updated_account.login_status}")
                print(f"  - 最后登录时间: {updated_account.last_login_time}")
            else:
                print("❌ 无法找到更新后的账号")
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_single_account())
