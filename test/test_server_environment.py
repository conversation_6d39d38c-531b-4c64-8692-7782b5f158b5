#!/usr/bin/env python3
"""
测试服务器环境下的视频号数据下载功能
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.wechat_channels_service import WeChatChannelsService


async def test_environment_detection():
    """测试环境检测功能"""
    print("=== 环境检测测试 ===")
    
    service = WeChatChannelsService(account_id=999, headless=True)
    
    try:
        # 测试环境检测
        is_docker_wsl = service._is_docker_or_wsl_environment()
        can_use_gui = service._can_use_gui()
        
        print(f"Docker/WSL环境: {is_docker_wsl}")
        print(f"GUI支持: {can_use_gui}")
        
        # 检查系统信息
        import platform
        system_info = platform.uname()
        print(f"系统: {system_info.system}")
        print(f"版本: {system_info.release}")
        print(f"架构: {system_info.machine}")
        
        # 检查特殊文件
        docker_env = os.path.exists('/.dockerenv')
        print(f"Docker环境文件: {docker_env}")
        
        # 检查/proc/version
        try:
            with open('/proc/version', 'r') as f:
                version_info = f.read().strip()
                print(f"内核版本: {version_info}")
        except:
            print("无法读取/proc/version")
        
        # 检查环境变量
        wsl_distro = os.environ.get('WSL_DISTRO_NAME')
        docker_container = os.environ.get('DOCKER_CONTAINER')
        print(f"WSL_DISTRO_NAME: {wsl_distro}")
        print(f"DOCKER_CONTAINER: {docker_container}")
        
    except Exception as e:
        print(f"❌ 环境检测测试失败: {e}")
    finally:
        await service.close()


async def test_network_connectivity():
    """测试网络连接功能"""
    print("\n=== 网络连接测试 ===")
    
    service = WeChatChannelsService(account_id=999, headless=True)
    
    try:
        # 初始化浏览器
        await service._init_browser()
        service.context = await service._create_persistent_context()
        service.page = await service.context.new_page()
        
        # 测试网络连接
        network_ok = await service._check_network_connectivity()
        print(f"网络连接状态: {'正常' if network_ok else '异常'}")
        
        if network_ok:
            print("✅ 网络连接测试通过")
        else:
            print("❌ 网络连接测试失败")
            
    except Exception as e:
        print(f"❌ 网络连接测试出错: {e}")
    finally:
        await service.close()


async def test_login_check_with_retry():
    """测试带重试的登录检查"""
    print("\n=== 登录状态检查测试 ===")
    
    service = WeChatChannelsService(account_id=999, headless=True)
    
    try:
        # 加载登录状态
        if await service.load_login_state():
            print("✅ 登录状态加载成功")
            
            # 测试登录检查（带重试）
            login_valid = await service.check_existing_login()
            print(f"登录状态: {'有效' if login_valid else '无效'}")
            
            if login_valid:
                print("✅ 登录状态检查通过")
            else:
                print("❌ 登录状态检查失败")
        else:
            print("❌ 登录状态加载失败")
            
    except Exception as e:
        print(f"❌ 登录状态检查测试出错: {e}")
    finally:
        await service.close()


async def test_browser_startup():
    """测试浏览器启动"""
    print("\n=== 浏览器启动测试 ===")
    
    service = WeChatChannelsService(account_id=999, headless=True)
    
    try:
        # 测试浏览器启动
        await service._init_browser()
        
        if service.browser:
            print("✅ 浏览器启动成功")
            print(f"浏览器类型: {type(service.browser)}")
            print(f"浏览器连接状态: {service.browser.is_connected()}")
            
            # 测试上下文创建
            service.context = await service._create_persistent_context()
            if service.context:
                print("✅ 浏览器上下文创建成功")
                
                # 测试页面创建
                service.page = await service.context.new_page()
                if service.page:
                    print("✅ 页面创建成功")
                else:
                    print("❌ 页面创建失败")
            else:
                print("❌ 浏览器上下文创建失败")
        else:
            print("❌ 浏览器启动失败")
            
    except Exception as e:
        print(f"❌ 浏览器启动测试出错: {e}")
    finally:
        await service.close()


async def main():
    """主测试函数"""
    print("开始测试服务器环境下的视频号功能...\n")
    
    # 环境检测测试
    await test_environment_detection()
    
    # 浏览器启动测试
    await test_browser_startup()
    
    # 网络连接测试
    await test_network_connectivity()
    
    # 登录状态检查测试
    await test_login_check_with_retry()
    
    print("\n=== 测试完成 ===")
    print("如果网络连接测试失败，可能的原因:")
    print("1. Docker容器网络配置问题")
    print("2. WSL2网络代理设置问题")
    print("3. 防火墙阻止了外网访问")
    print("4. DNS解析问题")
    print("\n建议解决方案:")
    print("1. 检查Docker网络配置")
    print("2. 配置正确的DNS服务器")
    print("3. 检查代理设置")
    print("4. 增加更长的超时时间")


if __name__ == "__main__":
    asyncio.run(main())
