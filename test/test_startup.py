#!/usr/bin/env python3
"""
测试登录状态维持服务启动
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_startup():
    """测试服务启动"""
    try:
        print("🔍 测试登录状态维持服务启动...")
        
        # 测试导入
        from app.background.login_state_keeper import start_login_keeper, stop_login_keeper
        print("✅ 导入成功")
        
        # 测试启动
        await start_login_keeper()
        print("✅ 启动测试完成")
        
        # 测试停止
        await stop_login_keeper()
        print("✅ 停止测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_startup())
