#!/usr/bin/env python3
"""
任务明细功能测试脚本

测试任务明细管理和单项重试功能
"""

import sys
import os
import asyncio
from datetime import datetime, date

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import SessionLocal
from app.models import DataUpdateRecord, DataUpdateTaskItem, PlatformAccount
from app.services.data_update_service import DataUpdateService
from app.services.platform_service_base import PlatformServiceFactory, TaskItemStatus


def test_platform_service_factory():
    """测试平台服务工厂"""
    print("🧪 测试平台服务工厂")
    
    # 测试获取平台数据类型
    platforms = PlatformServiceFactory.get_all_platforms()
    print(f"支持的平台: {platforms}")
    
    for platform in platforms:
        data_types = PlatformServiceFactory.get_platform_data_types(platform)
        print(f"{platform} 支持的数据类型: {data_types}")
        
        # 测试数据类型验证
        for data_type, display_name in data_types:
            is_valid = PlatformServiceFactory.validate_data_type(platform, data_type)
            display = PlatformServiceFactory.get_data_type_display_name(platform, data_type)
            print(f"  - {data_type}: 有效={is_valid}, 显示名={display}")
    
    print("✅ 平台服务工厂测试通过\n")


def test_task_item_creation():
    """测试任务项创建"""
    print("🧪 测试任务项创建")
    
    db = SessionLocal()
    try:
        # 获取一些账号
        accounts = db.query(PlatformAccount).filter(
            PlatformAccount.login_status == True
        ).limit(3).all()
        
        if not accounts:
            print("❌ 没有找到已登录的账号，跳过测试")
            return
        
        print(f"找到 {len(accounts)} 个已登录账号")
        
        # 创建测试更新记录
        test_record = DataUpdateRecord(
            start_date=date(2024, 1, 1),
            end_date=date(2024, 1, 31),
            status='running',
            total_accounts=len(accounts)
        )
        db.add(test_record)
        db.commit()
        
        print(f"创建测试更新记录: {test_record.id}")
        
        # 创建任务项
        task_item_ids = DataUpdateService.create_task_items(test_record.id, accounts, db)
        print(f"创建了 {len(task_item_ids)} 个任务项")
        
        # 验证任务项
        task_items = db.query(DataUpdateTaskItem).filter(
            DataUpdateTaskItem.update_record_id == test_record.id
        ).all()
        
        print("任务项详情:")
        for item in task_items:
            print(f"  - {item.account_name} ({item.platform}): {item.data_type_display} [{item.status}]")
        
        # 清理测试数据
        db.query(DataUpdateTaskItem).filter(
            DataUpdateTaskItem.update_record_id == test_record.id
        ).delete()
        db.query(DataUpdateRecord).filter(
            DataUpdateRecord.id == test_record.id
        ).delete()
        db.commit()
        
        print("✅ 任务项创建测试通过\n")
        
    except Exception as e:
        print(f"❌ 任务项创建测试失败: {e}")
        db.rollback()
    finally:
        db.close()


def test_task_item_status_update():
    """测试任务项状态更新"""
    print("🧪 测试任务项状态更新")
    
    db = SessionLocal()
    try:
        # 获取一个账号
        account = db.query(PlatformAccount).filter(
            PlatformAccount.login_status == True
        ).first()
        
        if not account:
            print("❌ 没有找到已登录的账号，跳过测试")
            return
        
        # 创建测试更新记录
        test_record = DataUpdateRecord(
            start_date=date(2024, 1, 1),
            end_date=date(2024, 1, 31),
            status='running',
            total_accounts=1
        )
        db.add(test_record)
        db.commit()
        
        # 创建任务项
        task_item_ids = DataUpdateService.create_task_items(test_record.id, [account], db)
        task_item_id = task_item_ids[0]
        
        print(f"创建测试任务项: {task_item_id}")
        
        # 测试状态更新
        statuses = [
            (TaskItemStatus.RUNNING, None),
            (TaskItemStatus.COMPLETED, None),
            (TaskItemStatus.FAILED, "测试错误信息"),
            (TaskItemStatus.RETRYING, None)
        ]
        
        for status, error_msg in statuses:
            success = DataUpdateService.update_task_item_status(
                task_item_id, status, error_msg, db
            )
            print(f"更新状态为 {status}: {'成功' if success else '失败'}")
            
            # 验证更新
            task_item = db.query(DataUpdateTaskItem).filter(
                DataUpdateTaskItem.id == task_item_id
            ).first()
            
            if task_item:
                print(f"  当前状态: {task_item.status}")
                if error_msg:
                    print(f"  错误信息: {task_item.error_message}")
        
        # 清理测试数据
        db.query(DataUpdateTaskItem).filter(
            DataUpdateTaskItem.update_record_id == test_record.id
        ).delete()
        db.query(DataUpdateRecord).filter(
            DataUpdateRecord.id == test_record.id
        ).delete()
        db.commit()
        
        print("✅ 任务项状态更新测试通过\n")
        
    except Exception as e:
        print(f"❌ 任务项状态更新测试失败: {e}")
        db.rollback()
    finally:
        db.close()


def test_get_task_items():
    """测试获取任务明细"""
    print("🧪 测试获取任务明细")
    
    db = SessionLocal()
    try:
        # 获取一个现有的更新记录
        update_record = db.query(DataUpdateRecord).first()
        
        if not update_record:
            print("❌ 没有找到更新记录，跳过测试")
            return
        
        print(f"使用更新记录: {update_record.id}")
        
        # 获取任务明细
        result = DataUpdateService.get_task_items(
            record_id=update_record.id,
            page=1,
            page_size=10
        )
        
        print(f"总任务数: {result['total']}")
        print(f"当前页任务数: {len(result['items'])}")
        print(f"状态统计: {result['stats']}")
        
        # 显示前几个任务项
        for i, item in enumerate(result['items'][:5]):
            print(f"  {i+1}. {item['account_name']} - {item['data_type_display']}: {item['status']}")
        
        print("✅ 获取任务明细测试通过\n")
        
    except Exception as e:
        print(f"❌ 获取任务明细测试失败: {e}")
    finally:
        db.close()


async def test_service_creation():
    """测试服务创建"""
    print("🧪 测试服务创建")
    
    db = SessionLocal()
    try:
        # 获取不同平台的账号
        platforms = ['wechat_channels', 'xiaohongshu', 'wechat_mp']
        
        for platform in platforms:
            account = db.query(PlatformAccount).filter(
                PlatformAccount.platform == platform,
                PlatformAccount.login_status == True
            ).first()
            
            if account:
                print(f"测试 {platform} 服务创建")
                try:
                    service = PlatformServiceFactory.create_service(platform, account.id)
                    data_types = service.get_supported_data_types()
                    print(f"  支持的数据类型: {data_types}")
                    await service.close()
                    print(f"  ✅ {platform} 服务创建成功")
                except Exception as e:
                    print(f"  ❌ {platform} 服务创建失败: {e}")
            else:
                print(f"  ⚠️ 没有找到 {platform} 平台的已登录账号")
        
        print("✅ 服务创建测试完成\n")
        
    except Exception as e:
        print(f"❌ 服务创建测试失败: {e}")
    finally:
        db.close()


def main():
    """主函数"""
    print("🚀 开始任务明细功能测试")
    print("=" * 50)
    
    tests = [
        ("平台服务工厂", test_platform_service_factory),
        ("任务项创建", test_task_item_creation),
        ("任务项状态更新", test_task_item_status_update),
        ("获取任务明细", test_get_task_items),
        ("服务创建", test_service_creation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"📋 运行测试: {test_name}")
        try:
            if asyncio.iscoroutinefunction(test_func):
                asyncio.run(test_func())
            else:
                test_func()
            passed += 1
        except Exception as e:
            print(f"❌ 测试 {test_name} 异常: {e}")
    
    print("=" * 50)
    print(f"🎉 测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("✅ 所有测试通过！任务明细功能正常工作")
    else:
        print("⚠️ 部分测试失败，请检查相关功能")


if __name__ == "__main__":
    main()
