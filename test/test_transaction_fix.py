#!/usr/bin/env python3
"""
测试事务提交修复
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def test_wechat_service_transaction_fix():
    """测试微信服务事务修复"""
    print("=== 测试微信服务事务修复 ===")
    
    try:
        service_file = "app/services/wechat_service.py"
        
        if os.path.exists(service_file):
            print(f"✅ 微信服务文件存在")
            
            with open(service_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # 检查事务处理修复
                transaction_checks = [
                    ("数据导入成功后提交事务", "db.commit()"),
                    ("数据导入失败后回滚事务", "db.rollback()"),
                    ("异常处理中回滚事务", "except Exception as e:" in content and "db.rollback()" in content),
                    ("成功日志", "数据导入成功: 新增"),
                    ("失败日志", "数据导入失败:"),
                    ("异常日志", "数据导入过程中发生异常:")
                ]
                
                all_passed = True
                for check_name, check_condition in transaction_checks:
                    if isinstance(check_condition, bool):
                        if check_condition:
                            print(f"✅ {check_name}: 正确")
                        else:
                            print(f"❌ {check_name}: 错误")
                            all_passed = False
                    else:
                        if check_condition in content:
                            print(f"✅ {check_name}: 存在")
                        else:
                            print(f"❌ {check_name}: 缺失")
                            all_passed = False
                
                return all_passed
        else:
            print(f"❌ 微信服务文件不存在")
            return False
        
    except Exception as e:
        print(f"❌ 测试微信服务事务修复失败: {e}")
        return False


def test_import_method_structure():
    """测试导入方法结构"""
    print("\n=== 测试导入方法结构 ===")
    
    try:
        service_file = "app/services/wechat_service.py"
        
        with open(service_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
            # 查找_import_excel_to_database方法
            import_method_start = content.find("async def _import_excel_to_database")
            if import_method_start == -1:
                print("❌ 未找到_import_excel_to_database方法")
                return False
            
            # 提取方法内容（简单的方式，查找下一个方法定义）
            next_method_start = content.find("\n    async def ", import_method_start + 1)
            if next_method_start == -1:
                next_method_start = content.find("\n    def ", import_method_start + 1)
            
            if next_method_start == -1:
                method_content = content[import_method_start:]
            else:
                method_content = content[import_method_start:next_method_start]
            
            print("✅ 找到_import_excel_to_database方法")
            
            # 检查方法结构
            structure_checks = [
                ("创建数据库会话", "db = SessionLocal()"),
                ("try-except-finally结构", "try:" in method_content and "except Exception as e:" in method_content and "finally:" in method_content),
                ("调用数据导入服务", "DataDetailsService.import_excel_data"),
                ("成功时提交事务", "if result[\"success\"]:" in method_content and "db.commit()" in method_content),
                ("失败时回滚事务", "else:" in method_content and "db.rollback()" in method_content),
                ("异常时回滚事务", "except Exception as e:" in method_content and "db.rollback()" in method_content),
                ("关闭数据库连接", "db.close()")
            ]
            
            all_passed = True
            for check_name, check_condition in structure_checks:
                if isinstance(check_condition, bool):
                    if check_condition:
                        print(f"✅ {check_name}: 正确")
                    else:
                        print(f"❌ {check_name}: 错误")
                        all_passed = False
                else:
                    if check_condition in method_content:
                        print(f"✅ {check_name}: 存在")
                    else:
                        print(f"❌ {check_name}: 缺失")
                        all_passed = False
            
            return all_passed
        
    except Exception as e:
        print(f"❌ 测试导入方法结构失败: {e}")
        return False


def test_data_details_service_transaction():
    """测试数据详情服务的事务处理"""
    print("\n=== 测试数据详情服务的事务处理 ===")
    
    try:
        service_file = "app/services/data_details_service.py"
        
        if os.path.exists(service_file):
            print(f"✅ 数据详情服务文件存在")
            
            with open(service_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # 检查import_excel_data方法的事务处理
                transaction_checks = [
                    ("数据导入方法存在", "def import_excel_data"),
                    ("事务回滚处理", "db.rollback()"),
                    ("返回成功结果", "return {\"success\": True"),
                    ("返回失败结果", "return {\"success\": False")
                ]
                
                all_passed = True
                for check_name, check_content in transaction_checks:
                    if check_content in content:
                        print(f"✅ {check_name}: 存在")
                    else:
                        print(f"❌ {check_name}: 缺失")
                        all_passed = False
                
                return all_passed
        else:
            print(f"❌ 数据详情服务文件不存在")
            return False
        
    except Exception as e:
        print(f"❌ 测试数据详情服务事务处理失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🔧 测试事务提交修复")
    
    tests = [
        ("微信服务事务修复", test_wechat_service_transaction_fix),
        ("导入方法结构", test_import_method_structure),
        ("数据详情服务事务", test_data_details_service_transaction)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            if result:
                print(f"✅ {test_name} - 通过")
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"事务修复测试总结: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 事务提交修复完成！")
        print("\n📋 修复内容:")
        print("  ✅ 添加了数据导入成功后的事务提交")
        print("  ✅ 添加了数据导入失败后的事务回滚")
        print("  ✅ 添加了异常情况下的事务回滚")
        print("  ✅ 完善了错误处理和日志记录")
        print("\n🔧 修复的关键问题:")
        print("  - 之前: 数据添加到会话但未提交，导致数据丢失")
        print("  - 现在: 成功时提交事务，失败时回滚事务")
        print("\n💡 现在数据更新应该能正常工作:")
        print("  1. Excel文件下载成功")
        print("  2. 数据解析和导入成功")
        print("  3. 事务正确提交到数据库")
        print("  4. 数据在数据库中持久化保存")
        print("\n🎯 建议:")
        print("  - 重新运行数据更新任务")
        print("  - 检查数据库中是否有新的数据记录")
        print("  - 观察日志中的数据导入成功信息")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return False


if __name__ == "__main__":
    main()
