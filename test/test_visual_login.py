#!/usr/bin/env python3
"""
测试可视化登录的脚本
使用方法：
1. 确保服务器正在运行
2. 运行此脚本
3. 脚本会发送请求到登录接口，并设置headless=false
"""

import requests
import json
import time

# 配置
BASE_URL = "http://127.0.0.1:8000"
ACCOUNT_ID = 11  # 您的视频号账号ID

def test_visual_login():
    """测试可视化登录"""
    print("🎬 开始测试视频号可视化登录")
    print("=" * 50)
    
    # 首先需要登录获取token（如果需要的话）
    # 这里假设您已经有了有效的认证token
    
    try:
        # 发送获取二维码的请求，设置headless=false
        print("📱 步骤1: 获取视频号登录二维码（可视模式）")
        print("-" * 30)
        
        qr_url = f"{BASE_URL}/api/wechat/login/qrcode/{ACCOUNT_ID}"
        params = {"headless": False}  # 设置为可视模式
        
        print(f"发送请求到: {qr_url}")
        print(f"参数: {params}")
        
        # 注意：这里需要添加认证头，如果您的API需要认证的话
        headers = {
            "Content-Type": "application/json",
            # "Authorization": "Bearer YOUR_TOKEN_HERE"  # 如果需要认证
        }
        
        response = requests.post(qr_url, params=params, headers=headers)
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 成功获取二维码")
            print(f"账号ID: {result.get('account_id')}")
            
            qrcode_data = result.get('qrcode')
            if qrcode_data:
                if qrcode_data == "already_logged_in":
                    print("✅ 检测到已有有效的登录状态")
                    return True
                elif qrcode_data.startswith("data:image/png;base64,"):
                    print(f"✅ 获取到二维码，数据长度: {len(qrcode_data)} 字符")
                    print("🖥️ 浏览器窗口应该已经打开，您可以观察登录过程")
                    
                    # 开始轮询登录状态
                    print("\n📱 步骤2: 轮询登录状态")
                    print("-" * 30)
                    
                    max_attempts = 20  # 最多检查20次
                    for i in range(max_attempts):
                        print(f"⏰ 检查登录状态 ({i+1}/{max_attempts})...")
                        
                        status_url = f"{BASE_URL}/api/wechat/login/status/{ACCOUNT_ID}"
                        status_response = requests.get(status_url, headers=headers)
                        
                        if status_response.status_code == 200:
                            status_result = status_response.json()
                            is_logged_in = status_result.get('logged_in', False)
                            
                            if is_logged_in:
                                print("🎉 登录成功！")
                                return True
                            else:
                                print("⏳ 等待扫码...")
                                time.sleep(3)  # 等待3秒后再次检查
                        else:
                            print(f"❌ 检查登录状态失败: {status_response.status_code}")
                            print(f"错误信息: {status_response.text}")
                            break
                    
                    print("⏰ 登录超时")
                    return False
                else:
                    print(f"⚠️ 收到意外的二维码数据: {qrcode_data[:100]}...")
                    return False
            else:
                print("❌ 未收到二维码数据")
                return False
        else:
            print(f"❌ 获取二维码失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

def main():
    """主函数"""
    print("🚀 视频号可视化登录测试开始")
    print("=" * 60)
    print("⚠️ 注意：此脚本会打开浏览器窗口，请确保您的环境支持图形界面")
    print("📋 使用说明：")
    print("   1. 确保后端服务器正在运行")
    print("   2. 浏览器窗口打开后，使用微信扫描二维码")
    print("   3. 观察登录过程")
    print()
    
    success = test_visual_login()
    
    if success:
        print("\n🏆 测试成功！")
        print("✅ 视频号登录功能正常工作")
    else:
        print("\n💥 测试失败！")
        print("❌ 请检查错误信息并修复问题")
    
    return success

if __name__ == "__main__":
    result = main()
    exit(0 if result else 1)
