#!/usr/bin/env python3
"""
测试微信视频号数据明细API功能
"""

import sys
import os
import requests
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import SessionLocal
from app.models import WeChatChannelsVideoData, PlatformAccount, User


def test_api_endpoints():
    """测试API端点"""
    print("=== 测试API端点 ===")
    
    base_url = "http://localhost:8000"
    
    # 测试配置API
    try:
        response = requests.get(f"{base_url}/api/data-details/wechat-channels/config")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 配置API正常: {data['success']}")
            if data['success']:
                config = data['data_types']
                print(f"  支持的数据类型: {list(config.keys())}")
                if 'single_video' in config:
                    print(f"  single_video配置: {config['single_video']['name']}")
                    print(f"  字段数量: {len(config['single_video']['columns'])}")
        else:
            print(f"❌ 配置API失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 配置API异常: {e}")
        return False
    
    # 测试账号API
    try:
        response = requests.get(f"{base_url}/api/data-details/wechat-channels/accounts")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 账号API正常: {data['success']}")
            if data['success']:
                accounts = data['accounts']
                print(f"  账号数量: {len(accounts)}")
                for account in accounts:
                    print(f"    - {account['name']} (ID: {account['id']})")
        else:
            print(f"❌ 账号API失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 账号API异常: {e}")
        return False
    
    return True


def test_data_details_api():
    """测试数据明细API"""
    print("\n=== 测试数据明细API ===")
    
    base_url = "http://localhost:8000"
    
    # 获取测试账号
    db = SessionLocal()
    try:
        test_account = db.query(PlatformAccount).filter(
            PlatformAccount.platform == 'wechat_channels'
        ).first()
        
        if not test_account:
            print("❌ 没有找到测试账号")
            return False
        
        print(f"使用测试账号: {test_account.name} (ID: {test_account.id})")
        
        # 测试数据列表API
        try:
            params = {
                'account_id': test_account.id,
                'page': 1,
                'page_size': 10
            }
            response = requests.get(
                f"{base_url}/api/data-details/wechat-channels/single_video",
                params=params
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 数据列表API正常: {data['success']}")
                if data['success']:
                    print(f"  总记录数: {data['total']}")
                    print(f"  当前页: {data['page']}")
                    print(f"  每页大小: {data['page_size']}")
                    print(f"  数据条数: {len(data['data'])}")
                    
                    # 显示部分数据
                    if data['data']:
                        sample = data['data'][0]
                        print(f"  样本数据字段: {list(sample.keys())}")
                        print(f"  视频描述: {sample.get('video_description', 'N/A')[:30]}...")
                        print(f"  播放量: {sample.get('play_count', 'N/A')}")
            else:
                print(f"❌ 数据列表API失败: {response.status_code}")
                print(f"  响应: {response.text}")
                return False
        except Exception as e:
            print(f"❌ 数据列表API异常: {e}")
            return False
        
        # 测试汇总API
        try:
            response = requests.get(
                f"{base_url}/api/data-details/wechat-channels/single_video/summary",
                params={'account_id': test_account.id}
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 汇总API正常: {data['success']}")
                if data['success']:
                    print(f"  总记录数: {data['total_records']}")
                    print(f"  最新时间: {data.get('latest_time', 'N/A')}")
            else:
                print(f"❌ 汇总API失败: {response.status_code}")
                print(f"  响应: {response.text}")
                return False
        except Exception as e:
            print(f"❌ 汇总API异常: {e}")
            return False
        
        return True
        
    finally:
        db.close()


def test_data_filtering():
    """测试数据过滤功能"""
    print("\n=== 测试数据过滤功能 ===")
    
    base_url = "http://localhost:8000"
    
    db = SessionLocal()
    try:
        test_account = db.query(PlatformAccount).filter(
            PlatformAccount.platform == 'wechat_channels'
        ).first()
        
        if not test_account:
            print("❌ 没有找到测试账号")
            return False
        
        # 测试搜索功能
        try:
            params = {
                'account_id': test_account.id,
                'page': 1,
                'page_size': 5,
                'search': '测试'  # 搜索包含"测试"的记录
            }
            response = requests.get(
                f"{base_url}/api/data-details/wechat-channels/single_video",
                params=params
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 搜索功能正常: {data['success']}")
                if data['success']:
                    print(f"  搜索结果数: {len(data['data'])}")
            else:
                print(f"❌ 搜索功能失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 搜索功能异常: {e}")
            return False
        
        # 测试排序功能
        try:
            params = {
                'account_id': test_account.id,
                'page': 1,
                'page_size': 5,
                'sort_field': 'play_count',
                'sort_order': 'desc'
            }
            response = requests.get(
                f"{base_url}/api/data-details/wechat-channels/single_video",
                params=params
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 排序功能正常: {data['success']}")
                if data['success'] and data['data']:
                    # 检查是否按播放量降序排列
                    play_counts = [item.get('play_count', 0) for item in data['data']]
                    is_sorted = all(play_counts[i] >= play_counts[i+1] for i in range(len(play_counts)-1))
                    print(f"  排序正确性: {'✅' if is_sorted else '❌'}")
                    print(f"  播放量序列: {play_counts}")
            else:
                print(f"❌ 排序功能失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 排序功能异常: {e}")
            return False
        
        return True
        
    finally:
        db.close()


def test_all_accounts_query():
    """测试全部账号查询"""
    print("\n=== 测试全部账号查询 ===")
    
    base_url = "http://localhost:8000"
    
    try:
        # 不传account_id参数，应该返回所有账号的数据
        params = {
            'page': 1,
            'page_size': 10
        }
        response = requests.get(
            f"{base_url}/api/data-details/wechat-channels/single_video",
            params=params
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 全部账号查询正常: {data['success']}")
            if data['success']:
                print(f"  总记录数: {data['total']}")
                # 检查是否包含多个账号的数据
                if data['data']:
                    account_names = set(item.get('account_name') for item in data['data'])
                    print(f"  涉及账号: {list(account_names)}")
        else:
            print(f"❌ 全部账号查询失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 全部账号查询异常: {e}")
        return False
    
    return True


def main():
    """主测试函数"""
    print("🚀 开始测试微信视频号数据明细API功能")
    
    tests = [
        ("API端点", test_api_endpoints),
        ("数据明细API", test_data_details_api),
        ("数据过滤功能", test_data_filtering),
        ("全部账号查询", test_all_accounts_query)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        try:
            if test_func():
                print(f"✅ {test_name} - 通过")
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"API测试总结: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有API测试通过！")
        print("📋 功能清单:")
        print("  ✅ 配置API正常")
        print("  ✅ 账号API正常")
        print("  ✅ 数据列表API正常")
        print("  ✅ 数据汇总API正常")
        print("  ✅ 搜索功能正常")
        print("  ✅ 排序功能正常")
        print("  ✅ 全部账号查询正常")
        print("\n🚀 微信视频号数据明细API已准备就绪！")
        return True
    else:
        print("⚠️  部分API测试失败，请检查相关功能。")
        return False


if __name__ == "__main__":
    main()
