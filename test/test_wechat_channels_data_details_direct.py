#!/usr/bin/env python3
"""
直接测试微信视频号数据明细服务功能（不依赖HTTP服务）
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import SessionLocal
from app.models import WeChatChannelsVideoData, PlatformAccount
from app.services.data_details_service import DataDetailsService
from app.routers.data_details import DATA_TYPE_CONFIG


def test_data_config():
    """测试数据配置"""
    print("=== 测试数据配置 ===")
    
    try:
        # 检查配置是否包含视频号数据
        if 'single_video' in DATA_TYPE_CONFIG:
            config = DATA_TYPE_CONFIG['single_video']
            print(f"✅ 配置存在: {config['name']}")
            print(f"  描述: {config['description']}")
            print(f"  字段数量: {len(config['columns'])}")
            
            # 检查关键字段
            column_keys = [col['key'] for col in config['columns']]
            required_fields = [
                'account_name', 'video_description', 'video_id', 'publish_time',
                'play_count', 'like_count', 'comment_count', 'share_count'
            ]
            
            missing_fields = [field for field in required_fields if field not in column_keys]
            if missing_fields:
                print(f"❌ 缺少必需字段: {missing_fields}")
                return False
            else:
                print("✅ 所有必需字段都存在")
            
            return True
        else:
            print("❌ 配置中缺少single_video")
            return False
            
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False


def test_data_service():
    """测试数据服务"""
    print("\n=== 测试数据服务 ===")
    
    db = SessionLocal()
    try:
        # 查找测试账号
        test_account = db.query(PlatformAccount).filter(
            PlatformAccount.platform == 'wechat_channels'
        ).first()
        
        if not test_account:
            print("❌ 没有找到测试账号")
            return False
        
        print(f"使用测试账号: {test_account.name} (ID: {test_account.id})")
        
        # 测试数据列表获取
        result = DataDetailsService.get_data_list(
            db=db,
            account_id=test_account.id,
            data_type='single_video',
            page=1,
            page_size=5
        )
        
        if result['success']:
            print(f"✅ 数据列表获取成功")
            print(f"  总记录数: {result['total']}")
            print(f"  当前页: {result['page']}")
            print(f"  每页大小: {result['page_size']}")
            print(f"  数据条数: {len(result['data'])}")
            
            # 检查数据结构
            if result['data']:
                sample = result['data'][0]
                print(f"  样本数据字段: {list(sample.keys())}")
                
                # 检查关键字段
                required_fields = ['account_name', 'video_id', 'video_description', 'play_count']
                missing_fields = [field for field in required_fields if field not in sample]
                if missing_fields:
                    print(f"❌ 样本数据缺少字段: {missing_fields}")
                    return False
                else:
                    print("✅ 样本数据结构正确")
                    print(f"  视频描述: {sample.get('video_description', 'N/A')[:30]}...")
                    print(f"  播放量: {sample.get('play_count', 'N/A')}")
        else:
            print(f"❌ 数据列表获取失败: {result['error']}")
            return False
        
        # 测试数据汇总
        summary_result = DataDetailsService.get_data_summary(
            db=db,
            account_id=test_account.id,
            data_type='single_video'
        )
        
        if summary_result['success']:
            print(f"✅ 数据汇总获取成功")
            print(f"  总记录数: {summary_result['total_records']}")
            print(f"  最新时间: {summary_result.get('latest_time', 'N/A')}")
        else:
            print(f"❌ 数据汇总获取失败: {summary_result['error']}")
            return False
        
        return True
        
    finally:
        db.close()


def test_data_filtering():
    """测试数据过滤和排序"""
    print("\n=== 测试数据过滤和排序 ===")
    
    db = SessionLocal()
    try:
        test_account = db.query(PlatformAccount).filter(
            PlatformAccount.platform == 'wechat_channels'
        ).first()
        
        if not test_account:
            print("❌ 没有找到测试账号")
            return False
        
        # 测试搜索功能
        search_result = DataDetailsService.get_data_list(
            db=db,
            account_id=test_account.id,
            data_type='single_video',
            page=1,
            page_size=10,
            search='测试'
        )
        
        if search_result['success']:
            print(f"✅ 搜索功能正常")
            print(f"  搜索结果数: {len(search_result['data'])}")
        else:
            print(f"❌ 搜索功能失败: {search_result['error']}")
            return False
        
        # 测试排序功能
        sort_result = DataDetailsService.get_data_list(
            db=db,
            account_id=test_account.id,
            data_type='single_video',
            page=1,
            page_size=5,
            sort_field='play_count',
            sort_order='desc'
        )
        
        if sort_result['success']:
            print(f"✅ 排序功能正常")
            if sort_result['data']:
                play_counts = [item.get('play_count', 0) for item in sort_result['data']]
                is_sorted = all(play_counts[i] >= play_counts[i+1] for i in range(len(play_counts)-1))
                print(f"  排序正确性: {'✅' if is_sorted else '❌'}")
                print(f"  播放量序列: {play_counts}")
        else:
            print(f"❌ 排序功能失败: {sort_result['error']}")
            return False
        
        return True
        
    finally:
        db.close()


def test_multi_account_query():
    """测试多账号查询"""
    print("\n=== 测试多账号查询 ===")
    
    db = SessionLocal()
    try:
        # 获取所有视频号账号
        accounts = db.query(PlatformAccount).filter(
            PlatformAccount.platform == 'wechat_channels'
        ).all()
        
        if not accounts:
            print("❌ 没有找到视频号账号")
            return False
        
        account_ids = [acc.id for acc in accounts]
        print(f"找到 {len(accounts)} 个视频号账号")
        
        # 测试多账号数据查询
        result = DataDetailsService.get_data_list(
            db=db,
            account_id=account_ids,
            data_type='single_video',
            page=1,
            page_size=10
        )
        
        if result['success']:
            print(f"✅ 多账号查询成功")
            print(f"  总记录数: {result['total']}")
            
            # 检查是否包含多个账号的数据
            if result['data']:
                account_names = set(item.get('account_name') for item in result['data'])
                print(f"  涉及账号: {list(account_names)}")
        else:
            print(f"❌ 多账号查询失败: {result['error']}")
            return False
        
        # 测试全部账号查询（account_id=None）
        all_result = DataDetailsService.get_data_list(
            db=db,
            account_id=None,
            data_type='single_video',
            page=1,
            page_size=5
        )
        
        if all_result['success']:
            print(f"✅ 全部账号查询成功")
            print(f"  总记录数: {all_result['total']}")
        else:
            print(f"❌ 全部账号查询失败: {all_result['error']}")
            return False
        
        return True
        
    finally:
        db.close()


def test_model_mapping():
    """测试模型映射"""
    print("\n=== 测试模型映射 ===")
    
    try:
        # 检查模型映射
        model_mapping = DataDetailsService.MODEL_MAPPING
        if 'single_video' in model_mapping:
            model_class = model_mapping['single_video']
            print(f"✅ 模型映射存在: single_video -> {model_class.__name__}")
            
            # 验证模型类
            if model_class == WeChatChannelsVideoData:
                print("✅ 模型类正确")
            else:
                print(f"❌ 模型类错误，期望: WeChatChannelsVideoData, 实际: {model_class}")
                return False
        else:
            print("❌ 模型映射缺少single_video")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 模型映射测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始直接测试微信视频号数据明细服务功能")
    
    tests = [
        ("数据配置", test_data_config),
        ("模型映射", test_model_mapping),
        ("数据服务", test_data_service),
        ("数据过滤和排序", test_data_filtering),
        ("多账号查询", test_multi_account_query)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        try:
            if test_func():
                print(f"✅ {test_name} - 通过")
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n{'='*50}")
    print(f"服务测试总结: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有服务测试通过！")
        print("📋 功能清单:")
        print("  ✅ 数据配置正确")
        print("  ✅ 模型映射正确")
        print("  ✅ 数据列表查询正常")
        print("  ✅ 数据汇总查询正常")
        print("  ✅ 搜索功能正常")
        print("  ✅ 排序功能正常")
        print("  ✅ 多账号查询正常")
        print("\n🚀 微信视频号数据明细服务已准备就绪！")
        return True
    else:
        print("⚠️  部分服务测试失败，请检查相关功能。")
        return False


if __name__ == "__main__":
    main()
