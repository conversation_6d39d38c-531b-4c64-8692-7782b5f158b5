#!/usr/bin/env python3
"""
测试微信视频号数据导入功能
"""

import sys
import os
import pandas as pd
from datetime import datetime, date
import io

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import SessionLocal
from app.models import WeChatChannelsVideoData, PlatformAccount
from app.services.data_details_service import DataDetailsService
from app.services.wechat_channels_service import WeChatChannelsService


def test_model_creation():
    """测试数据模型创建"""
    print("=== 测试数据模型创建 ===")
    
    db = SessionLocal()
    try:
        # 测试查询表是否存在
        result = db.query(WeChatChannelsVideoData).count()
        print(f"✅ 视频号数据表查询成功，当前记录数: {result}")
        return True
    except Exception as e:
        print(f"❌ 数据表查询失败: {e}")
        return False
    finally:
        db.close()


def test_download_config():
    """测试下载配置"""
    print("\n=== 测试下载配置 ===")
    
    try:
        service = WeChatChannelsService()
        config = service._get_download_config('single_video')
        
        if config:
            print(f"✅ 配置获取成功:")
            print(f"  名称: {config.get('name')}")
            print(f"  数据起始行: {config.get('data_start_row')}")
            print(f"  字段数量: {len(config.get('fields', []))}")
            
            # 显示字段配置
            fields = config.get('fields', [])
            print("  字段配置:")
            for field_name, field_type in fields[:5]:  # 显示前5个字段
                type_name = {1: "文本", 2: "数字"}.get(field_type, f"类型{field_type}")
                print(f"    - {field_name}: {type_name}")
            if len(fields) > 5:
                print(f"    ... 还有 {len(fields) - 5} 个字段")
            
            return True
        else:
            print("❌ 配置获取失败")
            return False
            
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False


def create_test_excel_data():
    """创建测试用的Excel数据"""
    print("\n=== 创建测试Excel数据 ===")
    
    try:
        # 创建测试数据
        test_data = {
            '视频描述': ['测试视频1', '测试视频2', '测试视频3'],
            '视频ID': ['video_001', 'video_002', 'video_003'],
            '发布时间': ['2025-01-01 10:00:00', '2025-01-02 15:30:00', '2025-01-03 20:45:00'],
            '完播率': ['85.5%', '92.3%', '78.9%'],
            '平均播放时长': [120, 180, 95],
            '播放量': [1000, 2500, 800],
            '推荐': [500, 1200, 400],
            '喜欢': [100, 250, 80],
            '评论量': [20, 45, 15],
            '分享量': [30, 60, 25],
            '关注量': [10, 25, 8],
            '转发聊天和朋友圈': [15, 35, 12],
            '设为铃声': [5, 10, 3],
            '设为状态': [8, 15, 6],
            '设为朋友圈封面': [2, 5, 1]
        }
        
        # 创建DataFrame
        df = pd.DataFrame(test_data)
        
        # 转换为Excel字节数据
        excel_buffer = io.BytesIO()
        df.to_excel(excel_buffer, index=False, engine='openpyxl')
        excel_content = excel_buffer.getvalue()
        
        print(f"✅ 测试Excel数据创建成功，大小: {len(excel_content)} bytes")
        print(f"  数据行数: {len(df)}")
        print(f"  字段数: {len(df.columns)}")
        
        return excel_content
        
    except Exception as e:
        print(f"❌ 创建测试Excel数据失败: {e}")
        return None


def test_data_import():
    """测试数据导入功能"""
    print("\n=== 测试数据导入功能 ===")
    
    try:
        # 创建测试Excel数据
        excel_content = create_test_excel_data()
        if not excel_content:
            return False
        
        # 创建或获取测试账号
        db = SessionLocal()
        try:
            # 查找或创建测试账号
            test_account = db.query(PlatformAccount).filter(
                PlatformAccount.platform == 'wechat_channels'
            ).first()
            
            if not test_account:
                print("创建测试账号...")
                from app.models import User
                # 查找或创建测试用户
                test_user = db.query(User).first()
                if not test_user:
                    print("❌ 需要先创建用户才能测试")
                    return False
                
                test_account = PlatformAccount(
                    name="测试视频号账号",
                    platform="wechat_channels",
                    user_id=test_user.id,
                    login_status=True
                )
                db.add(test_account)
                db.commit()
                print(f"✅ 测试账号创建成功，ID: {test_account.id}")
            else:
                print(f"✅ 使用现有测试账号，ID: {test_account.id}")
            
            # 测试数据导入
            print("开始导入数据...")
            result = DataDetailsService.import_excel_data(
                db=db,
                account_id=test_account.id,
                data_type='single_video',
                excel_content=excel_content
            )
            
            if result["success"]:
                print(f"✅ 数据导入成功:")
                print(f"  新增记录: {result['imported_count']}")
                print(f"  更新记录: {result['updated_count']}")
                print(f"  总处理数: {result['total_processed']}")
                
                # 验证数据是否正确插入
                count = db.query(WeChatChannelsVideoData).filter(
                    WeChatChannelsVideoData.account_id == test_account.id
                ).count()
                print(f"  数据库中该账号的记录数: {count}")
                
                # 显示部分导入的数据
                sample_records = db.query(WeChatChannelsVideoData).filter(
                    WeChatChannelsVideoData.account_id == test_account.id
                ).limit(2).all()
                
                print("  样本数据:")
                for record in sample_records:
                    print(f"    视频ID: {record.video_id}, 描述: {record.video_description[:20]}...")
                    print(f"    播放量: {record.play_count}, 喜欢: {record.like_count}")
                
                return True
            else:
                print(f"❌ 数据导入失败: {result['error']}")
                return False
                
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ 数据导入测试失败: {e}")
        return False


def test_data_uniqueness():
    """测试数据唯一性约束"""
    print("\n=== 测试数据唯一性约束 ===")
    
    try:
        # 重复导入相同数据，应该更新而不是插入
        excel_content = create_test_excel_data()
        if not excel_content:
            return False
        
        db = SessionLocal()
        try:
            test_account = db.query(PlatformAccount).filter(
                PlatformAccount.platform == 'wechat_channels'
            ).first()
            
            if not test_account:
                print("❌ 找不到测试账号")
                return False
            
            # 记录导入前的数据量
            count_before = db.query(WeChatChannelsVideoData).filter(
                WeChatChannelsVideoData.account_id == test_account.id
            ).count()
            
            # 再次导入相同数据
            result = DataDetailsService.import_excel_data(
                db=db,
                account_id=test_account.id,
                data_type='single_video',
                excel_content=excel_content
            )
            
            if result["success"]:
                count_after = db.query(WeChatChannelsVideoData).filter(
                    WeChatChannelsVideoData.account_id == test_account.id
                ).count()
                
                print(f"✅ 重复导入测试:")
                print(f"  导入前记录数: {count_before}")
                print(f"  导入后记录数: {count_after}")
                print(f"  新增记录: {result['imported_count']}")
                print(f"  更新记录: {result['updated_count']}")
                
                if count_before == count_after and result['updated_count'] > 0:
                    print("✅ 唯一性约束工作正常，重复数据被更新而非插入")
                    return True
                else:
                    print("❌ 唯一性约束可能有问题")
                    return False
            else:
                print(f"❌ 重复导入失败: {result['error']}")
                return False
                
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ 唯一性测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试微信视频号数据导入功能")
    
    tests = [
        ("数据模型创建", test_model_creation),
        ("下载配置", test_download_config),
        ("数据导入功能", test_data_import),
        ("数据唯一性约束", test_data_uniqueness)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        try:
            if test_func():
                print(f"✅ {test_name} - 通过")
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"测试总结: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有测试通过！视频号数据导入功能正常工作。")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return False


if __name__ == "__main__":
    main()
