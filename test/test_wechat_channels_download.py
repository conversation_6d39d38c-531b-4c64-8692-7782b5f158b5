#!/usr/bin/env python3
"""
测试视频号数据下载功能
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.wechat_channels_service import WeChatChannelsService
from app.services.data_download_service import DataDownloadService


async def test_wechat_channels_service():
    """测试WeChatChannelsService的基本功能"""
    print("=== 测试WeChatChannelsService ===")
    
    # 创建服务实例
    service = WeChatChannelsService(account_id=999, headless=True)
    
    try:
        # 测试日期转换功能
        print("测试日期转换功能...")
        start_timestamp = service._date_to_timestamp("2024-01-01")
        end_timestamp = service._date_to_timestamp("2024-01-31", end_of_day=True)
        
        print(f"开始时间戳: {start_timestamp}")
        print(f"结束时间戳: {end_timestamp}")
        
        if start_timestamp and end_timestamp:
            print("✅ 日期转换功能正常")
        else:
            print("❌ 日期转换功能异常")
            
    except Exception as e:
        print(f"❌ 测试WeChatChannelsService时出错: {e}")
    finally:
        await service.close()


def test_data_download_service():
    """测试DataDownloadService的配置"""
    print("\n=== 测试DataDownloadService配置 ===")
    
    # 测试平台数据类型获取
    print("测试平台数据类型获取...")
    
    wechat_mp_types = DataDownloadService.get_platform_data_types("wechat_mp")
    wechat_channels_types = DataDownloadService.get_platform_data_types("wechat_channels")
    unknown_types = DataDownloadService.get_platform_data_types("unknown")
    
    print(f"微信公众号支持的数据类型: {wechat_mp_types}")
    print(f"微信视频号支持的数据类型: {wechat_channels_types}")
    print(f"未知平台支持的数据类型: {unknown_types}")
    
    # 验证配置
    expected_mp_types = ["content_trend", "content_source", "content_detail", "user_channel", "user_source"]
    expected_channels_types = ["single_video"]
    
    if set(wechat_mp_types) == set(expected_mp_types):
        print("✅ 微信公众号数据类型配置正确")
    else:
        print("❌ 微信公众号数据类型配置异常")
        
    if set(wechat_channels_types) == set(expected_channels_types):
        print("✅ 微信视频号数据类型配置正确")
    else:
        print("❌ 微信视频号数据类型配置异常")
        
    if len(unknown_types) == 0:
        print("✅ 未知平台处理正确")
    else:
        print("❌ 未知平台处理异常")
    
    # 测试文件名生成
    print("\n测试文件名生成...")
    
    mp_filename = DataDownloadService.generate_filename(
        "测试公众号", "content_trend", "2024-01-01", "2024-01-31", "wechat_mp"
    )
    channels_filename = DataDownloadService.generate_filename(
        "测试视频号", "single_video", "2024-01-01", "2024-01-31", "wechat_channels"
    )
    
    print(f"微信公众号文件名: {mp_filename}")
    print(f"微信视频号文件名: {channels_filename}")
    
    if "wechat_data_" in mp_filename and "内容数据趋势明细" in mp_filename:
        print("✅ 微信公众号文件名生成正确")
    else:
        print("❌ 微信公众号文件名生成异常")
        
    if "wechat_channels_data_" in channels_filename and "单篇视频数据" in channels_filename:
        print("✅ 微信视频号文件名生成正确")
    else:
        print("❌ 微信视频号文件名生成异常")


def test_data_type_config():
    """测试数据类型配置"""
    print("\n=== 测试数据类型配置 ===")
    
    # 检查合并后的配置
    all_config = DataDownloadService.DATA_TYPE_CONFIG
    mp_config = DataDownloadService.WECHAT_MP_DATA_TYPE_CONFIG
    channels_config = DataDownloadService.WECHAT_CHANNELS_DATA_TYPE_CONFIG
    
    print(f"所有数据类型: {list(all_config.keys())}")
    print(f"微信公众号数据类型: {list(mp_config.keys())}")
    print(f"微信视频号数据类型: {list(channels_config.keys())}")
    
    # 验证合并是否正确
    expected_all_keys = set(mp_config.keys()) | set(channels_config.keys())
    actual_all_keys = set(all_config.keys())
    
    if expected_all_keys == actual_all_keys:
        print("✅ 数据类型配置合并正确")
    else:
        print("❌ 数据类型配置合并异常")
        print(f"期望: {expected_all_keys}")
        print(f"实际: {actual_all_keys}")
    
    # 检查特定配置
    if "single_video" in all_config and all_config["single_video"] == "单篇视频数据":
        print("✅ 视频号数据类型配置正确")
    else:
        print("❌ 视频号数据类型配置异常")


async def main():
    """主测试函数"""
    print("开始测试视频号数据下载功能...\n")
    
    # 测试服务配置
    test_data_download_service()
    test_data_type_config()
    
    # 测试WeChatChannelsService
    await test_wechat_channels_service()
    
    print("\n=== 测试完成 ===")
    print("注意：完整的功能测试需要:")
    print("1. 有效的视频号登录状态")
    print("2. 视频号平台页面结构保持稳定")
    print("3. 网络连接正常")


if __name__ == "__main__":
    asyncio.run(main())
