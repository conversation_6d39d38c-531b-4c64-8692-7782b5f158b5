#!/usr/bin/env python3
"""
测试微信视频号关注者数据功能
"""

import sys
import os
from datetime import datetime, date
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import SessionLocal
from app.models import WeChatChannelsFollowerData, PlatformAccount
from app.services.wechat_channels_service import WeChatChannelsService


def test_model_creation():
    """测试数据模型创建"""
    print("=== 测试数据模型创建 ===")
    
    db = SessionLocal()
    try:
        # 测试查询表是否存在
        result = db.query(WeChatChannelsFollowerData).count()
        print(f"✅ 微信视频号关注者数据表查询成功，当前记录数: {result}")
        return True
    except Exception as e:
        print(f"❌ 数据表查询失败: {e}")
        return False
    finally:
        db.close()


def test_csv_parsing_logic():
    """测试CSV解析逻辑"""
    print("\n=== 测试CSV解析逻辑 ===")
    
    try:
        # 模拟CSV数据解析
        mock_csv_data = [
            {
                '时间': '2024-01-01',
                '净增关注': '150',
                '新增关注': '200',
                '取消关注': '50',
                '关注者总数': '10000'
            },
            {
                '时间': '2024-01-02',
                '净增关注': '180',
                '新增关注': '220',
                '取消关注': '40',
                '关注者总数': '10180'
            },
            {
                '时间': '2024-01-03',
                '净增关注': '120',
                '新增关注': '180',
                '取消关注': '60',
                '关注者总数': '10300'
            }
        ]
        
        # 测试数据解析逻辑
        parsed_data = []
        
        for row in mock_csv_data:
            parsed_row = {}
            
            # 根据CSV列名进行映射
            for key, value in row.items():
                if '时间' in key or 'date' in key.lower():
                    parsed_row['date'] = value.strip()
                elif '净增' in key or 'net' in key.lower():
                    parsed_row['net_follower_increase'] = int(value) if value.isdigit() else 0
                elif '新增' in key or 'new' in key.lower():
                    parsed_row['new_followers'] = int(value) if value.isdigit() else 0
                elif '取消' in key or '取关' in key or 'unfollow' in key.lower():
                    parsed_row['unfollowers'] = int(value) if value.isdigit() else 0
                elif '总数' in key or 'total' in key.lower():
                    parsed_row['total_followers'] = int(value) if value.isdigit() else 0
            
            if parsed_row.get('date'):
                parsed_data.append(parsed_row)
        
        # 验证解析结果
        if len(parsed_data) == 3:
            print("✅ CSV数据解析数量正确")
        else:
            print(f"❌ CSV数据解析数量错误: {len(parsed_data)} != 3")
            return False
        
        # 验证第一条数据
        first_row = parsed_data[0]
        if (first_row['date'] == '2024-01-01' and 
            first_row['net_follower_increase'] == 150 and
            first_row['new_followers'] == 200 and
            first_row['unfollowers'] == 50 and
            first_row['total_followers'] == 10000):
            print("✅ CSV数据解析内容正确")
        else:
            print(f"❌ CSV数据解析内容错误: {first_row}")
            return False
        
        print("✅ CSV解析逻辑测试通过")
        return True
        
    except Exception as e:
        print(f"❌ CSV解析逻辑测试失败: {e}")
        return False


def test_database_operations():
    """测试数据库操作"""
    print("\n=== 测试数据库操作 ===")
    
    db = SessionLocal()
    try:
        # 查找或创建测试账号
        test_account = db.query(PlatformAccount).filter(
            PlatformAccount.platform == 'wechat_channels'
        ).first()
        
        if not test_account:
            print("❌ 需要先创建微信视频号测试账号")
            return False
        
        print(f"✅ 使用测试账号，ID: {test_account.id}")
        
        # 创建测试数据
        test_date = date(2024, 1, 1)
        test_data = {
            'account_id': test_account.id,
            'date': test_date,
            'net_follower_increase': 150,
            'new_followers': 200,
            'unfollowers': 50,
            'total_followers': 10000
        }
        
        # 删除可能存在的测试数据
        db.query(WeChatChannelsFollowerData).filter(
            WeChatChannelsFollowerData.account_id == test_account.id,
            WeChatChannelsFollowerData.date == test_date
        ).delete()
        db.commit()
        
        # 插入新数据
        record = WeChatChannelsFollowerData(**test_data)
        db.add(record)
        db.commit()
        
        # 验证插入
        inserted_record = db.query(WeChatChannelsFollowerData).filter(
            WeChatChannelsFollowerData.account_id == test_account.id,
            WeChatChannelsFollowerData.date == test_date
        ).first()
        
        if inserted_record:
            print("✅ 数据插入成功")
            print(f"  净增关注: {inserted_record.net_follower_increase}")
            print(f"  新增关注: {inserted_record.new_followers}")
            print(f"  取消关注: {inserted_record.unfollowers}")
            print(f"  关注者总数: {inserted_record.total_followers}")
        else:
            print("❌ 数据插入失败")
            return False
        
        # 测试更新
        inserted_record.total_followers = 10500
        inserted_record.updated_at = datetime.utcnow()
        db.commit()
        
        # 验证更新
        updated_record = db.query(WeChatChannelsFollowerData).filter(
            WeChatChannelsFollowerData.account_id == test_account.id,
            WeChatChannelsFollowerData.date == test_date
        ).first()
        
        if updated_record and updated_record.total_followers == 10500:
            print("✅ 数据更新成功")
        else:
            print("❌ 数据更新失败")
            return False
        
        # 清理测试数据
        db.delete(updated_record)
        db.commit()
        print("✅ 测试数据清理完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库操作测试失败: {e}")
        return False
    finally:
        db.close()


def test_api_configuration():
    """测试API配置"""
    print("\n=== 测试API配置 ===")
    
    try:
        import requests
        base_url = 'http://localhost:8000'
        
        # 测试微信视频号配置API
        response = requests.get(f'{base_url}/api/data-details/wechat-channels/config', timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                data_types = data.get('data_types', {})
                
                # 检查是否包含关注者数据配置
                if 'follower_data' in data_types:
                    follower_config = data_types['follower_data']
                    print("✅ 关注者数据配置存在")
                    print(f"  名称: {follower_config.get('name')}")
                    print(f"  描述: {follower_config.get('description')}")
                    print(f"  字段数量: {len(follower_config.get('columns', []))}")
                    
                    # 检查关键字段
                    columns = follower_config.get('columns', [])
                    expected_fields = [
                        'date', 'net_follower_increase', 'new_followers', 'unfollowers', 'total_followers'
                    ]
                    
                    actual_fields = [col['key'] for col in columns if col['key'] not in ['account_name', 'updated_at']]
                    missing_fields = set(expected_fields) - set(actual_fields)
                    
                    if not missing_fields:
                        print("✅ 所有必需字段都存在")
                    else:
                        print(f"❌ 缺少字段: {missing_fields}")
                        return False
                else:
                    print("❌ 缺少关注者数据配置")
                    return False
                
                # 检查是否包含单篇视频数据配置
                if 'single_video' in data_types:
                    print("✅ 单篇视频数据配置存在")
                else:
                    print("❌ 缺少单篇视频数据配置")
                    return False
                
            else:
                print(f"❌ API返回失败: {data}")
                return False
        else:
            print(f"❌ API请求失败: {response.status_code}")
            return False
        
        print("✅ API配置测试通过")
        return True
        
    except Exception as e:
        print(f"❌ API配置测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试微信视频号关注者数据功能")
    
    tests = [
        ("数据模型创建", test_model_creation),
        ("CSV解析逻辑", test_csv_parsing_logic),
        ("数据库操作", test_database_operations),
        ("API配置", test_api_configuration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        try:
            if test_func():
                print(f"✅ {test_name} - 通过")
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"微信视频号关注者数据功能测试总结: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有测试通过！")
        print("\n📋 功能总结:")
        print("  ✅ 数据模型创建成功")
        print("  ✅ CSV解析逻辑正确")
        print("  ✅ 数据库操作正常")
        print("  ✅ API配置完整")
        print("\n🚀 微信视频号关注者数据功能已就绪！")
        print("\n📊 支持的关注者数据类型:")
        print("  - 净增关注趋势")
        print("  - 新增关注趋势")
        print("  - 取消关注趋势")
        print("  - 关注者总数趋势")
        print("\n🔗 数据来源:")
        print("  - 页面: https://channels.weixin.qq.com/platform/statistic/follower")
        print("  - API: fans_trend (iframe内)")
        print("  - 文件: CSV下载")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return False


if __name__ == "__main__":
    main()
