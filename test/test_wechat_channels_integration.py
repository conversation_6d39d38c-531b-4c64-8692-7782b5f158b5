#!/usr/bin/env python3
"""
微信视频号数据处理功能集成测试
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import SessionLocal
from app.models import WeChatChannelsVideoData, PlatformAccount
from app.services.data_download_service import DataDownloadService


def test_data_download_service_config():
    """测试DataDownloadService的视频号配置"""
    print("=== 测试DataDownloadService配置 ===")
    
    try:
        # 测试平台数据类型获取
        wechat_channels_types = DataDownloadService.get_platform_data_types("wechat_channels")
        print(f"✅ 视频号支持的数据类型: {wechat_channels_types}")
        
        # 验证配置
        expected_types = ["single_video"]
        if wechat_channels_types == expected_types:
            print("✅ 数据类型配置正确")
        else:
            print(f"❌ 数据类型配置错误，期望: {expected_types}, 实际: {wechat_channels_types}")
            return False
        
        # 测试数据类型配置
        config = DataDownloadService.DATA_TYPE_CONFIG
        if "single_video" in config:
            print(f"✅ 数据类型配置包含single_video: {config['single_video']}")
        else:
            print("❌ 数据类型配置缺少single_video")
            return False
        
        # 测试Excel下载类型
        excel_types = DataDownloadService.WECHAT_CHANNELS_EXCEL_TYPES
        if "single_video" in excel_types:
            print(f"✅ Excel下载类型配置正确: {excel_types}")
        else:
            print(f"❌ Excel下载类型配置错误: {excel_types}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ DataDownloadService配置测试失败: {e}")
        return False


def test_model_mapping():
    """测试模型映射"""
    print("\n=== 测试模型映射 ===")
    
    try:
        from app.services.data_details_service import DataDetailsService
        
        # 检查模型映射
        model_mapping = DataDetailsService.MODEL_MAPPING
        if "single_video" in model_mapping:
            model_class = model_mapping["single_video"]
            print(f"✅ 模型映射正确: single_video -> {model_class.__name__}")
            
            # 验证模型类
            if model_class == WeChatChannelsVideoData:
                print("✅ 模型类匹配正确")
            else:
                print(f"❌ 模型类不匹配，期望: WeChatChannelsVideoData, 实际: {model_class}")
                return False
        else:
            print("❌ 模型映射缺少single_video")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 模型映射测试失败: {e}")
        return False


def test_database_schema():
    """测试数据库表结构"""
    print("\n=== 测试数据库表结构 ===")
    
    try:
        db = SessionLocal()
        
        # 测试表是否存在并可查询
        count = db.query(WeChatChannelsVideoData).count()
        print(f"✅ 数据表查询成功，记录数: {count}")
        
        # 测试表字段
        if count > 0:
            # 如果有数据，获取一条记录测试字段
            sample = db.query(WeChatChannelsVideoData).first()
            fields_to_check = [
                'video_description', 'video_id', 'publish_time', 'completion_rate',
                'avg_play_duration', 'play_count', 'recommend_count', 'like_count',
                'comment_count', 'share_count', 'follow_count', 'forward_chat_moments',
                'set_as_ringtone', 'set_as_status', 'set_as_moments_cover'
            ]
            
            missing_fields = []
            for field in fields_to_check:
                if not hasattr(sample, field):
                    missing_fields.append(field)
            
            if missing_fields:
                print(f"❌ 缺少字段: {missing_fields}")
                return False
            else:
                print("✅ 所有必需字段都存在")
        else:
            print("ℹ️  表为空，无法测试字段，但表结构正常")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库表结构测试失败: {e}")
        return False


def test_service_integration():
    """测试服务集成"""
    print("\n=== 测试服务集成 ===")
    
    try:
        from app.services.wechat_channels_service import WeChatChannelsService
        
        # 测试服务初始化
        service = WeChatChannelsService(account_id=999)  # 使用测试账号ID
        print("✅ WeChatChannelsService初始化成功")
        
        # 测试配置获取
        config = service._get_download_config('single_video')
        if config:
            print(f"✅ 配置获取成功: {config['name']}")
        else:
            print("❌ 配置获取失败")
            return False
        
        # 测试导入方法存在
        if hasattr(service, '_import_excel_to_database'):
            print("✅ 数据导入方法存在")
        else:
            print("❌ 数据导入方法不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 服务集成测试失败: {e}")
        return False


def test_end_to_end_workflow():
    """测试端到端工作流程"""
    print("\n=== 测试端到端工作流程 ===")
    
    try:
        # 这里我们模拟整个工作流程，但不实际下载数据
        print("1. 模拟账号登录状态检查...")
        
        db = SessionLocal()
        try:
            # 查找视频号账号
            channels_accounts = db.query(PlatformAccount).filter(
                PlatformAccount.platform == 'wechat_channels'
            ).all()
            
            if channels_accounts:
                print(f"✅ 找到 {len(channels_accounts)} 个视频号账号")
                for account in channels_accounts:
                    print(f"  - {account.name} (ID: {account.id})")
            else:
                print("ℹ️  没有找到视频号账号，这是正常的")
            
            print("2. 模拟数据类型验证...")
            supported_types = DataDownloadService.get_platform_data_types("wechat_channels")
            print(f"✅ 支持的数据类型: {supported_types}")
            
            print("3. 模拟配置验证...")
            from app.services.wechat_channels_service import WeChatChannelsService
            service = WeChatChannelsService()
            for data_type in supported_types:
                config = service._get_download_config(data_type)
                if config:
                    print(f"✅ {data_type} 配置正常")
                else:
                    print(f"❌ {data_type} 配置缺失")
                    return False
            
            print("4. 模拟数据导入服务验证...")
            from app.services.data_details_service import DataDetailsService
            for data_type in supported_types:
                if data_type in DataDetailsService.MODEL_MAPPING:
                    print(f"✅ {data_type} 模型映射正常")
                else:
                    print(f"❌ {data_type} 模型映射缺失")
                    return False
            
            print("✅ 端到端工作流程验证完成")
            return True
            
        finally:
            db.close()
        
    except Exception as e:
        print(f"❌ 端到端工作流程测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始微信视频号数据处理功能集成测试")
    
    tests = [
        ("DataDownloadService配置", test_data_download_service_config),
        ("模型映射", test_model_mapping),
        ("数据库表结构", test_database_schema),
        ("服务集成", test_service_integration),
        ("端到端工作流程", test_end_to_end_workflow)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"测试: {test_name}")
        print('='*60)
        
        try:
            if test_func():
                print(f"✅ {test_name} - 通过")
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
    
    print(f"\n{'='*60}")
    print(f"集成测试总结: {passed}/{total} 通过")
    print('='*60)
    
    if passed == total:
        print("🎉 所有集成测试通过！")
        print("📋 功能清单:")
        print("  ✅ 数据库表结构正确")
        print("  ✅ 数据模型映射正确")
        print("  ✅ 服务配置完整")
        print("  ✅ 数据导入逻辑正常")
        print("  ✅ 端到端流程完整")
        print("\n🚀 微信视频号数据处理功能已准备就绪！")
        return True
    else:
        print("⚠️  部分集成测试失败，请检查相关功能。")
        return False


if __name__ == "__main__":
    main()
