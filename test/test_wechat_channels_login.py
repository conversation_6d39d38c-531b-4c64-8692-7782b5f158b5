#!/usr/bin/env python3
"""
测试视频号登录功能的脚本
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.wechat_channels_service import WeChatChannelsService

async def test_wechat_channels_login():
    """测试视频号登录流程"""
    print("🎬 开始测试视频号登录功能")
    print("=" * 50)
    
    # 创建视频号服务实例
    service = WeChatChannelsService(account_id=999)  # 使用测试账号ID
    
    try:
        print("📱 步骤1: 获取视频号登录二维码")
        print("-" * 30)
        
        qr_code = await service.get_login_qrcode()
        
        if qr_code == "already_logged_in":
            print("✅ 检测到已有有效的登录状态")
            return True
        elif qr_code and qr_code.startswith("data:image/png;base64,"):
            print(f"✅ 成功获取视频号二维码")
            print(f"📏 二维码数据长度: {len(qr_code)} 字符")
            print("📋 请在浏览器中打开以下链接查看二维码:")
            print(f"data:text/html,<img src='{qr_code}' />")
            
            print("\n📱 步骤2: 等待用户扫码登录")
            print("-" * 30)
            print("请使用微信扫描上述二维码...")
            
            # 模拟等待用户扫码的过程
            max_wait_time = 60  # 最多等待60秒
            check_interval = 3   # 每3秒检查一次
            
            for i in range(0, max_wait_time, check_interval):
                print(f"⏰ 等待扫码中... ({i+check_interval}/{max_wait_time}秒)")
                
                # 检查登录状态
                is_logged_in = await service.check_login_status(wait_for_redirect=True, timeout=check_interval)
                
                if is_logged_in:
                    print("🎉 登录成功！")
                    
                    # 保存登录状态
                    save_result = await service.save_login_state()
                    if save_result:
                        print("💾 登录状态已保存")
                    
                    # 获取cookies
                    cookies = await service.get_cookies()
                    if cookies:
                        print(f"🍪 成功获取cookies，长度: {len(cookies)} 字符")
                    
                    return True
                
                await asyncio.sleep(check_interval)
            
            print("⏰ 等待扫码超时")
            return False
            
        else:
            print("❌ 获取二维码失败")
            if qr_code:
                print(f"返回内容: {qr_code[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理资源
        print("\n🧹 清理资源...")
        await service.close()
        print("✅ 资源清理完成")

async def test_existing_login():
    """测试现有登录状态检查"""
    print("\n🔍 测试现有登录状态检查")
    print("=" * 50)
    
    service = WeChatChannelsService(account_id=999)
    
    try:
        # 尝试加载已保存的登录状态
        load_result = await service.load_login_state()
        if load_result:
            print("✅ 成功加载已保存的登录状态")
            
            # 检查登录是否仍然有效
            is_valid = await service.check_existing_login()
            if is_valid:
                print("✅ 现有登录状态仍然有效")
                return True
            else:
                print("⚠️ 现有登录状态已失效")
                return False
        else:
            print("ℹ️ 未找到已保存的登录状态")
            return False
            
    except Exception as e:
        print(f"❌ 检查现有登录状态时发生错误: {e}")
        return False
        
    finally:
        await service.close()

async def main():
    """主测试函数"""
    print("🚀 视频号登录功能测试开始")
    print("=" * 60)
    
    # 首先检查是否有现有的登录状态
    existing_login = await test_existing_login()
    
    if not existing_login:
        # 如果没有有效的现有登录，进行完整的登录测试
        success = await test_wechat_channels_login()
        
        if success:
            print("\n🎉 视频号登录功能测试成功！")
        else:
            print("\n❌ 视频号登录功能测试失败！")
            
        return success
    else:
        print("\n✅ 检测到有效的现有登录状态，无需重新登录")
        return True

if __name__ == "__main__":
    # 运行测试
    result = asyncio.run(main())
    
    if result:
        print("\n🏆 所有测试通过！")
        sys.exit(0)
    else:
        print("\n💥 测试失败！")
        sys.exit(1)
