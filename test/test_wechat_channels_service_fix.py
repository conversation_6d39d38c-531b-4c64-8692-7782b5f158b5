#!/usr/bin/env python3
"""
测试微信视频号服务修复
"""

import sys
import os
import asyncio
from datetime import datetime, date

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import SessionLocal
from app.models import PlatformAccount, WeChatChannelsFollowerData
from app.services.wechat_channels_service import WeChatChannelsService


def test_service_methods():
    """测试服务方法是否存在"""
    print("=== 测试服务方法 ===")
    
    try:
        # 检查WeChatChannelsService的方法
        service_methods = dir(WeChatChannelsService)
        
        required_methods = [
            '_init_browser',
            '_create_persistent_context',
            'check_login_status',
            'get_follower_data',
            '_download_follower_csv',
            '_parse_follower_csv',
            '_import_follower_to_database'
        ]
        
        missing_methods = []
        existing_methods = []
        
        for method in required_methods:
            if method in service_methods:
                existing_methods.append(method)
                print(f"✅ 方法存在: {method}")
            else:
                missing_methods.append(method)
                print(f"❌ 方法缺失: {method}")
        
        if missing_methods:
            print(f"❌ 缺失方法: {missing_methods}")
            return False
        
        print(f"✅ 所有必需方法都存在: {len(existing_methods)}/{len(required_methods)}")
        return True
        
    except Exception as e:
        print(f"❌ 服务方法测试失败: {e}")
        return False


def test_service_initialization():
    """测试服务初始化"""
    print("\n=== 测试服务初始化 ===")
    
    try:
        # 查找测试账号
        db = SessionLocal()
        try:
            test_account = db.query(PlatformAccount).filter(
                PlatformAccount.platform == 'wechat_channels'
            ).first()
            
            if not test_account:
                print("⚠️  没有找到微信视频号测试账号，创建模拟服务")
                service = WeChatChannelsService(account_id=999, headless=True)
            else:
                print(f"✅ 使用测试账号，ID: {test_account.id}")
                service = WeChatChannelsService(account_id=test_account.id, headless=True)
            
            # 检查服务属性
            print(f"✅ 账号ID: {service.account_id}")
            print(f"✅ Headless模式: {service.headless}")
            print(f"✅ 浏览器状态: {service.browser}")
            print(f"✅ 页面状态: {service.page}")
            
            return True
            
        finally:
            db.close()
        
    except Exception as e:
        print(f"❌ 服务初始化测试失败: {e}")
        return False


async def test_browser_initialization():
    """测试浏览器初始化"""
    print("\n=== 测试浏览器初始化 ===")
    
    try:
        # 创建服务实例
        service = WeChatChannelsService(account_id=999, headless=True)
        
        # 测试浏览器初始化
        print("正在初始化浏览器...")
        await service._init_browser()
        
        if service.browser:
            print("✅ 浏览器初始化成功")
        else:
            print("❌ 浏览器初始化失败")
            return False
        
        # 测试上下文创建
        print("正在创建浏览器上下文...")
        context = await service._create_persistent_context()
        
        if context:
            print("✅ 浏览器上下文创建成功")
        else:
            print("❌ 浏览器上下文创建失败")
            return False
        
        # 清理资源
        await service.close()
        print("✅ 资源清理完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 浏览器初始化测试失败: {e}")
        return False


def test_csv_parsing_logic():
    """测试CSV解析逻辑"""
    print("\n=== 测试CSV解析逻辑 ===")
    
    try:
        # 创建服务实例
        service = WeChatChannelsService(account_id=999, headless=True)
        
        # 测试数字解析方法
        test_numbers = [
            ("123", 123),
            ("1,234", 1234),
            ("1,234,567", 1234567),
            ("-50", -50),
            ("abc", 0),
            ("", 0),
            ("123abc", 123)
        ]
        
        print("测试数字解析:")
        for input_val, expected in test_numbers:
            result = service._parse_number(input_val)
            if result == expected:
                print(f"  ✅ '{input_val}' -> {result}")
            else:
                print(f"  ❌ '{input_val}' -> {result} (期望: {expected})")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ CSV解析逻辑测试失败: {e}")
        return False


def test_database_model():
    """测试数据库模型"""
    print("\n=== 测试数据库模型 ===")
    
    db = SessionLocal()
    try:
        # 测试查询关注者数据表
        result = db.query(WeChatChannelsFollowerData).count()
        print(f"✅ 关注者数据表查询成功，当前记录数: {result}")
        
        # 测试模型字段
        model_fields = [
            'id', 'account_id', 'date', 'net_follower_increase',
            'new_followers', 'unfollowers', 'total_followers',
            'created_at', 'updated_at'
        ]
        
        # 检查模型是否有所需字段
        model_columns = [column.name for column in WeChatChannelsFollowerData.__table__.columns]
        
        missing_fields = []
        for field in model_fields:
            if field in model_columns:
                print(f"✅ 字段存在: {field}")
            else:
                missing_fields.append(field)
                print(f"❌ 字段缺失: {field}")
        
        if missing_fields:
            print(f"❌ 缺失字段: {missing_fields}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库模型测试失败: {e}")
        return False
    finally:
        db.close()


async def main():
    """主测试函数"""
    print("🚀 开始测试微信视频号服务修复")
    
    tests = [
        ("服务方法", test_service_methods, False),
        ("服务初始化", test_service_initialization, False),
        ("浏览器初始化", test_browser_initialization, True),
        ("CSV解析逻辑", test_csv_parsing_logic, False),
        ("数据库模型", test_database_model, False)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func, is_async in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        try:
            if is_async:
                result = await test_func()
            else:
                result = test_func()
                
            if result:
                print(f"✅ {test_name} - 通过")
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"微信视频号服务修复测试总结: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有测试通过！")
        print("\n📋 修复总结:")
        print("  ✅ 服务方法完整")
        print("  ✅ 浏览器初始化正常")
        print("  ✅ CSV解析逻辑正确")
        print("  ✅ 数据库模型完整")
        print("\n🚀 微信视频号关注者数据功能已修复！")
        print("\n🔧 修复内容:")
        print("  - 修复了 _start_browser -> _init_browser 方法名错误")
        print("  - 修复了浏览器上下文创建逻辑")
        print("  - 修复了时间处理的弃用警告")
        print("  - 完善了错误处理机制")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return False


if __name__ == "__main__":
    asyncio.run(main())
