#!/usr/bin/env python3
"""
测试微信公众号数据导入问题
"""

import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import SessionLocal
from app.models import PlatformAccount, WeChatMPContentTrend, WeChatMPContentSource, WeChatMPContentDetail
from sqlalchemy import func


def check_wechat_accounts():
    """检查微信公众号账号"""
    print("=== 检查微信公众号账号 ===")
    
    db = SessionLocal()
    try:
        # 查询微信公众号账号
        accounts = db.query(PlatformAccount).filter(
            PlatformAccount.platform.in_(["wechat_mp", "wechat_service"])
        ).all()
        
        if not accounts:
            print("❌ 没有找到微信公众号账号")
            return False
        
        print(f"✅ 找到 {len(accounts)} 个微信公众号账号:")
        for account in accounts:
            print(f"  - ID: {account.id}, 名称: {account.name}, 平台: {account.platform}, 登录状态: {account.login_status}")
        
        return accounts
        
    except Exception as e:
        print(f"❌ 检查账号失败: {e}")
        return False
    finally:
        db.close()


def check_data_tables():
    """检查数据表中的记录数量"""
    print("\n=== 检查数据表记录数量 ===")
    
    db = SessionLocal()
    try:
        # 检查各个数据表
        tables = [
            ("内容数据趋势明细", WeChatMPContentTrend),
            ("内容流量来源明细", WeChatMPContentSource),
            ("内容已通知内容明细", WeChatMPContentDetail)
        ]
        
        for table_name, model_class in tables:
            count = db.query(func.count(model_class.id)).scalar()
            print(f"  {table_name}: {count} 条记录")
            
            if count > 0:
                # 查看最新的几条记录
                latest_records = db.query(model_class).order_by(model_class.id.desc()).limit(3).all()
                print(f"    最新记录:")
                for record in latest_records:
                    if hasattr(record, 'title'):
                        print(f"      - ID: {record.id}, 账号: {record.account_id}, 标题: {getattr(record, 'title', 'N/A')[:50]}...")
                    else:
                        print(f"      - ID: {record.id}, 账号: {record.account_id}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查数据表失败: {e}")
        return False
    finally:
        db.close()


def check_recent_data_updates():
    """检查最近的数据更新"""
    print("\n=== 检查最近的数据更新 ===")
    
    db = SessionLocal()
    try:
        # 检查最近7天的数据
        seven_days_ago = datetime.now() - timedelta(days=7)
        
        tables = [
            ("内容数据趋势明细", WeChatMPContentTrend),
            ("内容流量来源明细", WeChatMPContentSource),
            ("内容已通知内容明细", WeChatMPContentDetail)
        ]
        
        for table_name, model_class in tables:
            if hasattr(model_class, 'created_at'):
                recent_count = db.query(func.count(model_class.id)).filter(
                    model_class.created_at >= seven_days_ago
                ).scalar()
                print(f"  {table_name} (最近7天): {recent_count} 条记录")
            else:
                print(f"  {table_name}: 无创建时间字段，无法检查最近更新")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查最近数据更新失败: {e}")
        return False
    finally:
        db.close()


def check_data_import_service():
    """检查数据导入服务"""
    print("\n=== 检查数据导入服务 ===")
    
    try:
        from app.services.data_details_service import DataDetailsService
        
        # 检查模型映射
        model_mapping = DataDetailsService.MODEL_MAPPING
        
        required_types = ['content_trend', 'content_source', 'content_detail']
        
        for data_type in required_types:
            if data_type in model_mapping:
                model_class = model_mapping[data_type]
                print(f"✅ {data_type}: 映射到 {model_class.__name__}")
            else:
                print(f"❌ {data_type}: 未找到模型映射")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 检查数据导入服务失败: {e}")
        return False


def check_wechat_service_methods():
    """检查微信服务方法"""
    print("\n=== 检查微信服务方法 ===")
    
    try:
        from app.services.wechat_service import WeChatMPService
        
        # 检查关键方法是否存在
        methods = [
            'download_data_excel',
            'batch_download_data_excel',
            '_import_excel_to_database'
        ]
        
        for method_name in methods:
            if hasattr(WeChatMPService, method_name):
                print(f"✅ {method_name}: 方法存在")
            else:
                print(f"❌ {method_name}: 方法不存在")
                return False
        
        # 检查下载模板配置
        templates = WeChatMPService.DOWNLOAD_TEMPLATES
        required_templates = ['content_trend', 'content_source', 'content_detail']
        
        for template_name in required_templates:
            if template_name in templates:
                template = templates[template_name]
                print(f"✅ {template_name}: 模板配置存在 - {template['name']}")
            else:
                print(f"❌ {template_name}: 模板配置不存在")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 检查微信服务方法失败: {e}")
        return False


def check_data_update_service():
    """检查数据更新服务"""
    print("\n=== 检查数据更新服务 ===")
    
    try:
        from app.services.data_update_service import DataUpdateService
        
        # 检查数据类型配置
        wechat_data_types = DataUpdateService.WECHAT_MP_DATA_TYPES
        required_types = ['content_trend', 'content_source', 'content_detail']
        
        for data_type in required_types:
            if data_type in wechat_data_types:
                print(f"✅ {data_type}: 在数据更新服务中配置")
            else:
                print(f"❌ {data_type}: 未在数据更新服务中配置")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 检查数据更新服务失败: {e}")
        return False


def main():
    """主检查函数"""
    print("🔍 检查微信公众号数据导入问题")
    
    checks = [
        ("微信公众号账号", check_wechat_accounts),
        ("数据表记录数量", check_data_tables),
        ("最近数据更新", check_recent_data_updates),
        ("数据导入服务", check_data_import_service),
        ("微信服务方法", check_wechat_service_methods),
        ("数据更新服务", check_data_update_service)
    ]
    
    passed = 0
    total = len(checks)
    
    for check_name, check_func in checks:
        print(f"\n{'='*50}")
        print(f"检查: {check_name}")
        print('='*50)
        
        try:
            result = check_func()
            if result:
                print(f"✅ {check_name} - 通过")
                passed += 1
            else:
                print(f"❌ {check_name} - 失败")
        except Exception as e:
            print(f"❌ {check_name} - 异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"检查结果总结: {passed}/{total} 通过")
    print('='*50)
    
    if passed < total:
        print("\n🔧 可能的问题和解决方案:")
        print("1. 检查微信公众号账号是否正确登录")
        print("2. 检查数据更新任务是否正常执行")
        print("3. 检查数据导入过程中是否有错误日志")
        print("4. 检查Excel文件是否正确下载")
        print("5. 检查数据库连接是否正常")
        print("\n📝 调试建议:")
        print("- 查看应用日志中的数据导入相关信息")
        print("- 手动触发一次数据更新并观察日志")
        print("- 检查Excel文件内容是否符合预期格式")
        print("- 验证数据库表结构是否正确")
    else:
        print("\n✅ 所有检查都通过，数据导入配置正常")
    
    return passed == total


if __name__ == "__main__":
    main()
