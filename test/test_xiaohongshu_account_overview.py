#!/usr/bin/env python3
"""
测试小红书账号概览功能
"""

import sys
import os
from datetime import datetime, date
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import SessionLocal
from app.models import XiaohongshuAccountOverview, PlatformAccount
from app.services.xiaohongshu_service import XiaohongshuService


def test_model_creation():
    """测试数据模型创建"""
    print("=== 测试数据模型创建 ===")
    
    db = SessionLocal()
    try:
        # 测试查询表是否存在
        result = db.query(XiaohongshuAccountOverview).count()
        print(f"✅ 小红书账号概览数据表查询成功，当前记录数: {result}")
        return True
    except Exception as e:
        print(f"❌ 数据表查询失败: {e}")
        return False
    finally:
        db.close()


def test_data_import_logic():
    """测试数据导入逻辑"""
    print("\n=== 测试数据导入逻辑 ===")
    
    try:
        # 模拟API返回的数据结构
        mock_overview_data = {
            "view_list": [
                {"date": *********0000, "count": 1500},  # 2024-01-01
                {"date": *************, "count": 1800},  # 2024-01-02
                {"date": *************, "count": 2100}   # 2024-01-03
            ],
            "view_time_list": [
                {"date": *********0000, "count": 3600},
                {"date": *************, "count": 4200},
                {"date": *************, "count": 4800}
            ],
            "home_view_list": [
                {"date": *********0000, "count": 120},
                {"date": *************, "count": 150},
                {"date": *************, "count": 180}
            ],
            "like_list": [
                {"date": *********0000, "count": 200},
                {"date": *************, "count": 250},
                {"date": *************, "count": 300}
            ],
            "collect_list": [
                {"date": *********0000, "count": 80},
                {"date": *************, "count": 100},
                {"date": *************, "count": 120}
            ],
            "comment_list": [
                {"date": *********0000, "count": 50},
                {"date": *************, "count": 60},
                {"date": *************, "count": 70}
            ],
            "danmaku_list": [
                {"date": *********0000, "count": 20},
                {"date": *************, "count": 25},
                {"date": *************, "count": 30}
            ],
            "rise_fans_list": [
                {"date": *********0000, "count": 15},
                {"date": *************, "count": 20},
                {"date": *************, "count": 25}
            ],
            "share_list": [
                {"date": *********0000, "count": 40},
                {"date": *************, "count": 50},
                {"date": *************, "count": 60}
            ]
        }
        
        # 测试时间戳转换
        timestamp_ms = *********0000  # 2024-01-01 00:00:00 UTC
        timestamp_s = timestamp_ms / 1000
        converted_date = datetime.fromtimestamp(timestamp_s).date()
        expected_date = date(2024, 1, 1)
        
        if converted_date == expected_date:
            print("✅ 时间戳转换正确")
        else:
            print(f"❌ 时间戳转换错误: {converted_date} != {expected_date}")
            return False
        
        # 测试数据结构解析
        trend_mappings = {
            'view_list': 'view_count',
            'view_time_list': 'view_time_count', 
            'home_view_list': 'home_view_count',
            'like_list': 'like_count',
            'collect_list': 'collect_count',
            'comment_list': 'comment_count',
            'danmaku_list': 'danmaku_count',
            'rise_fans_list': 'rise_fans_count',
            'share_list': 'share_count'
        }
        
        # 收集所有日期的数据
        date_data = {}
        
        for api_field, db_field in trend_mappings.items():
            trend_list = mock_overview_data.get(api_field, [])
            for item in trend_list:
                if 'date' in item and 'count' in item:
                    timestamp_ms = item['date']
                    timestamp_s = timestamp_ms / 1000
                    item_date = datetime.fromtimestamp(timestamp_s).date()
                    
                    if item_date not in date_data:
                        date_data[item_date] = {}
                    
                    date_data[item_date][db_field] = item['count']
        
        # 验证解析结果
        expected_dates = [date(2024, 1, 1), date(2024, 1, 2), date(2024, 1, 3)]
        if set(date_data.keys()) == set(expected_dates):
            print("✅ 日期解析正确")
        else:
            print(f"❌ 日期解析错误: {list(date_data.keys())} != {expected_dates}")
            return False
        
        # 验证数据字段
        first_date_data = date_data[date(2024, 1, 1)]
        expected_fields = set(trend_mappings.values())
        if set(first_date_data.keys()) == expected_fields:
            print("✅ 数据字段解析正确")
        else:
            print(f"❌ 数据字段解析错误: {set(first_date_data.keys())} != {expected_fields}")
            return False
        
        # 验证数据值
        if first_date_data['view_count'] == 1500 and first_date_data['like_count'] == 200:
            print("✅ 数据值解析正确")
        else:
            print(f"❌ 数据值解析错误")
            return False
        
        print("✅ 数据导入逻辑测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 数据导入逻辑测试失败: {e}")
        return False


def test_database_operations():
    """测试数据库操作"""
    print("\n=== 测试数据库操作 ===")
    
    db = SessionLocal()
    try:
        # 查找或创建测试账号
        test_account = db.query(PlatformAccount).filter(
            PlatformAccount.platform == 'xiaohongshu'
        ).first()
        
        if not test_account:
            print("❌ 需要先创建小红书测试账号")
            return False
        
        print(f"✅ 使用测试账号，ID: {test_account.id}")
        
        # 创建测试数据
        test_date = date(2024, 1, 1)
        test_data = {
            'account_id': test_account.id,
            'date': test_date,
            'view_count': 1500,
            'view_time_count': 3600,
            'home_view_count': 120,
            'like_count': 200,
            'collect_count': 80,
            'comment_count': 50,
            'danmaku_count': 20,
            'rise_fans_count': 15,
            'share_count': 40
        }
        
        # 删除可能存在的测试数据
        db.query(XiaohongshuAccountOverview).filter(
            XiaohongshuAccountOverview.account_id == test_account.id,
            XiaohongshuAccountOverview.date == test_date
        ).delete()
        db.commit()
        
        # 插入新数据
        record = XiaohongshuAccountOverview(**test_data)
        db.add(record)
        db.commit()
        
        # 验证插入
        inserted_record = db.query(XiaohongshuAccountOverview).filter(
            XiaohongshuAccountOverview.account_id == test_account.id,
            XiaohongshuAccountOverview.date == test_date
        ).first()
        
        if inserted_record:
            print("✅ 数据插入成功")
            print(f"  观看量: {inserted_record.view_count}")
            print(f"  点赞数: {inserted_record.like_count}")
            print(f"  涨粉数: {inserted_record.rise_fans_count}")
        else:
            print("❌ 数据插入失败")
            return False
        
        # 测试更新
        inserted_record.view_count = 2000
        inserted_record.updated_at = datetime.utcnow()
        db.commit()
        
        # 验证更新
        updated_record = db.query(XiaohongshuAccountOverview).filter(
            XiaohongshuAccountOverview.account_id == test_account.id,
            XiaohongshuAccountOverview.date == test_date
        ).first()
        
        if updated_record and updated_record.view_count == 2000:
            print("✅ 数据更新成功")
        else:
            print("❌ 数据更新失败")
            return False
        
        # 清理测试数据
        db.delete(updated_record)
        db.commit()
        print("✅ 测试数据清理完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库操作测试失败: {e}")
        return False
    finally:
        db.close()


def test_api_configuration():
    """测试API配置"""
    print("\n=== 测试API配置 ===")
    
    try:
        import requests
        base_url = 'http://localhost:8000'
        
        # 测试小红书配置API
        response = requests.get(f'{base_url}/api/data-details/xiaohongshu/config', timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                data_types = data.get('data_types', {})
                
                # 检查是否包含账号概览配置
                if 'account_overview' in data_types:
                    overview_config = data_types['account_overview']
                    print("✅ 账号概览配置存在")
                    print(f"  名称: {overview_config.get('name')}")
                    print(f"  描述: {overview_config.get('description')}")
                    print(f"  字段数量: {len(overview_config.get('columns', []))}")
                    
                    # 检查关键字段
                    columns = overview_config.get('columns', [])
                    expected_fields = [
                        'date', 'view_count', 'view_time_count', 'home_view_count',
                        'like_count', 'collect_count', 'comment_count', 'danmaku_count',
                        'rise_fans_count', 'share_count'
                    ]
                    
                    actual_fields = [col['key'] for col in columns if col['key'] not in ['account_name', 'updated_at']]
                    missing_fields = set(expected_fields) - set(actual_fields)
                    
                    if not missing_fields:
                        print("✅ 所有必需字段都存在")
                    else:
                        print(f"❌ 缺少字段: {missing_fields}")
                        return False
                else:
                    print("❌ 缺少账号概览配置")
                    return False
                
                # 检查是否包含笔记数据配置
                if 'note_data' in data_types:
                    print("✅ 笔记数据配置存在")
                else:
                    print("❌ 缺少笔记数据配置")
                    return False
                
            else:
                print(f"❌ API返回失败: {data}")
                return False
        else:
            print(f"❌ API请求失败: {response.status_code}")
            return False
        
        print("✅ API配置测试通过")
        return True
        
    except Exception as e:
        print(f"❌ API配置测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试小红书账号概览功能")
    
    tests = [
        ("数据模型创建", test_model_creation),
        ("数据导入逻辑", test_data_import_logic),
        ("数据库操作", test_database_operations),
        ("API配置", test_api_configuration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        try:
            if test_func():
                print(f"✅ {test_name} - 通过")
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"小红书账号概览功能测试总结: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有测试通过！")
        print("\n📋 功能总结:")
        print("  ✅ 数据模型创建成功")
        print("  ✅ 数据导入逻辑正确")
        print("  ✅ 数据库操作正常")
        print("  ✅ API配置完整")
        print("\n🚀 小红书账号概览功能已就绪！")
        print("\n📊 支持的数据类型:")
        print("  - 观看趋势")
        print("  - 观看总时长趋势")
        print("  - 主页访客趋势")
        print("  - 点赞趋势")
        print("  - 收藏趋势")
        print("  - 评论趋势")
        print("  - 弹幕趋势")
        print("  - 涨粉趋势")
        print("  - 分享趋势")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return False


if __name__ == "__main__":
    main()
