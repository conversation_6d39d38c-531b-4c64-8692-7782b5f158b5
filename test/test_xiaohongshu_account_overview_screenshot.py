#!/usr/bin/env python3
"""
测试小红书账号总览截图功能
"""

import os
import asyncio
import inspect

def test_account_overview_screenshot_method():
    """测试账号总览截图方法是否存在"""
    print("=== 测试账号总览截图方法是否存在 ===")
    
    try:
        # 导入服务类
        import sys
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        from app.services.xiaohongshu_service import XiaohongshuService
        
        # 检查方法是否存在
        if hasattr(XiaohongshuService, 'screenshot_account_overview'):
            print("✅ screenshot_account_overview方法存在")
            
            # 检查方法是否是异步的
            method = getattr(XiaohongshuService, 'screenshot_account_overview')
            if asyncio.iscoroutinefunction(method):
                print("✅ screenshot_account_overview方法是异步方法")
            else:
                print("❌ screenshot_account_overview方法不是异步方法")
                return False
            
            # 检查方法签名
            sig = inspect.signature(method)
            params = list(sig.parameters.keys())
            
            if 'self' in params and 'save_path' in params:
                print("✅ 方法签名正确，包含self和save_path参数")
            else:
                print(f"❌ 方法签名错误，参数: {params}")
                return False
            
            return True
        else:
            print("❌ screenshot_account_overview方法不存在")
            return False
        
    except Exception as e:
        print(f"❌ 测试账号总览截图方法失败: {e}")
        return False


def test_account_overview_implementation():
    """测试账号总览截图方法实现"""
    print("\n=== 测试账号总览截图方法实现 ===")
    
    try:
        service_file = "app/services/xiaohongshu_service.py"
        
        if os.path.exists(service_file):
            print(f"✅ 小红书服务文件存在")
            
            with open(service_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # 检查账号总览截图方法实现
                implementation_checks = [
                    ("方法定义", "async def screenshot_account_overview(self, save_path: str)"),
                    ("返回类型注解", "-> Optional[str]"),
                    ("账号ID检查", "if not self.account_id:"),
                    ("启动浏览器", "await self._start_browser()"),
                    ("检查登录状态", "await self.check_existing_login()"),
                    ("访问账号总览页面", "creator.xiaohongshu.com/statistics/account"),
                    ("等待页面加载", "await asyncio.sleep(3)"),
                    ("切换到近30日", "正在切换到近30日"),
                    ("点击近30日", "get_by_text(\"近30日\").first"),
                    ("点击操作", "await thirty_days_option.click()"),
                    ("等待数据刷新", "await asyncio.sleep(3)"),
                    ("等待选择器", "wait_for_selector"),
                    ("目标选择器", "#content-area > main > div:nth-child(3) > div > div > div > div.data > div > div.d-tabs-pane > div > div.datas"),
                    ("创建目录", "os.makedirs"),
                    ("元素截图", "await element.screenshot"),
                    ("异常处理", "except Exception as e:")
                ]
                
                all_passed = True
                for check_name, check_content in implementation_checks:
                    if check_content in content:
                        print(f"✅ {check_name}: 存在")
                    else:
                        print(f"❌ {check_name}: 缺失")
                        all_passed = False
                
                return all_passed
        else:
            print(f"❌ 小红书服务文件不存在")
            return False
        
    except Exception as e:
        print(f"❌ 测试账号总览截图方法实现失败: {e}")
        return False


def test_data_download_service_integration():
    """测试数据下载服务集成"""
    print("\n=== 测试数据下载服务集成 ===")
    
    try:
        download_service_file = "app/services/data_download_service.py"
        
        if os.path.exists(download_service_file):
            print(f"✅ 数据下载服务文件存在")
            
            with open(download_service_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # 检查数据下载服务中的账号总览截图集成
                integration_checks = [
                    ("账号总览截图开始日志", "开始截图小红书账号.*的账号总览"),
                    ("账号总览文件名生成", "xiaohongshu_{account.name}_account_overview"),
                    ("账号总览截图路径", "account_screenshot_path"),
                    ("调用账号总览截图方法", "await xiaohongshu_service.screenshot_account_overview"),
                    ("账号总览成功处理", "account_overview_screenshot"),
                    ("账号总览失败处理", "截图账号总览失败"),
                    ("账号总览异常捕获", "except Exception as e:")
                ]
                
                all_passed = True
                for check_name, check_content in integration_checks:
                    if check_content in content:
                        print(f"✅ {check_name}: 存在")
                    else:
                        print(f"❌ {check_name}: 缺失")
                        all_passed = False
                
                return all_passed
        else:
            print(f"❌ 数据下载服务文件不存在")
            return False
        
    except Exception as e:
        print(f"❌ 测试数据下载服务集成失败: {e}")
        return False


def test_file_naming_and_structure():
    """测试文件命名和结构"""
    print("\n=== 测试文件命名和结构 ===")
    
    try:
        download_service_file = "app/services/data_download_service.py"
        
        with open(download_service_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
            # 检查文件命名和结构
            naming_checks = [
                ("粉丝概览文件名", "xiaohongshu_{account.name}_fans_overview_{start_date}_{end_date}.png"),
                ("账号总览文件名", "xiaohongshu_{account.name}_account_overview_{start_date}_{end_date}.png"),
                ("粉丝概览数据类型", "fans_overview_screenshot"),
                ("账号总览数据类型", "account_overview_screenshot"),
                ("PNG格式", ".png"),
                ("包含平台名", "xiaohongshu_"),
                ("包含账号名", "{account.name}"),
                ("包含日期范围", "{start_date}_{end_date}")
            ]
            
            all_passed = True
            for check_name, check_content in naming_checks:
                if check_content in content:
                    print(f"✅ {check_name}: 正确")
                else:
                    print(f"❌ {check_name}: 错误")
                    all_passed = False
            
            return all_passed
        
    except Exception as e:
        print(f"❌ 测试文件命名和结构失败: {e}")
        return False


def test_complete_download_flow():
    """测试完整下载流程"""
    print("\n=== 测试完整下载流程 ===")
    
    try:
        download_service_file = "app/services/data_download_service.py"
        
        with open(download_service_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
            # 检查完整下载流程
            flow_checks = [
                ("笔记数据下载", "download_note_data_excel"),
                ("粉丝概览截图", "screenshot_fans_overview"),
                ("账号总览截图", "screenshot_account_overview"),
                ("文件添加到下载列表", "downloaded_files.append"),
                ("错误记录", "failed_files.append"),
                ("日志记录", "logger.info"),
                ("异常处理", "except Exception as e:")
            ]
            
            all_passed = True
            for check_name, check_content in flow_checks:
                if check_content in content:
                    print(f"✅ {check_name}: 存在")
                else:
                    print(f"❌ {check_name}: 缺失")
                    all_passed = False
            
            return all_passed
        
    except Exception as e:
        print(f"❌ 测试完整下载流程失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 测试小红书账号总览截图功能")
    
    tests = [
        ("账号总览截图方法", test_account_overview_screenshot_method),
        ("账号总览截图实现", test_account_overview_implementation),
        ("数据下载服务集成", test_data_download_service_integration),
        ("文件命名和结构", test_file_naming_and_structure),
        ("完整下载流程", test_complete_download_flow)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            if result:
                print(f"✅ {test_name} - 通过")
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"小红书账号总览截图功能测试总结: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 小红书账号总览截图功能实现完成！")
        print("\n📋 功能总结:")
        print("  ✅ 添加了screenshot_account_overview方法")
        print("  ✅ 集成到数据下载服务")
        print("  ✅ 自动切换到近30日数据")
        print("  ✅ 完善的错误处理")
        print("  ✅ 规范的文件命名")
        print("\n🎯 现在小红书数据下载包含:")
        print("  📊 笔记数据Excel文件")
        print("  📸 粉丝数据概览截图（30天）")
        print("  📈 账号总览截图（30日）")
        print("\n🔧 技术实现:")
        print("  - 访问: https://creator.xiaohongshu.com/statistics/account")
        print("  - 切换: 点击'近30日'选项")
        print("  - 截图: #content-area > main > div:nth-child(3) > div > div > div > div.data > div > div.d-tabs-pane > div > div.datas")
        print("  - 保存: xiaohongshu_{账号名}_account_overview_{开始日期}_{结束日期}.png")
        print("\n💡 用户价值:")
        print("  - 获得完整的小红书账号数据包")
        print("  - 包含结构化数据和可视化图表")
        print("  - 30天/30日数据保持一致性")
        print("  - 便于数据分析和报告制作")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return False


if __name__ == "__main__":
    main()
