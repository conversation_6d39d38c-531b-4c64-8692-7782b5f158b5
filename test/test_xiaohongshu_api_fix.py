#!/usr/bin/env python3
"""
测试小红书API路由修复
"""

import sys
import os
import requests
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def test_api_endpoints():
    """测试API端点"""
    print("=== 测试小红书API端点 ===")
    
    base_url = 'http://localhost:8000'
    
    # 测试配置API
    try:
        print("测试配置API...")
        response = requests.get(f'{base_url}/api/data-details/xiaohongshu/config', timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 配置API正常:")
            print(f"  状态码: {response.status_code}")
            print(f"  成功: {data.get('success')}")
            print(f"  数据类型数量: {len(data.get('data_types', {}))}")
            
            # 验证配置内容
            data_types = data.get('data_types', {})
            if 'note_data' in data_types:
                note_config = data_types['note_data']
                print(f"  note_data配置:")
                print(f"    名称: {note_config.get('name')}")
                print(f"    描述: {note_config.get('description')}")
                print(f"    字段数量: {len(note_config.get('columns', []))}")
                
                # 验证关键字段
                columns = note_config.get('columns', [])
                expected_fields = [
                    'note_title', 'first_publish_time', 'content_type', 
                    'view_count', 'like_count', 'comment_count', 'collect_count',
                    'follow_count', 'share_count', 'avg_view_duration', 'barrage_count'
                ]
                
                actual_fields = [col['key'] for col in columns if col['key'] != 'account_name' and col['key'] != 'updated_at']
                missing_fields = set(expected_fields) - set(actual_fields)
                
                if not missing_fields:
                    print("    ✅ 所有字段配置正确")
                else:
                    print(f"    ❌ 缺少字段: {missing_fields}")
                    return False
            else:
                print("  ❌ 缺少note_data配置")
                return False
        else:
            print(f"❌ 配置API失败: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 配置API测试异常: {e}")
        return False
    
    # 测试账号API
    try:
        print("\n测试账号API...")
        response = requests.get(f'{base_url}/api/data-details/xiaohongshu/accounts', timeout=5)
        
        if response.status_code == 403:
            print("✅ 账号API正常 (需要认证，返回403是正确的)")
        elif response.status_code == 200:
            data = response.json()
            print(f"✅ 账号API正常:")
            print(f"  状态码: {response.status_code}")
            print(f"  成功: {data.get('success')}")
            print(f"  账号数量: {len(data.get('accounts', []))}")
        else:
            print(f"❌ 账号API异常: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 账号API测试异常: {e}")
        return False
    
    return True


def test_route_conflicts():
    """测试路由冲突修复"""
    print("\n=== 测试路由冲突修复 ===")
    
    base_url = 'http://localhost:8000'
    
    # 测试具体路径不会被参数化路径拦截
    test_cases = [
        ('/api/data-details/xiaohongshu/config', '配置路径'),
        ('/api/data-details/xiaohongshu/accounts', '账号路径'),
    ]
    
    for path, description in test_cases:
        try:
            response = requests.get(f'{base_url}{path}', timeout=5)
            
            # 这些路径不应该返回400错误（路由冲突）
            if response.status_code != 400:
                print(f"✅ {description}路由正常 (状态码: {response.status_code})")
            else:
                print(f"❌ {description}路由冲突 (状态码: {response.status_code})")
                print(f"  响应: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ {description}路由测试异常: {e}")
            return False
    
    return True


def test_data_type_validation():
    """测试数据类型验证"""
    print("\n=== 测试数据类型验证 ===")
    
    base_url = 'http://localhost:8000'
    
    # 测试无效的数据类型应该返回400
    try:
        response = requests.get(f'{base_url}/api/data-details/xiaohongshu/invalid_type', timeout=5)
        
        if response.status_code == 400 or response.status_code == 403:
            print("✅ 无效数据类型正确处理")
        else:
            print(f"❌ 无效数据类型处理异常: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 数据类型验证测试异常: {e}")
        return False
    
    return True


def test_api_response_format():
    """测试API响应格式"""
    print("\n=== 测试API响应格式 ===")
    
    base_url = 'http://localhost:8000'
    
    try:
        response = requests.get(f'{base_url}/api/data-details/xiaohongshu/config', timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            
            # 验证响应格式
            required_fields = ['success', 'data_types']
            missing_fields = [field for field in required_fields if field not in data]
            
            if not missing_fields:
                print("✅ API响应格式正确")
                
                # 验证数据类型结构
                data_types = data.get('data_types', {})
                if 'note_data' in data_types:
                    note_config = data_types['note_data']
                    config_fields = ['name', 'description', 'columns']
                    missing_config_fields = [field for field in config_fields if field not in note_config]
                    
                    if not missing_config_fields:
                        print("✅ 数据类型配置结构正确")
                    else:
                        print(f"❌ 数据类型配置缺少字段: {missing_config_fields}")
                        return False
                else:
                    print("❌ 缺少note_data配置")
                    return False
            else:
                print(f"❌ API响应缺少字段: {missing_fields}")
                return False
        else:
            print(f"❌ API响应异常: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API响应格式测试异常: {e}")
        return False
    
    return True


def main():
    """主测试函数"""
    print("🚀 开始测试小红书API路由修复")
    
    tests = [
        ("API端点", test_api_endpoints),
        ("路由冲突修复", test_route_conflicts),
        ("数据类型验证", test_data_type_validation),
        ("API响应格式", test_api_response_format)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        try:
            if test_func():
                print(f"✅ {test_name} - 通过")
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"API修复测试总结: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有API修复测试通过！")
        print("📋 修复内容:")
        print("  ✅ 路由冲突已解决")
        print("  ✅ API端点正常工作")
        print("  ✅ 配置API返回正确数据")
        print("  ✅ 账号API认证正常")
        print("  ✅ 数据类型验证正常")
        print("\n🚀 小红书API路由修复完成！")
        return True
    else:
        print("⚠️  部分API修复测试失败，请检查相关功能。")
        return False


if __name__ == "__main__":
    main()
