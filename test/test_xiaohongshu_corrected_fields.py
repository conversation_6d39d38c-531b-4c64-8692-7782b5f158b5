#!/usr/bin/env python3
"""
测试修正后的小红书数据字段
"""

import sys
import os
import pandas as pd
from datetime import datetime
import io

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import SessionLocal
from app.models import XiaohongshuNoteData, PlatformAccount
from app.services.data_details_service import DataDetailsService
from app.services.xiaohongshu_service import XiaohongshuService


def test_corrected_field_config():
    """测试修正后的字段配置"""
    print("=== 测试修正后的字段配置 ===")
    
    try:
        service = XiaohongshuService()
        config = service._get_download_config('note_data')
        
        if config:
            print(f"✅ 配置获取成功:")
            print(f"  名称: {config.get('name')}")
            print(f"  数据起始行: {config.get('data_start_row')}")
            print(f"  字段数量: {len(config.get('fields', []))}")
            
            # 验证字段配置
            expected_fields = [
                '笔记标题', '首次发布时间', '体裁', '观看量', '点赞', 
                '评论', '收藏', '涨粉', '分享', '人均观看时长', '弹幕'
            ]
            
            fields = config.get('fields', [])
            actual_fields = [field[0] for field in fields]
            
            print("  字段配置:")
            for field_name, field_type in fields:
                type_name = {1: "文本", 2: "数字"}.get(field_type, f"类型{field_type}")
                print(f"    - {field_name}: {type_name}")
            
            # 检查字段是否匹配
            missing_fields = set(expected_fields) - set(actual_fields)
            extra_fields = set(actual_fields) - set(expected_fields)
            
            if not missing_fields and not extra_fields:
                print("✅ 字段配置完全匹配")
                return True
            else:
                if missing_fields:
                    print(f"❌ 缺少字段: {missing_fields}")
                if extra_fields:
                    print(f"❌ 多余字段: {extra_fields}")
                return False
            
        else:
            print("❌ 配置获取失败")
            return False
            
    except Exception as e:
        print(f"❌ 字段配置测试失败: {e}")
        return False


def create_corrected_test_data():
    """创建修正后的测试数据"""
    print("\n=== 创建修正后的测试数据 ===")
    
    try:
        # 创建符合新字段的测试数据
        test_data = {
            '笔记标题': ['春日穿搭分享', '美食探店记录', '旅行vlog'],
            '首次发布时间': ['2025-01-01 10:00:00', '2025-01-02 15:30:00', '2025-01-03 20:45:00'],
            '体裁': ['图文', '视频', '视频'],
            '观看量': [15000, 28000, 12000],
            '点赞': [1200, 2500, 980],
            '评论': [150, 320, 120],
            '收藏': [800, 1500, 600],
            '涨粉': [50, 120, 35],
            '分享': [200, 450, 180],
            '人均观看时长': ['1:30', '2:15', '0:45'],  # 时间格式
            '弹幕': [80, 150, 60]
        }
        
        # 创建DataFrame
        df = pd.DataFrame(test_data)
        
        # 转换为Excel字节数据
        excel_buffer = io.BytesIO()
        df.to_excel(excel_buffer, index=False, engine='openpyxl')
        excel_content = excel_buffer.getvalue()
        
        print(f"✅ 修正后的测试数据创建成功，大小: {len(excel_content)} bytes")
        print(f"  数据行数: {len(df)}")
        print(f"  字段数: {len(df.columns)}")
        
        # 显示样本数据
        print("  样本数据:")
        for i, row in df.head(2).iterrows():
            print(f"    笔记{i+1}: {row['笔记标题']}")
            print(f"      观看量: {row['观看量']}, 点赞: {row['点赞']}")
            print(f"      人均观看时长: {row['人均观看时长']}, 弹幕: {row['弹幕']}")
        
        return excel_content
        
    except Exception as e:
        print(f"❌ 创建修正后的测试数据失败: {e}")
        return None


def test_duration_parsing():
    """测试时长解析功能"""
    print("\n=== 测试时长解析功能 ===")
    
    try:
        # 测试不同格式的时长解析
        test_cases = [
            ('1:30', 90.0),      # 1分30秒 = 90秒
            ('0:45', 45.0),      # 45秒
            ('2:15', 135.0),     # 2分15秒 = 135秒
            ('90', 90.0),        # 直接90秒
            ('1.5', 1.5),        # 1.5秒
            ('', 0.0),           # 空字符串
            (None, 0.0),         # None值
            ('invalid', 0.0),    # 无效格式
        ]
        
        print("测试时长解析:")
        all_passed = True
        
        for input_val, expected in test_cases:
            result = DataDetailsService._parse_duration_to_seconds(input_val)
            if result == expected:
                print(f"  ✅ '{input_val}' -> {result}秒")
            else:
                print(f"  ❌ '{input_val}' -> {result}秒 (期望: {expected}秒)")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 时长解析测试失败: {e}")
        return False


def test_corrected_data_import():
    """测试修正后的数据导入"""
    print("\n=== 测试修正后的数据导入 ===")
    
    try:
        # 创建修正后的测试数据
        excel_content = create_corrected_test_data()
        if not excel_content:
            return False
        
        db = SessionLocal()
        try:
            # 清理旧的测试数据
            db.query(XiaohongshuNoteData).delete()
            db.commit()
            print("✅ 清理旧的测试数据")
            
            # 获取测试账号
            test_account = db.query(PlatformAccount).filter(
                PlatformAccount.platform == 'xiaohongshu'
            ).first()
            
            if not test_account:
                print("❌ 找不到测试账号")
                return False
            
            print(f"✅ 使用测试账号，ID: {test_account.id}")
            
            # 测试数据导入
            print("开始导入修正后的数据...")
            result = DataDetailsService.import_excel_data(
                db=db,
                account_id=test_account.id,
                data_type='note_data',
                excel_content=excel_content
            )
            
            if result["success"]:
                print(f"✅ 数据导入成功:")
                print(f"  新增记录: {result['imported_count']}")
                print(f"  更新记录: {result['updated_count']}")
                print(f"  总处理数: {result['total_processed']}")
                
                # 验证导入的数据
                records = db.query(XiaohongshuNoteData).filter(
                    XiaohongshuNoteData.account_id == test_account.id
                ).all()
                
                print(f"  数据库中的记录数: {len(records)}")
                
                # 验证字段数据
                print("  验证导入的数据:")
                for record in records:
                    print(f"    笔记: {record.note_title}")
                    print(f"      首次发布时间: {record.first_publish_time}")
                    print(f"      体裁: {record.content_type}")
                    print(f"      观看量: {record.view_count}")
                    print(f"      点赞: {record.like_count}")
                    print(f"      人均观看时长: {record.avg_view_duration}秒")
                    print(f"      弹幕: {record.barrage_count}")
                    print()
                
                # 验证时长解析是否正确
                expected_durations = [90.0, 135.0, 45.0]  # 对应1:30, 2:15, 0:45
                actual_durations = [r.avg_view_duration for r in records]
                
                if sorted(actual_durations) == sorted(expected_durations):
                    print("✅ 时长解析正确")
                else:
                    print(f"❌ 时长解析错误，期望: {expected_durations}, 实际: {actual_durations}")
                    return False
                
                return True
            else:
                print(f"❌ 数据导入失败: {result['error']}")
                return False
                
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ 修正后的数据导入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_model_fields():
    """测试数据模型字段"""
    print("\n=== 测试数据模型字段 ===")
    
    try:
        # 检查模型字段
        model = XiaohongshuNoteData
        
        expected_fields = [
            'note_title', 'first_publish_time', 'content_type', 'view_count',
            'like_count', 'comment_count', 'collect_count', 'follow_count',
            'share_count', 'avg_view_duration', 'barrage_count'
        ]
        
        print("检查模型字段:")
        all_fields_exist = True
        
        for field_name in expected_fields:
            if hasattr(model, field_name):
                print(f"  ✅ {field_name}")
            else:
                print(f"  ❌ {field_name} (缺失)")
                all_fields_exist = False
        
        return all_fields_exist
        
    except Exception as e:
        print(f"❌ 模型字段测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试修正后的小红书数据字段")
    
    tests = [
        ("修正后的字段配置", test_corrected_field_config),
        ("时长解析功能", test_duration_parsing),
        ("数据模型字段", test_model_fields),
        ("修正后的数据导入", test_corrected_data_import)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        try:
            if test_func():
                print(f"✅ {test_name} - 通过")
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"修正测试总结: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有修正测试通过！")
        print("📋 修正内容:")
        print("  ✅ 字段名称已更新为实际字段")
        print("  ✅ 数据模型结构已调整")
        print("  ✅ 时长解析功能正常")
        print("  ✅ 数据导入功能正常")
        print("  ✅ 唯一约束已更新")
        print("\n🚀 小红书数据字段修正完成！")
        return True
    else:
        print("⚠️  部分修正测试失败，请检查相关功能。")
        return False


if __name__ == "__main__":
    main()
