#!/usr/bin/env python3
"""
测试小红书数据导入功能
"""

import sys
import os
import pandas as pd
from datetime import datetime, date
import io

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import SessionLocal
from app.models import XiaohongshuNoteData, PlatformAccount, User
from app.services.data_details_service import DataDetailsService
from app.services.xiaohongshu_service import XiaohongshuService


def test_model_creation():
    """测试数据模型创建"""
    print("=== 测试数据模型创建 ===")
    
    db = SessionLocal()
    try:
        # 测试查询表是否存在
        result = db.query(XiaohongshuNoteData).count()
        print(f"✅ 小红书笔记数据表查询成功，当前记录数: {result}")
        return True
    except Exception as e:
        print(f"❌ 数据表查询失败: {e}")
        return False
    finally:
        db.close()


def test_download_config():
    """测试下载配置"""
    print("\n=== 测试下载配置 ===")
    
    try:
        service = XiaohongshuService()
        config = service._get_download_config('note_data')
        
        if config:
            print(f"✅ 配置获取成功:")
            print(f"  名称: {config.get('name')}")
            print(f"  数据起始行: {config.get('data_start_row')}")
            print(f"  字段数量: {len(config.get('fields', []))}")
            
            # 显示字段配置
            fields = config.get('fields', [])
            print("  字段配置:")
            for field_name, field_type in fields:
                type_name = {1: "文本", 2: "数字"}.get(field_type, f"类型{field_type}")
                print(f"    - {field_name}: {type_name}")
            
            return True
        else:
            print("❌ 配置获取失败")
            return False
            
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False


def create_test_excel_data():
    """创建测试用的Excel数据"""
    print("\n=== 创建测试Excel数据 ===")
    
    try:
        # 创建测试数据
        test_data = {
            '笔记标题': ['测试笔记1', '测试笔记2', '测试笔记3'],
            '笔记ID': ['note_001', 'note_002', 'note_003'],
            '发布时间': ['2025-01-01 10:00:00', '2025-01-02 15:30:00', '2025-01-03 20:45:00'],
            '笔记类型': ['图文', '视频', '图文'],
            '浏览量': [1000, 2500, 800],
            '点赞数': [100, 250, 80],
            '评论数': [20, 45, 15],
            '分享数': [30, 60, 25],
            '收藏数': [50, 120, 40],
            '关注数': [10, 25, 8]
        }
        
        # 创建DataFrame
        df = pd.DataFrame(test_data)
        
        # 转换为Excel字节数据
        excel_buffer = io.BytesIO()
        df.to_excel(excel_buffer, index=False, engine='openpyxl')
        excel_content = excel_buffer.getvalue()
        
        print(f"✅ 测试Excel数据创建成功，大小: {len(excel_content)} bytes")
        print(f"  数据行数: {len(df)}")
        print(f"  字段数: {len(df.columns)}")
        
        return excel_content
        
    except Exception as e:
        print(f"❌ 创建测试Excel数据失败: {e}")
        return None


def test_data_import():
    """测试数据导入功能"""
    print("\n=== 测试数据导入功能 ===")
    
    try:
        # 创建测试Excel数据
        excel_content = create_test_excel_data()
        if not excel_content:
            return False
        
        # 创建或获取测试账号
        db = SessionLocal()
        try:
            # 查找或创建测试账号
            test_account = db.query(PlatformAccount).filter(
                PlatformAccount.platform == 'xiaohongshu'
            ).first()
            
            if not test_account:
                print("创建测试账号...")
                from app.models import User
                # 查找或创建测试用户
                test_user = db.query(User).first()
                if not test_user:
                    print("❌ 需要先创建用户才能测试")
                    return False
                
                test_account = PlatformAccount(
                    name="测试小红书账号",
                    platform="xiaohongshu",
                    user_id=test_user.id,
                    login_status=True
                )
                db.add(test_account)
                db.commit()
                print(f"✅ 测试账号创建成功，ID: {test_account.id}")
            else:
                print(f"✅ 使用现有测试账号，ID: {test_account.id}")
            
            # 测试数据导入
            print("开始导入数据...")
            result = DataDetailsService.import_excel_data(
                db=db,
                account_id=test_account.id,
                data_type='note_data',
                excel_content=excel_content
            )
            
            if result["success"]:
                print(f"✅ 数据导入成功:")
                print(f"  新增记录: {result['imported_count']}")
                print(f"  更新记录: {result['updated_count']}")
                print(f"  总处理数: {result['total_processed']}")
                
                # 验证数据是否正确插入
                count = db.query(XiaohongshuNoteData).filter(
                    XiaohongshuNoteData.account_id == test_account.id
                ).count()
                print(f"  数据库中该账号的记录数: {count}")
                
                # 显示部分导入的数据
                sample_records = db.query(XiaohongshuNoteData).filter(
                    XiaohongshuNoteData.account_id == test_account.id
                ).limit(2).all()
                
                print("  样本数据:")
                for record in sample_records:
                    print(f"    笔记ID: {record.note_id}, 标题: {record.note_title}")
                    print(f"    浏览量: {record.view_count}, 点赞数: {record.like_count}")
                
                return True
            else:
                print(f"❌ 数据导入失败: {result['error']}")
                return False
                
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ 数据导入测试失败: {e}")
        return False


def test_data_uniqueness():
    """测试数据唯一性约束"""
    print("\n=== 测试数据唯一性约束 ===")
    
    try:
        # 重复导入相同数据，应该更新而不是插入
        excel_content = create_test_excel_data()
        if not excel_content:
            return False
        
        db = SessionLocal()
        try:
            test_account = db.query(PlatformAccount).filter(
                PlatformAccount.platform == 'xiaohongshu'
            ).first()
            
            if not test_account:
                print("❌ 找不到测试账号")
                return False
            
            # 记录导入前的数据量
            count_before = db.query(XiaohongshuNoteData).filter(
                XiaohongshuNoteData.account_id == test_account.id
            ).count()
            
            # 再次导入相同数据
            result = DataDetailsService.import_excel_data(
                db=db,
                account_id=test_account.id,
                data_type='note_data',
                excel_content=excel_content
            )
            
            if result["success"]:
                count_after = db.query(XiaohongshuNoteData).filter(
                    XiaohongshuNoteData.account_id == test_account.id
                ).count()
                
                print(f"✅ 重复导入测试:")
                print(f"  导入前记录数: {count_before}")
                print(f"  导入后记录数: {count_after}")
                print(f"  新增记录: {result['imported_count']}")
                print(f"  更新记录: {result['updated_count']}")
                
                if count_before == count_after and result['updated_count'] > 0:
                    print("✅ 唯一性约束工作正常，重复数据被更新而非插入")
                    return True
                else:
                    print("❌ 唯一性约束可能有问题")
                    return False
            else:
                print(f"❌ 重复导入失败: {result['error']}")
                return False
                
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ 唯一性测试失败: {e}")
        return False


def test_model_mapping():
    """测试模型映射"""
    print("\n=== 测试模型映射 ===")
    
    try:
        # 检查模型映射
        model_mapping = DataDetailsService.MODEL_MAPPING
        if "note_data" in model_mapping:
            model_class = model_mapping["note_data"]
            print(f"✅ 模型映射正确: note_data -> {model_class.__name__}")
            
            # 验证模型类
            if model_class == XiaohongshuNoteData:
                print("✅ 模型类匹配正确")
            else:
                print(f"❌ 模型类不匹配，期望: XiaohongshuNoteData, 实际: {model_class}")
                return False
        else:
            print("❌ 模型映射缺少note_data")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 模型映射测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试小红书数据导入功能")
    
    tests = [
        ("数据模型创建", test_model_creation),
        ("下载配置", test_download_config),
        ("模型映射", test_model_mapping),
        ("数据导入功能", test_data_import),
        ("数据唯一性约束", test_data_uniqueness)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        try:
            if test_func():
                print(f"✅ {test_name} - 通过")
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"测试总结: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有测试通过！小红书数据导入功能正常工作。")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return False


if __name__ == "__main__":
    main()
