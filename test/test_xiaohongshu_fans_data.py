#!/usr/bin/env python3
"""
测试小红书粉丝数据功能
"""

import sys
import os
from datetime import datetime, date
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import SessionLocal
from app.models import XiaohongshuFansData, PlatformAccount
from app.services.xiaohongshu_service import XiaohongshuService


def test_model_creation():
    """测试数据模型创建"""
    print("=== 测试数据模型创建 ===")
    
    db = SessionLocal()
    try:
        # 测试查询表是否存在
        result = db.query(XiaohongshuFansData).count()
        print(f"✅ 小红书粉丝数据表查询成功，当前记录数: {result}")
        return True
    except Exception as e:
        print(f"❌ 数据表查询失败: {e}")
        return False
    finally:
        db.close()


def test_data_import_logic():
    """测试数据导入逻辑"""
    print("\n=== 测试数据导入逻辑 ===")
    
    try:
        # 模拟API返回的粉丝数据结构
        mock_fans_data = {
            "fans_list": [
                {"date": *************, "count": 10000},  # 2024-01-01 总关注数
                {"date": *************, "count": 10150},  # 2024-01-02
                {"date": *************, "count": 10300}   # 2024-01-03
            ],
            "leave_fans_list": [  # 新增关注数
                {"date": *************, "count": 200},
                {"date": *************, "count": 180},
                {"date": *************, "count": 220}
            ],
            "rise_fans_list": [  # 取关数
                {"date": *************, "count": 50},
                {"date": *************, "count": 30},
                {"date": *************, "count": 70}
            ]
        }
        
        # 测试时间戳转换
        timestamp_ms = *************  # 2024-01-01 00:00:00 UTC
        timestamp_s = timestamp_ms / 1000
        converted_date = datetime.fromtimestamp(timestamp_s).date()
        expected_date = date(2024, 1, 1)
        
        if converted_date == expected_date:
            print("✅ 时间戳转换正确")
        else:
            print(f"❌ 时间戳转换错误: {converted_date} != {expected_date}")
            return False
        
        # 测试数据结构解析
        fans_mappings = {
            'fans_list': 'total_fans_count',      # 总关注数
            'leave_fans_list': 'new_fans_count',  # 新增关注数
            'rise_fans_list': 'unfans_count'      # 取关数
        }
        
        # 收集所有日期的数据
        date_data = {}
        
        for api_field, db_field in fans_mappings.items():
            fans_list = mock_fans_data.get(api_field, [])
            for item in fans_list:
                if 'date' in item and 'count' in item:
                    timestamp_ms = item['date']
                    timestamp_s = timestamp_ms / 1000
                    item_date = datetime.fromtimestamp(timestamp_s).date()
                    
                    if item_date not in date_data:
                        date_data[item_date] = {}
                    
                    date_data[item_date][db_field] = item['count']
        
        # 验证解析结果
        expected_dates = [date(2024, 1, 1), date(2024, 1, 2), date(2024, 1, 3)]
        if set(date_data.keys()) == set(expected_dates):
            print("✅ 日期解析正确")
        else:
            print(f"❌ 日期解析错误: {list(date_data.keys())} != {expected_dates}")
            return False
        
        # 验证数据字段
        first_date_data = date_data[date(2024, 1, 1)]
        expected_fields = set(fans_mappings.values())
        if set(first_date_data.keys()) == expected_fields:
            print("✅ 数据字段解析正确")
        else:
            print(f"❌ 数据字段解析错误: {set(first_date_data.keys())} != {expected_fields}")
            return False
        
        # 验证数据值
        if (first_date_data['total_fans_count'] == 10000 and 
            first_date_data['new_fans_count'] == 200 and 
            first_date_data['unfans_count'] == 50):
            print("✅ 数据值解析正确")
        else:
            print(f"❌ 数据值解析错误")
            return False
        
        print("✅ 数据导入逻辑测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 数据导入逻辑测试失败: {e}")
        return False


def test_database_operations():
    """测试数据库操作"""
    print("\n=== 测试数据库操作 ===")
    
    db = SessionLocal()
    try:
        # 查找或创建测试账号
        test_account = db.query(PlatformAccount).filter(
            PlatformAccount.platform == 'xiaohongshu'
        ).first()
        
        if not test_account:
            print("❌ 需要先创建小红书测试账号")
            return False
        
        print(f"✅ 使用测试账号，ID: {test_account.id}")
        
        # 创建测试数据
        test_date = date(2024, 1, 1)
        test_data = {
            'account_id': test_account.id,
            'date': test_date,
            'total_fans_count': 10000,
            'new_fans_count': 200,
            'unfans_count': 50
        }
        
        # 删除可能存在的测试数据
        db.query(XiaohongshuFansData).filter(
            XiaohongshuFansData.account_id == test_account.id,
            XiaohongshuFansData.date == test_date
        ).delete()
        db.commit()
        
        # 插入新数据
        record = XiaohongshuFansData(**test_data)
        db.add(record)
        db.commit()
        
        # 验证插入
        inserted_record = db.query(XiaohongshuFansData).filter(
            XiaohongshuFansData.account_id == test_account.id,
            XiaohongshuFansData.date == test_date
        ).first()
        
        if inserted_record:
            print("✅ 数据插入成功")
            print(f"  总关注数: {inserted_record.total_fans_count}")
            print(f"  新增关注数: {inserted_record.new_fans_count}")
            print(f"  取关数: {inserted_record.unfans_count}")
        else:
            print("❌ 数据插入失败")
            return False
        
        # 测试更新
        inserted_record.total_fans_count = 10500
        inserted_record.updated_at = datetime.utcnow()
        db.commit()
        
        # 验证更新
        updated_record = db.query(XiaohongshuFansData).filter(
            XiaohongshuFansData.account_id == test_account.id,
            XiaohongshuFansData.date == test_date
        ).first()
        
        if updated_record and updated_record.total_fans_count == 10500:
            print("✅ 数据更新成功")
        else:
            print("❌ 数据更新失败")
            return False
        
        # 清理测试数据
        db.delete(updated_record)
        db.commit()
        print("✅ 测试数据清理完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库操作测试失败: {e}")
        return False
    finally:
        db.close()


def test_api_configuration():
    """测试API配置"""
    print("\n=== 测试API配置 ===")
    
    try:
        import requests
        base_url = 'http://localhost:8000'
        
        # 测试小红书配置API
        response = requests.get(f'{base_url}/api/data-details/xiaohongshu/config', timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                data_types = data.get('data_types', {})
                
                # 检查是否包含粉丝数据配置
                if 'fans_data' in data_types:
                    fans_config = data_types['fans_data']
                    print("✅ 粉丝数据配置存在")
                    print(f"  名称: {fans_config.get('name')}")
                    print(f"  描述: {fans_config.get('description')}")
                    print(f"  字段数量: {len(fans_config.get('columns', []))}")
                    
                    # 检查关键字段
                    columns = fans_config.get('columns', [])
                    expected_fields = [
                        'date', 'total_fans_count', 'new_fans_count', 'unfans_count'
                    ]
                    
                    actual_fields = [col['key'] for col in columns if col['key'] not in ['account_name', 'updated_at']]
                    missing_fields = set(expected_fields) - set(actual_fields)
                    
                    if not missing_fields:
                        print("✅ 所有必需字段都存在")
                    else:
                        print(f"❌ 缺少字段: {missing_fields}")
                        return False
                else:
                    print("❌ 缺少粉丝数据配置")
                    return False
                
                # 检查是否包含其他配置
                expected_configs = ['note_data', 'account_overview', 'fans_data']
                for config_name in expected_configs:
                    if config_name in data_types:
                        print(f"✅ {config_name} 配置存在")
                    else:
                        print(f"❌ 缺少 {config_name} 配置")
                        return False
                
            else:
                print(f"❌ API返回失败: {data}")
                return False
        else:
            print(f"❌ API请求失败: {response.status_code}")
            return False
        
        print("✅ API配置测试通过")
        return True
        
    except Exception as e:
        print(f"❌ API配置测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试小红书粉丝数据功能")
    
    tests = [
        ("数据模型创建", test_model_creation),
        ("数据导入逻辑", test_data_import_logic),
        ("数据库操作", test_database_operations),
        ("API配置", test_api_configuration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        try:
            if test_func():
                print(f"✅ {test_name} - 通过")
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"小红书粉丝数据功能测试总结: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有测试通过！")
        print("\n📋 功能总结:")
        print("  ✅ 数据模型创建成功")
        print("  ✅ 数据导入逻辑正确")
        print("  ✅ 数据库操作正常")
        print("  ✅ API配置完整")
        print("\n🚀 小红书粉丝数据功能已就绪！")
        print("\n📊 支持的粉丝数据类型:")
        print("  - 总关注数趋势")
        print("  - 新增关注数趋势")
        print("  - 取关数趋势")
        print("\n🔗 数据来源:")
        print("  - 页面: https://creator.xiaohongshu.com/creator/fans")
        print("  - API: https://creator.xiaohongshu.com/api/galaxy/creator/data/fans/overall_new")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return False


if __name__ == "__main__":
    main()
