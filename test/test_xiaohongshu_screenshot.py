#!/usr/bin/env python3
"""
测试小红书截图功能
"""

import sys
import os
import asyncio
import inspect

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.xiaohongshu_service import XiaohongshuService


def test_screenshot_method_exists():
    """测试截图方法是否存在"""
    print("=== 测试截图方法是否存在 ===")
    
    try:
        # 检查方法是否存在
        if hasattr(XiaohongshuService, 'screenshot_fans_overview'):
            print("✅ screenshot_fans_overview方法存在")
            
            # 检查方法是否是异步的
            method = getattr(XiaohongshuService, 'screenshot_fans_overview')
            if asyncio.iscoroutinefunction(method):
                print("✅ screenshot_fans_overview方法是异步方法")
            else:
                print("❌ screenshot_fans_overview方法不是异步方法")
                return False
            
            # 检查方法签名
            sig = inspect.signature(method)
            params = list(sig.parameters.keys())
            
            if 'self' in params and 'save_path' in params:
                print("✅ 方法签名正确，包含self和save_path参数")
            else:
                print(f"❌ 方法签名错误，参数: {params}")
                return False
            
            return True
        else:
            print("❌ screenshot_fans_overview方法不存在")
            return False
        
    except Exception as e:
        print(f"❌ 测试截图方法失败: {e}")
        return False


def test_screenshot_implementation():
    """测试截图方法实现"""
    print("\n=== 测试截图方法实现 ===")
    
    try:
        service_file = "app/services/xiaohongshu_service.py"
        
        if os.path.exists(service_file):
            print(f"✅ 小红书服务文件存在")
            
            with open(service_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # 检查截图方法实现
                implementation_checks = [
                    ("方法定义", "async def screenshot_fans_overview(self, save_path: str)"),
                    ("返回类型注解", "-> Optional[str]"),
                    ("账号ID检查", "if not self.account_id:"),
                    ("启动浏览器", "await self._start_browser()"),
                    ("检查登录状态", "await self.check_existing_login()"),
                    ("访问粉丝页面", "creator.xiaohongshu.com/creator/fans"),
                    ("等待页面加载", "await asyncio.sleep(3)"),
                    ("等待选择器", "wait_for_selector"),
                    ("目标选择器", "#app > div > div.content > div.fans-data-container"),
                    ("创建目录", "os.makedirs"),
                    ("元素截图", "await element.screenshot"),
                    ("异常处理", "except Exception as e:")
                ]
                
                all_passed = True
                for check_name, check_content in implementation_checks:
                    if check_content in content:
                        print(f"✅ {check_name}: 存在")
                    else:
                        print(f"❌ {check_name}: 缺失")
                        all_passed = False
                
                return all_passed
        else:
            print(f"❌ 小红书服务文件不存在")
            return False
        
    except Exception as e:
        print(f"❌ 测试截图方法实现失败: {e}")
        return False


def test_data_download_integration():
    """测试数据下载服务集成"""
    print("\n=== 测试数据下载服务集成 ===")
    
    try:
        download_service_file = "app/services/data_download_service.py"
        
        if os.path.exists(download_service_file):
            print(f"✅ 数据下载服务文件存在")
            
            with open(download_service_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # 检查数据下载服务中的截图集成
                integration_checks = [
                    ("截图开始日志", "开始截图小红书账号"),
                    ("截图文件名生成", "xiaohongshu_{account.name}_fans_overview"),
                    ("截图路径生成", "screenshot_path = os.path.join"),
                    ("调用截图方法", "await xiaohongshu_service.screenshot_fans_overview"),
                    ("成功处理", "downloaded_files.append"),
                    ("失败处理", "failed_files.append"),
                    ("截图数据类型", "fans_overview_screenshot"),
                    ("异常捕获", "except Exception as e:")
                ]
                
                all_passed = True
                for check_name, check_content in integration_checks:
                    if check_content in content:
                        print(f"✅ {check_name}: 存在")
                    else:
                        print(f"❌ {check_name}: 缺失")
                        all_passed = False
                
                return all_passed
        else:
            print(f"❌ 数据下载服务文件不存在")
            return False
        
    except Exception as e:
        print(f"❌ 测试数据下载服务集成失败: {e}")
        return False


def test_file_naming_convention():
    """测试文件命名规范"""
    print("\n=== 测试文件命名规范 ===")
    
    try:
        download_service_file = "app/services/data_download_service.py"
        
        with open(download_service_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
            # 检查文件命名规范
            naming_checks = [
                ("包含平台名", "xiaohongshu_"),
                ("包含账号名", "{account.name}"),
                ("包含数据类型", "fans_overview"),
                ("包含日期范围", "{start_date}_{end_date}"),
                ("PNG格式", ".png"),
                ("完整命名模式", "xiaohongshu_{account.name}_fans_overview_{start_date}_{end_date}.png")
            ]
            
            all_passed = True
            for check_name, check_content in naming_checks:
                if check_content in content:
                    print(f"✅ {check_name}: 正确")
                else:
                    print(f"❌ {check_name}: 错误")
                    all_passed = False
            
            return all_passed
        
    except Exception as e:
        print(f"❌ 测试文件命名规范失败: {e}")
        return False


def test_error_handling():
    """测试错误处理"""
    print("\n=== 测试错误处理 ===")
    
    try:
        service_file = "app/services/xiaohongshu_service.py"
        download_service_file = "app/services/data_download_service.py"
        
        # 检查小红书服务的错误处理
        with open(service_file, 'r', encoding='utf-8') as f:
            service_content = f.read()
        
        # 检查数据下载服务的错误处理
        with open(download_service_file, 'r', encoding='utf-8') as f:
            download_content = f.read()
        
        # 错误处理检查
        error_handling_checks = [
            ("账号ID检查", "if not self.account_id:" in service_content),
            ("登录状态检查", "if not await self.check_existing_login():" in service_content),
            ("异常捕获", "except Exception as e:" in service_content),
            ("错误日志", "print(f\"" in service_content),
            ("返回None", "return None" in service_content),
            ("下载服务异常处理", "except Exception as e:" in download_content),
            ("失败文件记录", "failed_files.append" in download_content),
            ("错误日志记录", "logger.error" in download_content)
        ]
        
        all_passed = True
        for check_name, check_result in error_handling_checks:
            if check_result:
                print(f"✅ {check_name}: 正确")
            else:
                print(f"❌ {check_name}: 缺失")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 测试错误处理失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 测试小红书截图功能")
    
    tests = [
        ("截图方法存在性", test_screenshot_method_exists),
        ("截图方法实现", test_screenshot_implementation),
        ("数据下载服务集成", test_data_download_integration),
        ("文件命名规范", test_file_naming_convention),
        ("错误处理", test_error_handling)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            if result:
                print(f"✅ {test_name} - 通过")
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"小红书截图功能测试总结: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 小红书截图功能实现完成！")
        print("\n📋 功能总结:")
        print("  ✅ 添加了screenshot_fans_overview方法")
        print("  ✅ 集成到数据下载服务")
        print("  ✅ 完善的错误处理")
        print("  ✅ 规范的文件命名")
        print("  ✅ 完整的日志记录")
        print("\n🎯 截图功能:")
        print("  📸 访问粉丝数据页面")
        print("  🎯 定位指定DOM元素")
        print("  📷 截图保存到下载目录")
        print("  📦 与其他文件一起打包")
        print("\n🔧 技术实现:")
        print("  - 使用Playwright进行页面截图")
        print("  - 目标选择器: #app > div > div.content > div.fans-data-container")
        print("  - 备用方案: 整页截图")
        print("  - 自动创建保存目录")
        print("  - 完整的异常处理")
        print("\n📁 文件命名:")
        print("  格式: xiaohongshu_{账号名}_fans_overview_{开始日期}_{结束日期}.png")
        print("  示例: xiaohongshu_赵永明_fans_overview_2024-01-01_2024-01-31.png")
        print("\n💡 使用流程:")
        print("  1. 用户选择小红书账号进行数据下载")
        print("  2. 系统下载笔记数据Excel文件")
        print("  3. 自动截图粉丝数据概览页面")
        print("  4. 将截图文件添加到下载包中")
        print("  5. 用户获得完整的数据包（Excel + 截图）")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return False


if __name__ == "__main__":
    main()
