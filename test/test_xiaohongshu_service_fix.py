#!/usr/bin/env python3
"""
测试小红书服务修复
"""

import sys
import os
import asyncio
import inspect

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.xiaohongshu_service import XiaohongshuService


def test_start_browser_method_exists():
    """测试_start_browser方法是否存在"""
    print("=== 测试_start_browser方法是否存在 ===")
    
    try:
        # 检查方法是否存在
        if hasattr(XiaohongshuService, '_start_browser'):
            print("✅ _start_browser方法存在")
            
            # 检查方法是否是异步的
            method = getattr(XiaohongshuService, '_start_browser')
            if asyncio.iscoroutinefunction(method):
                print("✅ _start_browser方法是异步方法")
            else:
                print("❌ _start_browser方法不是异步方法")
                return False
            
            return True
        else:
            print("❌ _start_browser方法不存在")
            return False
        
    except Exception as e:
        print(f"❌ 测试_start_browser方法失败: {e}")
        return False


def test_method_signatures():
    """测试方法签名"""
    print("\n=== 测试方法签名 ===")
    
    try:
        # 检查关键方法的签名
        methods_to_check = [
            '_init_browser',
            '_create_persistent_context', 
            '_start_browser',
            'get_account_overview_data',
            'get_fans_data'
        ]
        
        all_passed = True
        for method_name in methods_to_check:
            if hasattr(XiaohongshuService, method_name):
                method = getattr(XiaohongshuService, method_name)
                if asyncio.iscoroutinefunction(method):
                    print(f"✅ {method_name}: 异步方法")
                else:
                    print(f"❌ {method_name}: 不是异步方法")
                    all_passed = False
            else:
                print(f"❌ {method_name}: 方法不存在")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 测试方法签名失败: {e}")
        return False


def test_service_instantiation():
    """测试服务实例化"""
    print("\n=== 测试服务实例化 ===")
    
    try:
        # 创建服务实例
        service = XiaohongshuService(account_id=1, headless=True)
        
        # 检查初始化属性
        if hasattr(service, 'browser'):
            print("✅ browser属性存在")
        else:
            print("❌ browser属性不存在")
            return False
        
        if hasattr(service, 'context'):
            print("✅ context属性存在")
        else:
            print("❌ context属性不存在")
            return False
        
        if hasattr(service, 'page'):
            print("✅ page属性存在")
        else:
            print("❌ page属性不存在")
            return False
        
        if hasattr(service, 'account_id'):
            print(f"✅ account_id属性存在: {service.account_id}")
        else:
            print("❌ account_id属性不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试服务实例化失败: {e}")
        return False


def test_method_calls_in_code():
    """测试代码中的方法调用"""
    print("\n=== 测试代码中的方法调用 ===")
    
    try:
        service_file = "app/services/xiaohongshu_service.py"
        
        if os.path.exists(service_file):
            print(f"✅ 小红书服务文件存在")
            
            with open(service_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # 检查_start_browser方法调用
                start_browser_calls = content.count('await self._start_browser()')
                if start_browser_calls > 0:
                    print(f"✅ 找到 {start_browser_calls} 个_start_browser方法调用")
                else:
                    print("❌ 没有找到_start_browser方法调用")
                    return False
                
                # 检查方法定义
                if 'async def _start_browser(self):' in content:
                    print("✅ _start_browser方法定义存在")
                else:
                    print("❌ _start_browser方法定义不存在")
                    return False
                
                # 检查方法实现
                implementation_checks = [
                    'await self._init_browser()',
                    'self.context = await self._create_persistent_context()',
                    'self.page = await self.context.new_page()',
                    'await self.load_login_state()'
                ]
                
                for check in implementation_checks:
                    if check in content:
                        print(f"✅ 实现检查通过: {check}")
                    else:
                        print(f"❌ 实现检查失败: {check}")
                        return False
                
                return True
        else:
            print(f"❌ 小红书服务文件不存在")
            return False
        
    except Exception as e:
        print(f"❌ 测试代码中的方法调用失败: {e}")
        return False


def test_error_scenarios():
    """测试错误场景"""
    print("\n=== 测试错误场景 ===")
    
    try:
        # 检查错误处理
        service_file = "app/services/xiaohongshu_service.py"
        
        with open(service_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
            # 检查错误处理
            error_handling_checks = [
                ("异常捕获", "except Exception as e:"),
                ("错误日志", "print(f\""),
                ("异常抛出", "raise")
            ]
            
            all_passed = True
            for check_name, check_content in error_handling_checks:
                if check_content in content:
                    print(f"✅ {check_name}: 存在")
                else:
                    print(f"❌ {check_name}: 缺失")
                    all_passed = False
            
            return all_passed
        
    except Exception as e:
        print(f"❌ 测试错误场景失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 测试小红书服务修复")
    
    tests = [
        ("_start_browser方法存在性", test_start_browser_method_exists),
        ("方法签名", test_method_signatures),
        ("服务实例化", test_service_instantiation),
        ("代码中的方法调用", test_method_calls_in_code),
        ("错误场景", test_error_scenarios)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            if result:
                print(f"✅ {test_name} - 通过")
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"小红书服务修复测试总结: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 小红书服务修复完成！")
        print("\n📋 修复总结:")
        print("  ✅ 添加了缺失的_start_browser方法")
        print("  ✅ 方法签名正确（异步方法）")
        print("  ✅ 服务实例化正常")
        print("  ✅ 方法调用匹配")
        print("  ✅ 错误处理完善")
        print("\n🔧 修复内容:")
        print("  - 添加了_start_browser异步方法")
        print("  - 方法内部调用_init_browser初始化浏览器")
        print("  - 创建持久化上下文和页面")
        print("  - 自动加载登录状态")
        print("  - 完善的异常处理")
        print("\n💡 现在可以:")
        print("  - 正常获取账号概览数据")
        print("  - 正常获取粉丝数据")
        print("  - 避免'_start_browser'方法不存在的错误")
        print("  - 确保浏览器正确启动和初始化")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return False


if __name__ == "__main__":
    main()
