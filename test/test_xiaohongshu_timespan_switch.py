#!/usr/bin/env python3
"""
测试小红书时间跨度切换功能
"""

import os

def test_timespan_switch_implementation():
    """测试时间跨度切换实现"""
    print("=== 测试时间跨度切换实现 ===")
    
    try:
        service_file = "app/services/xiaohongshu_service.py"
        
        if os.path.exists(service_file):
            print(f"✅ 小红书服务文件存在")
            
            with open(service_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # 检查时间跨度切换实现
                timespan_checks = [
                    ("切换开始日志", "正在切换时间跨度到近30天"),
                    ("点击7天按钮", "get_by_role(\"button\", name=\"近7天\")"),
                    ("点击按钮操作", "await time_button.click()"),
                    ("点击7天日志", "已点击时间跨度按钮"),
                    ("等待下拉菜单", "await asyncio.sleep(1)"),
                    ("选择30天选项", "get_by_text(\"近30天\").first"),
                    ("点击30天操作", "await thirty_days_option.click()"),
                    ("切换成功日志", "已切换到近30天"),
                    ("等待数据刷新", "await asyncio.sleep(3)"),
                    ("异常处理", "except Exception as e:"),
                    ("切换失败日志", "切换时间跨度失败"),
                    ("备用方案说明", "将使用默认的7天数据进行截图")
                ]
                
                all_passed = True
                for check_name, check_content in timespan_checks:
                    if check_content in content:
                        print(f"✅ {check_name}: 存在")
                    else:
                        print(f"❌ {check_name}: 缺失")
                        all_passed = False
                
                return all_passed
        else:
            print(f"❌ 小红书服务文件不存在")
            return False
        
    except Exception as e:
        print(f"❌ 测试时间跨度切换实现失败: {e}")
        return False


def test_playwright_api_usage():
    """测试Playwright API使用"""
    print("\n=== 测试Playwright API使用 ===")
    
    try:
        service_file = "app/services/xiaohongshu_service.py"
        
        with open(service_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
            # 检查Playwright API使用
            api_checks = [
                ("get_by_role方法", "self.page.get_by_role"),
                ("button角色选择", "\"button\""),
                ("name属性选择", "name=\"近7天\""),
                ("get_by_text方法", "self.page.get_by_text"),
                ("first选择器", ".first"),
                ("click操作", "await.*click()"),
                ("异步等待", "await asyncio.sleep")
            ]
            
            all_passed = True
            for check_name, check_content in api_checks:
                if check_content in content:
                    print(f"✅ {check_name}: 正确使用")
                else:
                    print(f"❌ {check_name}: 使用错误")
                    all_passed = False
            
            return all_passed
        
    except Exception as e:
        print(f"❌ 测试Playwright API使用失败: {e}")
        return False


def test_error_handling_logic():
    """测试错误处理逻辑"""
    print("\n=== 测试错误处理逻辑 ===")
    
    try:
        service_file = "app/services/xiaohongshu_service.py"
        
        with open(service_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
            # 检查错误处理逻辑
            error_checks = [
                ("try-except包装", "try:" in content and "except Exception as e:" in content),
                ("错误日志记录", "print(f\"切换时间跨度失败: {e}\")"),
                ("继续执行逻辑", "继续截图默认的7天数据"),
                ("不中断流程", "将使用默认的7天数据进行截图"),
                ("异常变量使用", "{e}")
            ]
            
            all_passed = True
            for check_name, check_condition in error_checks:
                if isinstance(check_condition, bool):
                    if check_condition:
                        print(f"✅ {check_name}: 正确")
                    else:
                        print(f"❌ {check_name}: 错误")
                        all_passed = False
                else:
                    if check_condition in content:
                        print(f"✅ {check_name}: 存在")
                    else:
                        print(f"❌ {check_name}: 缺失")
                        all_passed = False
            
            return all_passed
        
    except Exception as e:
        print(f"❌ 测试错误处理逻辑失败: {e}")
        return False


def test_timing_and_waits():
    """测试时间控制和等待"""
    print("\n=== 测试时间控制和等待 ===")
    
    try:
        service_file = "app/services/xiaohongshu_service.py"
        
        with open(service_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
            # 检查时间控制
            timing_checks = [
                ("页面加载等待", "await asyncio.sleep(3)"),
                ("下拉菜单等待", "await asyncio.sleep(1)"),
                ("数据刷新等待", "await asyncio.sleep(3)"),
                ("元素等待", "wait_for_selector"),
                ("异步操作", "await.*click()"),
                ("合理的等待时间", "sleep(1)" in content and "sleep(3)" in content)
            ]
            
            all_passed = True
            for check_name, check_condition in timing_checks:
                if isinstance(check_condition, bool):
                    if check_condition:
                        print(f"✅ {check_name}: 合理")
                    else:
                        print(f"❌ {check_name}: 不合理")
                        all_passed = False
                else:
                    if check_condition in content:
                        print(f"✅ {check_name}: 存在")
                    else:
                        print(f"❌ {check_name}: 缺失")
                        all_passed = False
            
            return all_passed
        
    except Exception as e:
        print(f"❌ 测试时间控制和等待失败: {e}")
        return False


def test_user_experience_flow():
    """测试用户体验流程"""
    print("\n=== 测试用户体验流程 ===")
    
    try:
        service_file = "app/services/xiaohongshu_service.py"
        
        with open(service_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
            # 检查用户体验流程
            ux_checks = [
                ("清晰的操作日志", "正在切换时间跨度到近30天"),
                ("步骤确认", "已点击时间跨度按钮"),
                ("成功反馈", "已切换到近30天"),
                ("失败处理", "切换时间跨度失败"),
                ("备用说明", "将使用默认的7天数据"),
                ("不中断用户流程", "继续截图" in content)
            ]
            
            all_passed = True
            for check_name, check_content in ux_checks:
                if check_content in content:
                    print(f"✅ {check_name}: 良好")
                else:
                    print(f"❌ {check_name}: 缺失")
                    all_passed = False
            
            return all_passed
        
    except Exception as e:
        print(f"❌ 测试用户体验流程失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 测试小红书时间跨度切换功能")
    
    tests = [
        ("时间跨度切换实现", test_timespan_switch_implementation),
        ("Playwright API使用", test_playwright_api_usage),
        ("错误处理逻辑", test_error_handling_logic),
        ("时间控制和等待", test_timing_and_waits),
        ("用户体验流程", test_user_experience_flow)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            if result:
                print(f"✅ {test_name} - 通过")
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"小红书时间跨度切换功能测试总结: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 小红书时间跨度切换功能实现完成！")
        print("\n📋 功能总结:")
        print("  ✅ 自动切换时间跨度从7天到30天")
        print("  ✅ 使用Playwright录制的操作序列")
        print("  ✅ 完善的错误处理和备用方案")
        print("  ✅ 合理的等待时间控制")
        print("  ✅ 清晰的用户反馈")
        print("\n🎯 操作流程:")
        print("  1️⃣  等待粉丝数据页面加载")
        print("  2️⃣  点击'近7天'时间跨度按钮")
        print("  3️⃣  等待下拉菜单出现")
        print("  4️⃣  点击'近30天'选项")
        print("  5️⃣  等待数据刷新完成")
        print("  6️⃣  执行截图操作")
        print("\n🔧 技术实现:")
        print("  - 使用get_by_role()定位按钮元素")
        print("  - 使用get_by_text().first定位文本选项")
        print("  - 异步点击操作确保响应")
        print("  - 合理的等待时间确保数据加载")
        print("\n🛡️ 错误处理:")
        print("  - 切换失败时不中断截图流程")
        print("  - 使用默认7天数据作为备用方案")
        print("  - 详细的错误日志记录")
        print("  - 用户友好的提示信息")
        print("\n💡 用户价值:")
        print("  - 截图包含更完整的30天数据")
        print("  - 提供更有价值的粉丝趋势信息")
        print("  - 自动化操作减少手动干预")
        print("  - 即使切换失败也能获得截图")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return False


if __name__ == "__main__":
    main()
