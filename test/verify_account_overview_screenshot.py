#!/usr/bin/env python3
"""
验证小红书账号总览截图功能
"""

import os

def main():
    """验证账号总览截图功能"""
    print("🚀 验证小红书账号总览截图功能")
    
    service_file = "app/services/xiaohongshu_service.py"
    download_service_file = "app/services/data_download_service.py"
    
    if not os.path.exists(service_file):
        print("❌ 小红书服务文件不存在")
        return False
    
    if not os.path.exists(download_service_file):
        print("❌ 数据下载服务文件不存在")
        return False
    
    # 检查小红书服务
    with open(service_file, 'r', encoding='utf-8') as f:
        service_content = f.read()
    
    # 检查数据下载服务
    with open(download_service_file, 'r', encoding='utf-8') as f:
        download_content = f.read()
    
    # 检查关键功能点
    checks = [
        # 小红书服务检查
        ("账号总览截图方法定义", "async def screenshot_account_overview", service_content),
        ("访问账号总览页面", "creator.xiaohongshu.com/statistics/account", service_content),
        ("切换到近30日", "get_by_text(\"近30日\").first", service_content),
        ("目标选择器", "#content-area > main > div:nth-child(3) > div > div > div > div.data > div > div.d-tabs-pane > div > div.datas", service_content),
        
        # 数据下载服务检查
        ("账号总览截图调用", "screenshot_account_overview", download_content),
        ("账号总览文件名", "account_overview", download_content),
        ("账号总览数据类型", "account_overview_screenshot", download_content),
        ("账号总览日志", "开始截图小红书账号", download_content)
    ]
    
    passed = 0
    for check_name, check_content, content in checks:
        if check_content in content:
            print(f"✅ {check_name}: 通过")
            passed += 1
        else:
            print(f"❌ {check_name}: 失败")
    
    print(f"\n📊 验证结果: {passed}/{len(checks)} 通过")
    
    if passed == len(checks):
        print("\n🎉 账号总览截图功能验证完成！")
        print("\n📋 功能特点:")
        print("  🎯 访问账号总览页面")
        print("  📅 自动切换到'近30日'数据")
        print("  📸 截图数据可视化区域")
        print("  💾 保存为PNG格式文件")
        print("\n🔧 操作序列:")
        print("  1. 访问 https://creator.xiaohongshu.com/statistics/account")
        print("  2. 等待页面加载完成")
        print("  3. 点击'近30日'切换时间范围")
        print("  4. 等待数据刷新")
        print("  5. 截图数据区域")
        print("  6. 保存到下载目录")
        print("\n📁 文件命名:")
        print("  格式: xiaohongshu_{账号名}_account_overview_{开始日期}_{结束日期}.png")
        print("  示例: xiaohongshu_赵永明_account_overview_2024-01-01_2024-01-31.png")
        print("\n📦 完整下载包:")
        print("  📊 笔记数据Excel文件")
        print("  📸 粉丝数据概览截图（30天）")
        print("  📈 账号总览截图（30日）")
        print("\n💡 用户价值:")
        print("  - 获得完整的小红书账号分析资料")
        print("  - 结构化数据 + 可视化图表")
        print("  - 便于制作数据报告和分析")
        print("  - 时间范围保持一致性")
        return True
    else:
        print("⚠️  部分功能验证失败")
        return False

if __name__ == "__main__":
    main()
