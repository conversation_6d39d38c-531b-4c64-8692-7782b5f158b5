#!/usr/bin/env python3
"""
验证界面布局调整
"""

import os

def main():
    """验证布局调整"""
    print("🚀 验证界面布局调整")
    
    page_file = "frontend/src/pages/DataUpdate.tsx"
    
    if not os.path.exists(page_file):
        print("❌ DataUpdate页面文件不存在")
        return False
    
    with open(page_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查布局顺序
    auto_update_pos = content.find("自动更新配置")
    manual_update_pos = content.find("手动更新")
    
    print(f"✅ 自动更新配置位置: {auto_update_pos}")
    print(f"✅ 手动更新位置: {manual_update_pos}")
    
    if auto_update_pos < manual_update_pos:
        print("✅ 布局顺序正确：自动更新在手动更新之前")
    else:
        print("❌ 布局顺序错误")
        return False
    
    # 检查关键组件
    components = [
        "自动更新配置",
        "手动更新", 
        "上次更新",
        "数据更新操作",
        "历史更新记录"
    ]
    
    for component in components:
        if component in content:
            print(f"✅ {component}: 存在")
        else:
            print(f"❌ {component}: 缺失")
            return False
    
    print("\n🎉 界面布局调整验证完成！")
    print("\n📋 新布局结构:")
    print("  1. 自动更新配置 (最上面)")
    print("  2. 手动更新 (包含所有手动更新功能)")
    print("     - 上次更新")
    print("     - 数据更新操作") 
    print("     - 当前任务进度")
    print("     - 历史更新记录")
    
    return True

if __name__ == "__main__":
    main()
