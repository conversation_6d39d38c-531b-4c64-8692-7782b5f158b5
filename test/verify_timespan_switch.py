#!/usr/bin/env python3
"""
验证小红书时间跨度切换功能
"""

import os

def main():
    """验证时间跨度切换功能"""
    print("🚀 验证小红书时间跨度切换功能")
    
    service_file = "app/services/xiaohongshu_service.py"
    
    if not os.path.exists(service_file):
        print("❌ 小红书服务文件不存在")
        return False
    
    with open(service_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键功能点
    checks = [
        ("时间跨度切换开始", "正在切换时间跨度到近30天"),
        ("点击7天按钮", "get_by_role(\"button\", name=\"近7天\")"),
        ("异步点击操作", "await time_button.click()"),
        ("等待下拉菜单", "await asyncio.sleep(1)"),
        ("选择30天选项", "get_by_text(\"近30天\").first"),
        ("点击30天选项", "await thirty_days_option.click()"),
        ("等待数据刷新", "await asyncio.sleep(3)"),
        ("异常处理", "except Exception as e:"),
        ("错误日志", "切换时间跨度失败"),
        ("备用方案", "将使用默认的7天数据")
    ]
    
    passed = 0
    for check_name, check_content in checks:
        if check_content in content:
            print(f"✅ {check_name}: 通过")
            passed += 1
        else:
            print(f"❌ {check_name}: 失败")
    
    print(f"\n📊 验证结果: {passed}/{len(checks)} 通过")
    
    if passed == len(checks):
        print("\n🎉 时间跨度切换功能验证完成！")
        print("\n📋 功能特点:")
        print("  🎯 自动从'近7天'切换到'近30天'")
        print("  🔄 使用Playwright录制的精确操作")
        print("  ⏱️  合理的等待时间确保操作成功")
        print("  🛡️  完善的错误处理和备用方案")
        print("\n🔧 操作序列:")
        print("  1. 等待粉丝数据页面加载完成")
        print("  2. 点击'近7天'时间跨度按钮")
        print("  3. 等待下拉菜单出现")
        print("  4. 点击'近30天'选项")
        print("  5. 等待数据刷新")
        print("  6. 继续执行截图")
        print("\n💡 用户价值:")
        print("  - 截图包含30天完整数据而非7天")
        print("  - 提供更有价值的粉丝趋势分析")
        print("  - 自动化操作无需手动干预")
        print("  - 即使切换失败也能正常截图")
        return True
    else:
        print("⚠️  部分功能验证失败")
        return False

if __name__ == "__main__":
    main()
