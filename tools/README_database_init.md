# 数据库初始化工具使用说明

## 📋 工具概述

本项目提供了两个数据库初始化脚本，用于快速设置和初始化社交媒体管理系统的数据库：

1. **`init_database.py`** - 完整的交互式初始化工具
2. **`quick_init.py`** - 快速初始化工具（使用现有配置）

## 🚀 快速开始

### 方式一：使用快速初始化工具（推荐）

如果您已经配置好了 `.env` 文件，可以直接使用快速初始化工具：

```bash
# 进入项目目录
cd /path/to/social-media-manager

# 运行快速初始化
python tools/quick_init.py
```

**功能特点：**
- ✅ 自动读取 `.env` 文件中的数据库配置
- ✅ 测试数据库连接
- ✅ 创建所有数据表结构
- ✅ 交互式创建管理员用户
- ✅ 显示初始化结果

### 方式二：使用完整初始化工具

如果您需要重新配置数据库连接，使用完整的初始化工具：

```bash
# 运行完整初始化
python tools/init_database.py
```

**功能特点：**
- ✅ 交互式选择数据库类型（MySQL/SQLite）
- ✅ 输入数据库连接信息
- ✅ 测试数据库连接
- ✅ 创建所有数据表结构
- ✅ 创建管理员用户
- ✅ 可选保存配置到 `.env` 文件

## 📊 支持的数据库

### MySQL
- **推荐用于生产环境**
- 支持远程数据库
- 自动配置连接池
- UTF8MB4字符集支持

### SQLite
- **适用于开发和测试**
- 本地文件数据库
- 无需额外配置
- 轻量级解决方案

## 🔧 使用示例

### 示例1：快速初始化（MySQL）

```bash
$ python tools/quick_init.py

============================================================
  🚀 快速数据库初始化工具
============================================================

📋 当前数据库配置:
   类型: MySQL
   主机: ***********
   端口: 33316
   数据库: social_media_manager
   用户: social_media_manager

🔍 测试数据库连接...
✅ MySQL连接成功，版本: 8.4.5

🏗️ 创建数据库表结构...
✅ 成功创建 20 个数据表

👤 创建管理员用户:
用户名: admin
邮箱: <EMAIL>
密码: ******
确认密码: ******
✅ 管理员用户创建成功!
   用户名: admin
   邮箱: <EMAIL>
   用户ID: 1

============================================================
🎉 数据库初始化完成!
============================================================

接下来可以:
1. 启动应用: python -m uvicorn main:app --reload
2. 或使用Docker: docker-compose up
3. 访问: http://localhost:8000
```

### 示例2：完整初始化（交互式配置）

```bash
$ python tools/init_database.py

======================================================================
  🚀 社交媒体管理系统 - 数据库初始化工具
======================================================================

📋 请输入数据库连接信息:

选择数据库类型:
1. MySQL
2. SQLite (本地开发)
请选择 (1/2): 1

📊 MySQL数据库配置:
主机地址 (默认: localhost): ***********
端口 (默认: 3306): 33316
数据库名 (默认: social_media_manager): social_media_manager
用户名: social_media_manager
密码: ********

🔍 测试MySQL数据库连接...
✅ MySQL连接成功，版本: 8.4.5

🏗️ 创建数据库表结构...
✅ 成功创建 20 个数据表:
   - auto_update_config
   - data_download_records
   - data_records
   - data_update_records
   - data_update_task_items
   - feishu_apps
   - feishu_tables
   - login_keeper_records
   - platform_accounts
   - users
   ... 还有 10 个表

👤 创建初始管理员用户:
用户名: admin
邮箱: <EMAIL>
密码: ********
确认密码: ********
✅ 用户创建成功!
   用户名: admin
   邮箱: <EMAIL>
   用户ID: 1

💾 是否保存数据库配置到.env文件?
保存配置? (y/N): y
✅ 配置已保存到 .env

======================================================================
🎉 数据库初始化完成!
======================================================================
数据库类型: MySQL
用户名: admin
邮箱: <EMAIL>

现在可以启动应用了: python -m uvicorn main:app --reload
```

## ⚠️ 注意事项

### 环境要求
- Python 3.8+
- 已安装项目依赖：`pip install -r requirements.txt`
- 对于MySQL：确保数据库服务器可访问且数据库已创建

### 权限要求
- MySQL用户需要有创建表的权限
- SQLite需要对目标目录有写权限

### 安全建议
- 生产环境请使用强密码
- 定期更新数据库用户密码
- 限制数据库用户权限

## 🛠️ 故障排除

### 常见问题

**1. 数据库连接失败**
```
❌ 数据库连接失败: (2003, "Can't connect to MySQL server...")
```
- 检查数据库服务器是否运行
- 验证主机地址和端口
- 确认用户名和密码正确
- 检查防火墙设置

**2. 表创建失败**
```
❌ 创建表结构失败: Access denied for user...
```
- 确认数据库用户有CREATE权限
- 检查数据库是否存在
- 验证字符集设置

**3. 用户已存在**
```
⚠️ 用户已存在 (用户名: admin, 邮箱: <EMAIL>)
```
- 这是正常提示，表示用户已经创建过
- 可以继续使用现有用户登录

**4. 环境变量未加载**
```
❌ 数据库连接失败: Connection refused
```
- 确认 `.env` 文件存在且格式正确
- 检查环境变量是否正确设置
- 重启终端或重新加载环境

## 📁 生成的文件

初始化完成后，会生成或更新以下文件：

- **`.env`** - 环境配置文件（如果选择保存）
- **数据库文件** - SQLite模式下会创建 `.db` 文件
- **日志文件** - 应用运行时的日志记录

## 🔄 重新初始化

如果需要重新初始化数据库：

1. **清空现有数据**：
   ```bash
   python tools/clean_mysql_database.py  # MySQL
   # 或删除SQLite文件
   ```

2. **重新运行初始化**：
   ```bash
   python tools/quick_init.py
   ```

## 📞 技术支持

如果遇到问题，请检查：
1. 数据库服务器状态
2. 网络连接
3. 用户权限
4. 环境变量配置
5. 依赖包安装

更多帮助请查看项目文档或联系技术支持。
