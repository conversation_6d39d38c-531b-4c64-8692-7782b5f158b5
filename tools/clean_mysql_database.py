#!/usr/bin/env python3
"""
清空MySQL数据库的所有表
"""

import os
import sys
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def clean_mysql_database():
    """清空MySQL数据库"""
    print("🧹 清空MySQL数据库...")
    
    # MySQL连接信息
    mysql_host = "***********"
    mysql_port = "33316"
    mysql_user = "social_media_manager"
    mysql_password = "PQhik9hWszKd"
    mysql_database = "social_media_manager"
    
    mysql_url = f"mysql+pymysql://{mysql_user}:{mysql_password}@{mysql_host}:{mysql_port}/{mysql_database}?charset=utf8mb4"
    
    try:
        engine = create_engine(
            mysql_url,
            pool_size=5,
            max_overflow=10,
            pool_pre_ping=True,
            pool_recycle=3600,
            echo=False
        )
        
        with engine.connect() as conn:
            # 禁用外键检查
            conn.execute(text("SET FOREIGN_KEY_CHECKS = 0"))
            
            # 获取所有表
            result = conn.execute(text("SHOW TABLES"))
            tables = [row[0] for row in result.fetchall()]
            
            if not tables:
                print("✅ 数据库中没有表需要清理")
                return True
            
            print(f"📋 找到 {len(tables)} 个表需要清理:")
            for table in tables:
                print(f"  - {table}")
            
            # 删除所有表
            for table in tables:
                try:
                    conn.execute(text(f"DROP TABLE {table}"))
                    print(f"✅ 删除表: {table}")
                except Exception as e:
                    print(f"❌ 删除表 {table} 失败: {e}")
            
            # 重新启用外键检查
            conn.execute(text("SET FOREIGN_KEY_CHECKS = 1"))
            
            # 提交事务
            conn.commit()
            
        print("🎉 MySQL数据库清理完成！")
        return True
        
    except SQLAlchemyError as e:
        print(f"❌ 清理数据库失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 清理过程出错: {e}")
        return False
    finally:
        try:
            engine.dispose()
        except:
            pass

def main():
    """主函数"""
    print("=" * 60)
    print("  MySQL数据库清理工具")
    print("=" * 60)
    
    # 确认操作
    confirm = input("⚠️  这将删除MySQL数据库中的所有表和数据！\n确认继续吗？(输入 'yes' 确认): ")
    
    if confirm.lower() != 'yes':
        print("❌ 操作已取消")
        return 1
    
    success = clean_mysql_database()
    
    if success:
        print("\n✅ 数据库清理成功！现在可以运行迁移脚本了。")
        return 0
    else:
        print("\n❌ 数据库清理失败！")
        return 1

if __name__ == "__main__":
    sys.exit(main())
