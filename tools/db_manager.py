#!/usr/bin/env python3
"""
数据库管理工具
功能：
1. 初始化数据库
2. 创建用户
3. 重置数据库
4. 备份和恢复
5. 数据库状态检查
"""

import os
import sys
import json
import getpass
import argparse
from datetime import datetime
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def print_banner():
    """打印横幅"""
    print("=" * 70)
    print("  🛠️ 社交媒体管理系统 - 数据库管理工具")
    print("=" * 70)
    print()

def get_database_info():
    """获取数据库信息"""
    try:
        from app.database import get_database_url, engine
        from sqlalchemy import text
        
        database_url = get_database_url()
        
        with engine.connect() as conn:
            if engine.url.drivername.startswith('mysql'):
                # MySQL信息
                result = conn.execute(text("SELECT VERSION()"))
                version = result.scalar()
                
                result = conn.execute(text("SELECT DATABASE()"))
                current_db = result.scalar()
                
                result = conn.execute(text("SHOW TABLES"))
                tables = [row[0] for row in result.fetchall()]
                
                return {
                    "type": "MySQL",
                    "version": version,
                    "database": current_db,
                    "host": os.getenv("DB_HOST", "localhost"),
                    "port": os.getenv("DB_PORT", "3306"),
                    "user": os.getenv("DB_USER", "root"),
                    "tables": tables,
                    "table_count": len(tables)
                }
            else:
                # SQLite信息
                result = conn.execute(text("SELECT sqlite_version()"))
                version = result.scalar()
                
                result = conn.execute(text("SELECT name FROM sqlite_master WHERE type='table'"))
                tables = [row[0] for row in result.fetchall()]
                
                return {
                    "type": "SQLite",
                    "version": version,
                    "database": database_url.replace('sqlite:///', ''),
                    "tables": tables,
                    "table_count": len(tables)
                }
                
    except Exception as e:
        return {"error": str(e)}

def show_status():
    """显示数据库状态"""
    print("📊 数据库状态检查")
    print("-" * 50)
    
    db_info = get_database_info()
    
    if "error" in db_info:
        print(f"❌ 数据库连接失败: {db_info['error']}")
        return False
    
    print(f"✅ 数据库类型: {db_info['type']}")
    print(f"✅ 版本: {db_info['version']}")
    
    if db_info['type'] == 'MySQL':
        print(f"✅ 主机: {db_info['host']}:{db_info['port']}")
        print(f"✅ 数据库: {db_info['database']}")
        print(f"✅ 用户: {db_info['user']}")
    else:
        print(f"✅ 文件: {db_info['database']}")
    
    print(f"✅ 表数量: {db_info['table_count']}")
    
    if db_info['tables']:
        print("✅ 数据表:")
        for table in sorted(db_info['tables'])[:10]:  # 只显示前10个
            print(f"   - {table}")
        if len(db_info['tables']) > 10:
            print(f"   ... 还有 {len(db_info['tables']) - 10} 个表")
    
    # 检查用户数量
    try:
        from app.database import SessionLocal
        from app.models import User
        
        db = SessionLocal()
        user_count = db.query(User).count()
        db.close()
        
        print(f"✅ 用户数量: {user_count}")
        
    except Exception as e:
        print(f"⚠️ 无法获取用户数量: {e}")
    
    print()
    return True

def init_database():
    """初始化数据库"""
    print("🏗️ 初始化数据库")
    print("-" * 50)
    
    try:
        from app.database import engine, Base
        
        # 创建所有表
        Base.metadata.create_all(bind=engine)
        print("✅ 数据库表结构创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return False

def create_user_interactive():
    """交互式创建用户"""
    print("👤 创建新用户")
    print("-" * 50)
    
    username = input("用户名: ").strip()
    if not username:
        print("❌ 用户名不能为空")
        return False
    
    email = input("邮箱: ").strip()
    if not email or '@' not in email:
        print("❌ 请输入有效的邮箱地址")
        return False
    
    password = getpass.getpass("密码: ")
    if len(password) < 6:
        print("❌ 密码长度至少6位")
        return False
    
    return create_user(username, email, password)

def create_user(username, email, password):
    """创建用户"""
    try:
        from app.database import SessionLocal
        from app.models import User
        from app.services.auth_service import AuthService
        
        db = SessionLocal()
        
        try:
            # 检查用户是否已存在
            existing_user = db.query(User).filter(
                (User.username == username) | (User.email == email)
            ).first()
            
            if existing_user:
                print(f"⚠️ 用户已存在:")
                print(f"   用户名: {existing_user.username}")
                print(f"   邮箱: {existing_user.email}")
                return False
            
            # 创建新用户
            hashed_password = AuthService.get_password_hash(password)
            new_user = User(
                username=username,
                email=email,
                hashed_password=hashed_password,
                created_at=datetime.utcnow()
            )
            
            db.add(new_user)
            db.commit()
            
            print(f"✅ 用户创建成功!")
            print(f"   用户名: {username}")
            print(f"   邮箱: {email}")
            print(f"   用户ID: {new_user.id}")
            
            return True
            
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ 创建用户失败: {e}")
        return False

def list_users():
    """列出所有用户"""
    print("👥 用户列表")
    print("-" * 50)
    
    try:
        from app.database import SessionLocal
        from app.models import User
        
        db = SessionLocal()
        users = db.query(User).all()
        db.close()
        
        if not users:
            print("📭 暂无用户")
            return
        
        print(f"共找到 {len(users)} 个用户:")
        print()
        
        for user in users:
            print(f"🔹 ID: {user.id}")
            print(f"   用户名: {user.username}")
            print(f"   邮箱: {user.email}")
            print(f"   创建时间: {user.created_at}")
            print()
            
    except Exception as e:
        print(f"❌ 获取用户列表失败: {e}")

def reset_database():
    """重置数据库"""
    print("🔄 重置数据库")
    print("-" * 50)
    print("⚠️ 警告：此操作将删除所有数据！")
    
    confirm = input("确认重置数据库? (输入 'RESET' 确认): ").strip()
    if confirm != 'RESET':
        print("❌ 操作已取消")
        return False
    
    try:
        from app.database import engine, Base
        
        # 删除所有表
        Base.metadata.drop_all(bind=engine)
        print("✅ 已删除所有表")
        
        # 重新创建表
        Base.metadata.create_all(bind=engine)
        print("✅ 已重新创建表结构")
        
        print("🎉 数据库重置完成")
        return True
        
    except Exception as e:
        print(f"❌ 重置失败: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='数据库管理工具')
    parser.add_argument('command', choices=[
        'status', 'init', 'create-user', 'list-users', 'reset'
    ], help='要执行的命令')
    parser.add_argument('--username', help='用户名（用于create-user）')
    parser.add_argument('--email', help='邮箱（用于create-user）')
    parser.add_argument('--password', help='密码（用于create-user）')
    
    args = parser.parse_args()
    
    print_banner()
    
    try:
        if args.command == 'status':
            show_status()
            
        elif args.command == 'init':
            if init_database():
                print("🎉 数据库初始化完成")
            
        elif args.command == 'create-user':
            if args.username and args.email and args.password:
                create_user(args.username, args.email, args.password)
            else:
                create_user_interactive()
                
        elif args.command == 'list-users':
            list_users()
            
        elif args.command == 'reset':
            reset_database()
            
    except KeyboardInterrupt:
        print("\n❌ 操作被用户中断")
        return 1
    except Exception as e:
        print(f"❌ 执行过程中发生错误: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
