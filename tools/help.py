#!/usr/bin/env python3
"""
数据库工具帮助文档
"""

def print_help():
    """打印帮助信息"""
    print("=" * 80)
    print("  🚀 社交媒体管理系统 - 数据库工具帮助")
    print("=" * 80)
    print()
    
    print("📋 可用工具:")
    print()
    
    print("1️⃣ 完整初始化工具 (init_database.py)")
    print("   功能: 交互式配置数据库连接并初始化")
    print("   用法: python tools/init_database.py")
    print("   特点:")
    print("   • 支持MySQL和SQLite")
    print("   • 交互式输入数据库连接信息")
    print("   • 自动测试连接")
    print("   • 创建表结构和初始用户")
    print("   • 可选保存配置到.env文件")
    print()
    
    print("2️⃣ 快速初始化工具 (quick_init.py)")
    print("   功能: 使用现有配置快速初始化数据库")
    print("   用法: python tools/quick_init.py")
    print("   特点:")
    print("   • 读取.env文件配置")
    print("   • 快速创建表结构")
    print("   • 交互式创建管理员用户")
    print("   • 适合已配置环境的快速部署")
    print()
    
    print("3️⃣ 数据库管理工具 (db_manager.py)")
    print("   功能: 全面的数据库管理命令行工具")
    print("   用法: python tools/db_manager.py <command>")
    print("   命令:")
    print("   • status      - 显示数据库状态")
    print("   • init        - 初始化数据库表结构")
    print("   • create-user - 创建新用户")
    print("   • list-users  - 列出所有用户")
    print("   • reset       - 重置数据库（危险操作）")
    print()
    print("   示例:")
    print("   python tools/db_manager.py status")
    print("   python tools/db_manager.py create-user --username admin --email <EMAIL> --password 123456")
    print()
    
    print("4️⃣ 数据迁移工具")
    print("   • migrate_sqlite_to_mysql.py - SQLite到MySQL迁移")
    print("   • test_mysql_connection.py   - 测试数据库连接")
    print("   • clean_mysql_database.py    - 清理MySQL数据库")
    print()
    
    print("🔧 使用场景:")
    print()
    
    print("🆕 首次部署:")
    print("   1. 使用 init_database.py 配置数据库连接")
    print("   2. 或者手动配置.env文件后使用 quick_init.py")
    print()
    
    print("🔄 日常管理:")
    print("   1. 使用 db_manager.py status 检查数据库状态")
    print("   2. 使用 db_manager.py create-user 创建新用户")
    print("   3. 使用 db_manager.py list-users 查看用户列表")
    print()
    
    print("🚨 故障排除:")
    print("   1. 检查数据库连接: python tools/test_mysql_connection.py")
    print("   2. 重新初始化: python tools/db_manager.py reset")
    print("   3. 查看状态: python tools/db_manager.py status")
    print()
    
    print("⚠️ 注意事项:")
    print("   • 确保已安装依赖: pip install -r requirements.txt")
    print("   • MySQL需要预先创建数据库")
    print("   • 生产环境请使用强密码")
    print("   • reset命令会删除所有数据，请谨慎使用")
    print()
    
    print("📞 获取更多帮助:")
    print("   • 查看详细文档: tools/README_database_init.md")
    print("   • 查看工具帮助: python tools/db_manager.py --help")
    print()

if __name__ == "__main__":
    print_help()
