#!/usr/bin/env python3
"""
交互式数据库初始化脚本
功能：
1. 输入数据库连接信息
2. 创建数据库表结构
3. 创建初始管理员用户
"""

import os
import sys
import getpass
from datetime import datetime
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError
from urllib.parse import quote_plus

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def print_banner():
    """打印欢迎横幅"""
    print("=" * 70)
    print("  🚀 社交媒体管理系统 - 数据库初始化工具")
    print("=" * 70)
    print()

def get_database_config():
    """交互式获取数据库配置"""
    print("📋 请输入数据库连接信息:")
    print()
    
    # 数据库类型选择
    print("选择数据库类型:")
    print("1. MySQL")
    print("2. SQLite (本地开发)")
    
    while True:
        db_type = input("请选择 (1/2): ").strip()
        if db_type in ['1', '2']:
            break
        print("❌ 请输入 1 或 2")
    
    if db_type == '1':
        # MySQL配置
        print("\n📊 MySQL数据库配置:")
        host = input("主机地址 (默认: localhost): ").strip() or "localhost"
        port = input("端口 (默认: 3306): ").strip() or "3306"
        database = input("数据库名 (默认: social_media_manager): ").strip() or "social_media_manager"
        username = input("用户名: ").strip()
        
        while not username:
            print("❌ 用户名不能为空")
            username = input("用户名: ").strip()
        
        password = getpass.getpass("密码: ")
        
        # 构建MySQL连接URL
        encoded_password = quote_plus(password) if password else ""
        if encoded_password:
            database_url = f"mysql+pymysql://{username}:{encoded_password}@{host}:{port}/{database}?charset=utf8mb4"
        else:
            database_url = f"mysql+pymysql://{username}@{host}:{port}/{database}?charset=utf8mb4"
        
        return database_url, "MySQL"
    
    else:
        # SQLite配置
        print("\n📁 SQLite数据库配置:")
        db_path = input("数据库文件路径 (默认: ./social_media.db): ").strip() or "./social_media.db"
        database_url = f"sqlite:///{db_path}"
        return database_url, "SQLite"

def test_database_connection(database_url, db_type):
    """测试数据库连接"""
    print(f"\n🔍 测试{db_type}数据库连接...")
    
    try:
        if db_type == "MySQL":
            engine = create_engine(
                database_url,
                pool_size=5,
                max_overflow=10,
                pool_pre_ping=True,
                pool_recycle=3600,
                echo=False
            )
        else:
            engine = create_engine(database_url)
        
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            test_value = result.scalar()
            
            if db_type == "MySQL":
                # 获取MySQL版本
                result = conn.execute(text("SELECT VERSION()"))
                version = result.scalar()
                print(f"✅ MySQL连接成功，版本: {version}")
            else:
                # 获取SQLite版本
                result = conn.execute(text("SELECT sqlite_version()"))
                version = result.scalar()
                print(f"✅ SQLite连接成功，版本: {version}")
        
        return engine
        
    except SQLAlchemyError as e:
        print(f"❌ 数据库连接失败: {e}")
        return None
    except Exception as e:
        print(f"❌ 连接测试出错: {e}")
        return None

def create_database_tables(engine):
    """创建数据库表结构"""
    print("\n🏗️ 创建数据库表结构...")
    
    try:
        from app.database import Base
        
        # 创建所有表
        Base.metadata.create_all(bind=engine)
        
        # 验证表创建
        with engine.connect() as conn:
            if engine.url.drivername.startswith('mysql'):
                result = conn.execute(text("SHOW TABLES"))
                tables = [row[0] for row in result.fetchall()]
            else:
                result = conn.execute(text("SELECT name FROM sqlite_master WHERE type='table'"))
                tables = [row[0] for row in result.fetchall()]
        
        print(f"✅ 成功创建 {len(tables)} 个数据表:")
        for table in sorted(tables):
            print(f"   - {table}")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建表结构失败: {e}")
        return False

def get_user_info():
    """获取初始用户信息"""
    print("\n👤 创建初始管理员用户:")
    print()
    
    username = input("用户名: ").strip()
    while not username:
        print("❌ 用户名不能为空")
        username = input("用户名: ").strip()
    
    email = input("邮箱: ").strip()
    while not email or '@' not in email:
        print("❌ 请输入有效的邮箱地址")
        email = input("邮箱: ").strip()
    
    while True:
        password = getpass.getpass("密码: ")
        if len(password) < 6:
            print("❌ 密码长度至少6位")
            continue
        
        confirm_password = getpass.getpass("确认密码: ")
        if password != confirm_password:
            print("❌ 两次输入的密码不一致")
            continue
        
        break
    
    return username, email, password

def create_initial_user(engine, username, email, password):
    """创建初始用户"""
    print(f"\n👨‍💼 创建用户 '{username}'...")
    
    try:
        from app.models import User
        from app.services.auth_service import AuthService
        from sqlalchemy.orm import sessionmaker
        
        SessionLocal = sessionmaker(bind=engine)
        db = SessionLocal()
        
        try:
            # 检查用户是否已存在
            existing_user = db.query(User).filter(
                (User.username == username) | (User.email == email)
            ).first()
            
            if existing_user:
                print(f"⚠️ 用户已存在 (用户名: {existing_user.username}, 邮箱: {existing_user.email})")
                return False
            
            # 创建新用户
            hashed_password = AuthService.get_password_hash(password)
            new_user = User(
                username=username,
                email=email,
                hashed_password=hashed_password,
                created_at=datetime.utcnow()
            )
            
            db.add(new_user)
            db.commit()
            
            print(f"✅ 用户创建成功!")
            print(f"   用户名: {username}")
            print(f"   邮箱: {email}")
            print(f"   用户ID: {new_user.id}")
            
            return True
            
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ 创建用户失败: {e}")
        return False

def save_env_config(database_url, db_type):
    """保存环境配置到.env文件"""
    print("\n💾 是否保存数据库配置到.env文件?")
    save_config = input("保存配置? (y/N): ").strip().lower()
    
    if save_config in ['y', 'yes']:
        try:
            env_path = ".env"
            
            # 读取现有配置
            env_content = ""
            if os.path.exists(env_path):
                with open(env_path, 'r', encoding='utf-8') as f:
                    env_content = f.read()
            
            # 更新数据库配置
            if db_type == "MySQL":
                # 解析MySQL URL获取各个组件
                from urllib.parse import urlparse
                parsed = urlparse(database_url)
                
                new_config = f"""# ===========================================
# 数据库配置 (MySQL)
# ===========================================
DB_HOST={parsed.hostname}
DB_PORT={parsed.port}
DB_USER={parsed.username}
DB_PASSWORD={parsed.password}
DB_NAME={parsed.path.lstrip('/')}

"""
            else:
                new_config = f"""# ===========================================
# 数据库配置 (SQLite)
# ===========================================
DATABASE_URL={database_url}

"""
            
            # 如果文件不存在或为空，添加基础配置
            if not env_content.strip():
                env_content = new_config + """# ===========================================
# JWT认证配置
# ===========================================
SECRET_KEY=your-secret-key-here-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# ===========================================
# 应用配置
# ===========================================
ENVIRONMENT=development
"""
            else:
                # 更新现有配置
                env_content = new_config + env_content
            
            with open(env_path, 'w', encoding='utf-8') as f:
                f.write(env_content)
            
            print(f"✅ 配置已保存到 {env_path}")
            
        except Exception as e:
            print(f"❌ 保存配置失败: {e}")

def main():
    """主函数"""
    print_banner()
    
    try:
        # 1. 获取数据库配置
        database_url, db_type = get_database_config()
        
        # 2. 测试数据库连接
        engine = test_database_connection(database_url, db_type)
        if not engine:
            print("\n❌ 数据库连接失败，初始化中止")
            return 1
        
        # 3. 创建数据库表结构
        if not create_database_tables(engine):
            print("\n❌ 创建表结构失败，初始化中止")
            return 1
        
        # 4. 创建初始用户
        username, email, password = get_user_info()
        if not create_initial_user(engine, username, email, password):
            print("\n⚠️ 用户创建失败，但表结构已创建")
        
        # 5. 保存配置
        save_env_config(database_url, db_type)
        
        print("\n" + "=" * 70)
        print("🎉 数据库初始化完成!")
        print("=" * 70)
        print(f"数据库类型: {db_type}")
        print(f"用户名: {username}")
        print(f"邮箱: {email}")
        print("\n现在可以启动应用了: python -m uvicorn main:app --reload")
        
        return 0
        
    except KeyboardInterrupt:
        print("\n\n❌ 用户中断初始化")
        return 1
    except Exception as e:
        print(f"\n❌ 初始化过程中发生错误: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
