#!/usr/bin/env python3
"""
SQLite到MySQL数据迁移工具
将现有的SQLite数据库迁移到远程MySQL数据库
"""

import os
import sys
import logging
from datetime import datetime
from sqlalchemy import create_engine, text, inspect
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import SQLAlchemyError

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import Base
from app.models import *

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'migration_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DatabaseMigrator:
    def __init__(self):
        # SQLite数据库连接
        self.sqlite_url = "sqlite:///./social_media.db"
        self.sqlite_engine = create_engine(self.sqlite_url)
        self.sqlite_session = sessionmaker(bind=self.sqlite_engine)()
        
        # MySQL数据库连接
        self.mysql_host = "***********"
        self.mysql_port = "33316"
        self.mysql_user = "social_media_manager"
        self.mysql_password = "PQhik9hWszKd"
        self.mysql_database = "social_media_manager"
        
        self.mysql_url = f"mysql+pymysql://{self.mysql_user}:{self.mysql_password}@{self.mysql_host}:{self.mysql_port}/{self.mysql_database}?charset=utf8mb4"
        self.mysql_engine = create_engine(
            self.mysql_url,
            pool_size=10,
            max_overflow=20,
            pool_pre_ping=True,
            pool_recycle=3600,
            echo=False
        )
        self.mysql_session = sessionmaker(bind=self.mysql_engine)()
        
        # 表迁移顺序（考虑外键依赖关系）
        self.migration_order = [
            User,
            FeishuApp,
            PlatformAccount,
            DataRecord,
            FeishuTable,
            WeChatMPUserChannel,
            WeChatMPUserSource,
            WeChatMPContentTrend,
            WeChatMPContentSource,
            WeChatMPContentDetail,
            WeChatChannelsFollowerData,
            WeChatChannelsVideoData,
            XiaohongshuFansData,
            XiaohongshuAccountOverview,
            XiaohongshuNoteData,
            DataDownloadRecord,
            DataUpdateRecord,
            DataUpdateTaskItem,
            LoginKeeperRecord,
            AutoUpdateConfig
        ]
    
    def test_connections(self):
        """测试数据库连接"""
        logger.info("🔍 测试数据库连接...")
        
        try:
            # 测试SQLite连接
            with self.sqlite_engine.connect() as conn:
                result = conn.execute(text("SELECT 1"))
                logger.info("✅ SQLite连接成功")
        except Exception as e:
            logger.error(f"❌ SQLite连接失败: {e}")
            return False
        
        try:
            # 测试MySQL连接
            with self.mysql_engine.connect() as conn:
                result = conn.execute(text("SELECT 1"))
                logger.info("✅ MySQL连接成功")
        except Exception as e:
            logger.error(f"❌ MySQL连接失败: {e}")
            return False
        
        return True
    
    def check_sqlite_tables(self):
        """检查SQLite中的表和数据"""
        logger.info("📊 检查SQLite数据库表和数据...")
        
        inspector = inspect(self.sqlite_engine)
        tables = inspector.get_table_names()
        
        logger.info(f"SQLite中的表: {tables}")
        
        table_stats = {}
        for table_name in tables:
            try:
                with self.sqlite_engine.connect() as conn:
                    result = conn.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
                    count = result.scalar()
                    table_stats[table_name] = count
                    logger.info(f"  {table_name}: {count} 条记录")
            except Exception as e:
                logger.warning(f"  {table_name}: 无法获取记录数 - {e}")
                table_stats[table_name] = 0
        
        return table_stats
    
    def create_mysql_tables(self):
        """在MySQL中创建表结构"""
        logger.info("🏗️ 在MySQL中创建表结构...")
        
        try:
            # 使用SQLAlchemy的Base.metadata.create_all()创建所有表
            Base.metadata.create_all(bind=self.mysql_engine)
            logger.info("✅ MySQL表结构创建成功")
            return True
        except Exception as e:
            logger.error(f"❌ MySQL表结构创建失败: {e}")
            return False
    
    def migrate_table_data(self, model_class):
        """迁移单个表的数据"""
        table_name = model_class.__tablename__
        logger.info(f"📦 迁移表: {table_name}")

        try:
            # 从SQLite读取数据
            sqlite_data = self.sqlite_session.query(model_class).all()

            if not sqlite_data:
                logger.info(f"  {table_name}: 无数据需要迁移")
                return True

            logger.info(f"  {table_name}: 找到 {len(sqlite_data)} 条记录")

            # 批量插入到MySQL
            batch_size = 50  # 减小批次大小以便更好地处理错误
            total_migrated = 0
            failed_records = 0

            for i in range(0, len(sqlite_data), batch_size):
                batch = sqlite_data[i:i + batch_size]

                # 将SQLAlchemy对象转换为字典，然后创建新对象
                batch_dicts = []
                for item in batch:
                    item_dict = {}
                    for column in model_class.__table__.columns:
                        value = getattr(item, column.name)
                        item_dict[column.name] = value
                    batch_dicts.append(item_dict)

                # 批量插入
                try:
                    self.mysql_session.bulk_insert_mappings(model_class, batch_dicts)
                    self.mysql_session.commit()
                    total_migrated += len(batch)
                    logger.info(f"  {table_name}: 已迁移 {total_migrated}/{len(sqlite_data)} 条记录")
                except Exception as e:
                    logger.warning(f"  {table_name}: 批量插入失败，尝试逐条插入 - {e}")
                    self.mysql_session.rollback()

                    # 逐条插入以跳过有问题的记录
                    for item_dict in batch_dicts:
                        try:
                            self.mysql_session.bulk_insert_mappings(model_class, [item_dict])
                            self.mysql_session.commit()
                            total_migrated += 1
                        except Exception as item_error:
                            logger.warning(f"  {table_name}: 跳过有问题的记录 ID={item_dict.get('id', 'unknown')} - {item_error}")
                            self.mysql_session.rollback()
                            failed_records += 1
                            continue

            if failed_records > 0:
                logger.warning(f"⚠️ {table_name}: 迁移完成，共 {total_migrated} 条记录，跳过 {failed_records} 条有问题的记录")
            else:
                logger.info(f"✅ {table_name}: 迁移完成，共 {total_migrated} 条记录")
            return True

        except Exception as e:
            logger.error(f"❌ {table_name}: 迁移失败 - {e}")
            self.mysql_session.rollback()
            return False
    
    def verify_migration(self):
        """验证迁移结果"""
        logger.info("🔍 验证迁移结果...")
        
        verification_passed = True
        
        for model_class in self.migration_order:
            table_name = model_class.__tablename__
            
            try:
                # 统计SQLite中的记录数
                sqlite_count = self.sqlite_session.query(model_class).count()
                
                # 统计MySQL中的记录数
                mysql_count = self.mysql_session.query(model_class).count()
                
                if sqlite_count == mysql_count:
                    logger.info(f"✅ {table_name}: SQLite({sqlite_count}) = MySQL({mysql_count})")
                else:
                    logger.error(f"❌ {table_name}: SQLite({sqlite_count}) ≠ MySQL({mysql_count})")
                    verification_passed = False
                    
            except Exception as e:
                logger.error(f"❌ {table_name}: 验证失败 - {e}")
                verification_passed = False
        
        return verification_passed
    
    def run_migration(self):
        """执行完整的迁移流程"""
        logger.info("🚀 开始数据库迁移...")
        
        # 1. 测试连接
        if not self.test_connections():
            logger.error("❌ 数据库连接测试失败，迁移终止")
            return False
        
        # 2. 检查SQLite数据
        sqlite_stats = self.check_sqlite_tables()
        
        # 3. 创建MySQL表结构
        if not self.create_mysql_tables():
            logger.error("❌ MySQL表结构创建失败，迁移终止")
            return False
        
        # 4. 迁移数据
        logger.info("📦 开始数据迁移...")
        migration_success = True
        
        for model_class in self.migration_order:
            if not self.migrate_table_data(model_class):
                migration_success = False
                break
        
        if not migration_success:
            logger.error("❌ 数据迁移失败")
            return False
        
        # 5. 验证迁移结果
        if not self.verify_migration():
            logger.error("❌ 迁移验证失败")
            return False
        
        logger.info("🎉 数据库迁移完成！")
        return True
    
    def cleanup(self):
        """清理资源"""
        try:
            self.sqlite_session.close()
            self.mysql_session.close()
            self.sqlite_engine.dispose()
            self.mysql_engine.dispose()
            logger.info("✅ 数据库连接已关闭")
        except Exception as e:
            logger.warning(f"清理资源时出错: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("  SQLite到MySQL数据迁移工具")
    print("=" * 60)
    
    migrator = DatabaseMigrator()
    
    try:
        success = migrator.run_migration()
        if success:
            print("\n🎉 迁移成功完成！")
            return 0
        else:
            print("\n❌ 迁移失败！")
            return 1
    except KeyboardInterrupt:
        logger.info("用户中断迁移")
        return 1
    except Exception as e:
        logger.error(f"迁移过程中发生未预期的错误: {e}")
        return 1
    finally:
        migrator.cleanup()

if __name__ == "__main__":
    sys.exit(main())
