#!/usr/bin/env python3
"""
微信视频号关注者数据表迁移脚本
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from app.database import engine, SessionLocal
from app.models import WeChatChannelsFollowerData

def create_wechat_channels_follower_data_table():
    """创建微信视频号关注者数据表"""
    
    # SQLite语法的建表语句
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS wechat_channels_follower_data (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        account_id INTEGER NOT NULL,
        date DATE NOT NULL,
        net_follower_increase INTEGER DEFAULT 0,
        new_followers INTEGER DEFAULT 0,
        unfollowers INTEGER DEFAULT 0,
        total_followers INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (account_id) REFERENCES platform_accounts(id)
    );
    """
    
    # 创建唯一索引
    create_unique_index_sql = """
    CREATE UNIQUE INDEX IF NOT EXISTS uq_follower_account_date 
    ON wechat_channels_follower_data (account_id, date);
    """
    
    try:
        with engine.connect() as connection:
            print("正在创建微信视频号关注者数据表...")
            connection.execute(text(create_table_sql))
            print("正在创建唯一索引...")
            connection.execute(text(create_unique_index_sql))
            connection.commit()
            print("✅ 微信视频号关注者数据表创建成功")
            
            # 验证表是否创建成功 (SQLite语法)
            result = connection.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name='wechat_channels_follower_data'"))
            if result.fetchone():
                print("✅ 表创建验证成功")
                
                # 显示表结构 (SQLite语法)
                print("\n📋 表结构:")
                result = connection.execute(text("PRAGMA table_info(wechat_channels_follower_data)"))
                for row in result:
                    print(f"  {row[1]}: {row[2]} {'NOT NULL' if row[3] else 'NULL'} {f'DEFAULT {row[4]}' if row[4] else ''}")
                    
                # 显示索引信息 (SQLite语法)
                print("\n🔑 索引信息:")
                result = connection.execute(text("PRAGMA index_list(wechat_channels_follower_data)"))
                for row in result:
                    print(f"  {row[1]}: {'UNIQUE' if row[2] else 'INDEX'}")
            else:
                print("❌ 表创建验证失败")
                return False
                
    except Exception as e:
        print(f"❌ 创建表失败: {e}")
        return False
        
    return True

def main():
    """主函数"""
    print("=== 微信视频号关注者数据表迁移 ===")
    
    try:
        # 创建表
        if create_wechat_channels_follower_data_table():
            print("\n🎉 迁移完成!")
            print("\n📊 表字段说明:")
            print("  - net_follower_increase: 净增关注")
            print("  - new_followers: 新增关注")
            print("  - unfollowers: 取消关注")
            print("  - total_followers: 关注者总数")
            print("\n📝 数据来源:")
            print("  - 页面: https://channels.weixin.qq.com/platform/statistic/follower")
            print("  - API: fans_trend (iframe内)")
            print("  - 文件: CSV下载")
        else:
            print("\n❌ 迁移失败!")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ 迁移过程中发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
