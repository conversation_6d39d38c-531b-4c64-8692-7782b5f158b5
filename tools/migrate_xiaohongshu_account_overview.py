#!/usr/bin/env python3
"""
小红书账号概览数据表迁移脚本
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from app.database import engine, SessionLocal
from app.models import XiaohongshuAccountOverview

def create_xiaohongshu_account_overview_table():
    """创建小红书账号概览数据表"""
    
    # SQLite语法的建表语句
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS xiaohongshu_account_overview (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        account_id INTEGER NOT NULL,
        date DATE NOT NULL,
        view_count INTEGER DEFAULT 0,
        view_time_count INTEGER DEFAULT 0,
        home_view_count INTEGER DEFAULT 0,
        like_count INTEGER DEFAULT 0,
        collect_count INTEGER DEFAULT 0,
        comment_count INTEGER DEFAULT 0,
        danmaku_count INTEGER DEFAULT 0,
        rise_fans_count INTEGER DEFAULT 0,
        share_count INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (account_id) REFERENCES platform_accounts(id)
    );
    """
    
    # 创建唯一索引
    create_unique_index_sql = """
    CREATE UNIQUE INDEX IF NOT EXISTS uq_account_date 
    ON xiaohongshu_account_overview (account_id, date);
    """
    
    try:
        with engine.connect() as connection:
            print("正在创建小红书账号概览数据表...")
            connection.execute(text(create_table_sql))
            print("正在创建唯一索引...")
            connection.execute(text(create_unique_index_sql))
            connection.commit()
            print("✅ 小红书账号概览数据表创建成功")
            
            # 验证表是否创建成功 (SQLite语法)
            result = connection.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name='xiaohongshu_account_overview'"))
            if result.fetchone():
                print("✅ 表创建验证成功")
                
                # 显示表结构 (SQLite语法)
                print("\n📋 表结构:")
                result = connection.execute(text("PRAGMA table_info(xiaohongshu_account_overview)"))
                for row in result:
                    print(f"  {row[1]}: {row[2]} {'NOT NULL' if row[3] else 'NULL'} {f'DEFAULT {row[4]}' if row[4] else ''}")
                    
                # 显示索引信息 (SQLite语法)
                print("\n🔑 索引信息:")
                result = connection.execute(text("PRAGMA index_list(xiaohongshu_account_overview)"))
                for row in result:
                    print(f"  {row[1]}: {'UNIQUE' if row[2] else 'INDEX'}")
            else:
                print("❌ 表创建验证失败")
                return False
                
    except Exception as e:
        print(f"❌ 创建表失败: {e}")
        return False
        
    return True

def main():
    """主函数"""
    print("=== 小红书账号概览数据表迁移 ===")
    
    try:
        # 创建表
        if create_xiaohongshu_account_overview_table():
            print("\n🎉 迁移完成!")
            print("\n📊 表字段说明:")
            print("  - view_count: 观看量")
            print("  - view_time_count: 观看总时长")
            print("  - home_view_count: 主页访客量")
            print("  - like_count: 点赞数")
            print("  - collect_count: 收藏数")
            print("  - comment_count: 评论数")
            print("  - danmaku_count: 弹幕数")
            print("  - rise_fans_count: 涨粉数")
            print("  - share_count: 分享数")
        else:
            print("\n❌ 迁移失败!")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ 迁移过程中发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
