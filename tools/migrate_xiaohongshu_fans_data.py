#!/usr/bin/env python3
"""
小红书粉丝数据表迁移脚本
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from app.database import engine, SessionLocal
from app.models import XiaohongshuFansData

def create_xiaohongshu_fans_data_table():
    """创建小红书粉丝数据表"""
    
    # SQLite语法的建表语句
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS xiaohongshu_fans_data (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        account_id INTEGER NOT NULL,
        date DATE NOT NULL,
        total_fans_count INTEGER DEFAULT 0,
        new_fans_count INTEGER DEFAULT 0,
        unfans_count INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (account_id) REFERENCES platform_accounts(id)
    );
    """
    
    # 创建唯一索引
    create_unique_index_sql = """
    CREATE UNIQUE INDEX IF NOT EXISTS uq_fans_account_date 
    ON xiaohongshu_fans_data (account_id, date);
    """
    
    try:
        with engine.connect() as connection:
            print("正在创建小红书粉丝数据表...")
            connection.execute(text(create_table_sql))
            print("正在创建唯一索引...")
            connection.execute(text(create_unique_index_sql))
            connection.commit()
            print("✅ 小红书粉丝数据表创建成功")
            
            # 验证表是否创建成功 (SQLite语法)
            result = connection.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name='xiaohongshu_fans_data'"))
            if result.fetchone():
                print("✅ 表创建验证成功")
                
                # 显示表结构 (SQLite语法)
                print("\n📋 表结构:")
                result = connection.execute(text("PRAGMA table_info(xiaohongshu_fans_data)"))
                for row in result:
                    print(f"  {row[1]}: {row[2]} {'NOT NULL' if row[3] else 'NULL'} {f'DEFAULT {row[4]}' if row[4] else ''}")
                    
                # 显示索引信息 (SQLite语法)
                print("\n🔑 索引信息:")
                result = connection.execute(text("PRAGMA index_list(xiaohongshu_fans_data)"))
                for row in result:
                    print(f"  {row[1]}: {'UNIQUE' if row[2] else 'INDEX'}")
            else:
                print("❌ 表创建验证失败")
                return False
                
    except Exception as e:
        print(f"❌ 创建表失败: {e}")
        return False
        
    return True

def main():
    """主函数"""
    print("=== 小红书粉丝数据表迁移 ===")
    
    try:
        # 创建表
        if create_xiaohongshu_fans_data_table():
            print("\n🎉 迁移完成!")
            print("\n📊 表字段说明:")
            print("  - total_fans_count: 总关注数")
            print("  - new_fans_count: 新增关注数")
            print("  - unfans_count: 取关数")
            print("\n📝 数据来源:")
            print("  - API: https://creator.xiaohongshu.com/api/galaxy/creator/data/fans/overall_new")
            print("  - 页面: https://creator.xiaohongshu.com/creator/fans")
        else:
            print("\n❌ 迁移失败!")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ 迁移过程中发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
