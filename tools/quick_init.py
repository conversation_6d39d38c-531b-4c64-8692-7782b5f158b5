#!/usr/bin/env python3
"""
快速数据库初始化脚本
使用当前环境变量配置快速初始化数据库
"""

import os
import sys
import getpass
from datetime import datetime
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def print_banner():
    """打印横幅"""
    print("=" * 60)
    print("  🚀 快速数据库初始化工具")
    print("=" * 60)
    print()

def show_current_config():
    """显示当前数据库配置"""
    from app.database import get_database_url
    
    database_url = get_database_url()
    print("📋 当前数据库配置:")
    
    if database_url.startswith('mysql'):
        # 解析MySQL配置
        db_host = os.getenv("DB_HOST", "localhost")
        db_port = os.getenv("DB_PORT", "3306")
        db_name = os.getenv("DB_NAME", "social_media_manager")
        db_user = os.getenv("DB_USER", "root")
        
        print(f"   类型: MySQL")
        print(f"   主机: {db_host}")
        print(f"   端口: {db_port}")
        print(f"   数据库: {db_name}")
        print(f"   用户: {db_user}")
    else:
        print(f"   类型: SQLite")
        print(f"   文件: {database_url.replace('sqlite:///', '')}")
    
    print()

def test_connection():
    """测试数据库连接"""
    print("🔍 测试数据库连接...")
    
    try:
        from app.database import engine
        
        with engine.connect() as conn:
            from sqlalchemy import text
            result = conn.execute(text("SELECT 1"))
            test_value = result.scalar()
            
            if engine.url.drivername.startswith('mysql'):
                result = conn.execute(text("SELECT VERSION()"))
                version = result.scalar()
                print(f"✅ MySQL连接成功，版本: {version}")
            else:
                result = conn.execute(text("SELECT sqlite_version()"))
                version = result.scalar()
                print(f"✅ SQLite连接成功，版本: {version}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def create_tables():
    """创建数据库表"""
    print("\n🏗️ 创建数据库表结构...")
    
    try:
        from app.database import engine, Base
        from sqlalchemy import text
        
        # 创建所有表
        Base.metadata.create_all(bind=engine)
        
        # 验证表创建
        with engine.connect() as conn:
            if engine.url.drivername.startswith('mysql'):
                result = conn.execute(text("SHOW TABLES"))
                tables = [row[0] for row in result.fetchall()]
            else:
                result = conn.execute(text("SELECT name FROM sqlite_master WHERE type='table'"))
                tables = [row[0] for row in result.fetchall()]
        
        print(f"✅ 成功创建 {len(tables)} 个数据表")
        return True
        
    except Exception as e:
        print(f"❌ 创建表结构失败: {e}")
        return False

def create_admin_user():
    """创建管理员用户"""
    print("\n👤 创建管理员用户:")
    
    # 获取用户信息
    username = input("用户名: ").strip()
    while not username:
        print("❌ 用户名不能为空")
        username = input("用户名: ").strip()
    
    email = input("邮箱: ").strip()
    while not email or '@' not in email:
        print("❌ 请输入有效的邮箱地址")
        email = input("邮箱: ").strip()
    
    while True:
        password = getpass.getpass("密码: ")
        if len(password) < 6:
            print("❌ 密码长度至少6位")
            continue
        
        confirm_password = getpass.getpass("确认密码: ")
        if password != confirm_password:
            print("❌ 两次输入的密码不一致")
            continue
        
        break
    
    # 创建用户
    try:
        from app.database import SessionLocal
        from app.models import User
        from app.services.auth_service import AuthService
        
        db = SessionLocal()
        
        try:
            # 检查用户是否已存在
            existing_user = db.query(User).filter(
                (User.username == username) | (User.email == email)
            ).first()
            
            if existing_user:
                print(f"⚠️ 用户已存在:")
                print(f"   用户名: {existing_user.username}")
                print(f"   邮箱: {existing_user.email}")
                print(f"   创建时间: {existing_user.created_at}")
                return True
            
            # 创建新用户
            hashed_password = AuthService.get_password_hash(password)
            new_user = User(
                username=username,
                email=email,
                hashed_password=hashed_password,
                created_at=datetime.utcnow()
            )
            
            db.add(new_user)
            db.commit()
            
            print(f"✅ 管理员用户创建成功!")
            print(f"   用户名: {username}")
            print(f"   邮箱: {email}")
            print(f"   用户ID: {new_user.id}")
            
            return True
            
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ 创建用户失败: {e}")
        return False

def show_summary():
    """显示初始化总结"""
    print("\n" + "=" * 60)
    print("🎉 数据库初始化完成!")
    print("=" * 60)
    print()
    print("接下来可以:")
    print("1. 启动应用: python -m uvicorn main:app --reload")
    print("2. 或使用Docker: docker-compose up")
    print("3. 访问: http://localhost:8000")
    print()

def main():
    """主函数"""
    print_banner()
    
    try:
        # 1. 显示当前配置
        show_current_config()
        
        # 2. 测试连接
        if not test_connection():
            print("\n❌ 数据库连接失败，请检查配置")
            return 1
        
        # 3. 创建表结构
        if not create_tables():
            print("\n❌ 创建表结构失败")
            return 1
        
        # 4. 创建管理员用户
        if not create_admin_user():
            print("\n❌ 创建用户失败")
            return 1
        
        # 5. 显示总结
        show_summary()
        
        return 0
        
    except KeyboardInterrupt:
        print("\n\n❌ 用户中断初始化")
        return 1
    except Exception as e:
        print(f"\n❌ 初始化过程中发生错误: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
