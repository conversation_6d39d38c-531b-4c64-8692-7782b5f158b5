#!/usr/bin/env python3
"""
测试微信视频号关注者数据获取修复
"""

import asyncio
import os
import sys

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from app.services.wechat_channels_service import WeChatChannelsService


async def test_follower_data_fix():
    """测试关注者数据获取修复"""
    print("🧪 开始测试微信视频号关注者数据获取修复...")
    
    # 测试账号ID（请根据实际情况修改）
    test_account_id = 1
    
    try:
        # 创建服务实例
        service = WeChatChannelsService(account_id=test_account_id, headless=True)
        
        print(f"📋 测试账号ID: {test_account_id}")
        print("🔧 使用headless模式进行测试")
        
        # 测试关注者数据获取
        print("\n📊 开始获取关注者数据...")
        follower_data = await service.get_follower_data(auto_import=False)
        
        if follower_data:
            print(f"✅ 关注者数据获取成功!")
            print(f"   数据条数: {len(follower_data)}")
            if follower_data:
                print(f"   示例数据: {follower_data[0]}")
        else:
            print("❌ 关注者数据获取失败")
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        
    finally:
        # 清理资源
        try:
            await service.close()
            print("✅ 服务资源已清理")
        except:
            pass


async def test_configuration():
    """测试配置优化"""
    print("\n🔧 测试配置优化...")
    
    from app.config.wechat_channels_config import WeChatChannelsConfig
    
    # 检查超时配置
    nav_timeout = WeChatChannelsConfig.get_timeout('navigation', False)
    default_timeout = WeChatChannelsConfig.get_timeout('default', False)
    network_timeout = WeChatChannelsConfig.get_timeout('network_check', False)
    browser_timeout = WeChatChannelsConfig.get_timeout('browser_launch', False)
    
    print(f"✅ 导航超时: {nav_timeout}ms")
    print(f"✅ 默认超时: {default_timeout}ms")
    print(f"✅ 网络检查超时: {network_timeout}ms")
    print(f"✅ 浏览器启动超时: {browser_timeout}ms")
    
    # 检查重试配置
    max_retries = WeChatChannelsConfig.get_max_retries()
    retry_delay = WeChatChannelsConfig.get_retry_delay(0)
    
    print(f"✅ 最大重试次数: {max_retries}")
    print(f"✅ 重试延迟: {retry_delay}秒")
    
    # 检查浏览器参数
    browser_args = WeChatChannelsConfig.get_browser_args(False, True)
    print(f"✅ 浏览器参数: {len(browser_args)} 个参数")
    if WeChatChannelsConfig.is_debug_mode():
        print(f"   参数详情: {browser_args}")


if __name__ == "__main__":
    print("🚀 微信视频号关注者数据获取修复测试")
    print("=" * 50)
    
    # 测试配置
    asyncio.run(test_configuration())
    
    # 测试功能（可选，需要有效的登录状态）
    print("\n" + "=" * 50)
    print("注意: 功能测试需要有效的微信视频号登录状态")
    user_input = input("是否进行功能测试? (y/N): ").strip().lower()
    
    if user_input == 'y':
        asyncio.run(test_follower_data_fix())
    else:
        print("⏭️  跳过功能测试")
    
    print("\n🎉 测试完成!")
