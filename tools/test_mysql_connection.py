#!/usr/bin/env python3
"""
测试MySQL数据库连接
"""

import os
import sys
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_mysql_connection():
    """测试MySQL连接"""
    print("🔍 测试MySQL数据库连接...")
    
    # MySQL连接信息
    mysql_host = "***********"
    mysql_port = "33316"
    mysql_user = "social_media_manager"
    mysql_password = "PQhik9hWszKd"
    mysql_database = "social_media_manager"
    
    mysql_url = f"mysql+pymysql://{mysql_user}:{mysql_password}@{mysql_host}:{mysql_port}/{mysql_database}?charset=utf8mb4"
    
    print(f"连接信息:")
    print(f"  主机: {mysql_host}")
    print(f"  端口: {mysql_port}")
    print(f"  用户: {mysql_user}")
    print(f"  数据库: {mysql_database}")
    print()
    
    try:
        # 创建引擎
        engine = create_engine(
            mysql_url,
            pool_size=5,
            max_overflow=10,
            pool_pre_ping=True,
            pool_recycle=3600,
            echo=False
        )
        
        # 测试连接
        with engine.connect() as conn:
            # 测试基本查询
            result = conn.execute(text("SELECT 1 as test"))
            test_value = result.scalar()
            print(f"✅ 基本连接测试: {test_value}")
            
            # 获取数据库版本
            result = conn.execute(text("SELECT VERSION() as version"))
            version = result.scalar()
            print(f"✅ MySQL版本: {version}")
            
            # 获取当前数据库
            result = conn.execute(text("SELECT DATABASE() as db"))
            current_db = result.scalar()
            print(f"✅ 当前数据库: {current_db}")
            
            # 检查字符集
            result = conn.execute(text("SELECT @@character_set_database as charset"))
            charset = result.scalar()
            print(f"✅ 数据库字符集: {charset}")
            
            # 检查排序规则
            result = conn.execute(text("SELECT @@collation_database as collation"))
            collation = result.scalar()
            print(f"✅ 数据库排序规则: {collation}")
            
            # 列出现有表
            result = conn.execute(text("SHOW TABLES"))
            tables = [row[0] for row in result.fetchall()]
            print(f"✅ 现有表数量: {len(tables)}")
            if tables:
                print(f"   表列表: {', '.join(tables[:10])}")  # 只显示前10个表
                if len(tables) > 10:
                    print(f"   ... 还有 {len(tables) - 10} 个表")
            
        print("\n🎉 MySQL连接测试成功！")
        return True
        
    except SQLAlchemyError as e:
        print(f"\n❌ MySQL连接失败: {e}")
        return False
    except Exception as e:
        print(f"\n❌ 连接测试出错: {e}")
        return False
    finally:
        try:
            engine.dispose()
        except:
            pass

def test_sqlite_connection():
    """测试SQLite连接"""
    print("\n🔍 测试SQLite数据库连接...")
    
    sqlite_path = "./social_media.db"
    
    if not os.path.exists(sqlite_path):
        print(f"❌ SQLite数据库文件不存在: {sqlite_path}")
        return False
    
    sqlite_url = f"sqlite:///{sqlite_path}"
    
    try:
        engine = create_engine(sqlite_url)
        
        with engine.connect() as conn:
            # 测试基本查询
            result = conn.execute(text("SELECT 1 as test"))
            test_value = result.scalar()
            print(f"✅ 基本连接测试: {test_value}")
            
            # 获取SQLite版本
            result = conn.execute(text("SELECT sqlite_version() as version"))
            version = result.scalar()
            print(f"✅ SQLite版本: {version}")
            
            # 列出现有表
            result = conn.execute(text("SELECT name FROM sqlite_master WHERE type='table'"))
            tables = [row[0] for row in result.fetchall()]
            print(f"✅ 现有表数量: {len(tables)}")
            if tables:
                print(f"   表列表: {', '.join(tables)}")
                
                # 统计每个表的记录数
                print("\n📊 表记录统计:")
                for table in tables:
                    try:
                        result = conn.execute(text(f"SELECT COUNT(*) FROM {table}"))
                        count = result.scalar()
                        print(f"   {table}: {count} 条记录")
                    except Exception as e:
                        print(f"   {table}: 无法获取记录数 - {e}")
        
        print("\n✅ SQLite连接测试成功！")
        return True
        
    except Exception as e:
        print(f"\n❌ SQLite连接失败: {e}")
        return False
    finally:
        try:
            engine.dispose()
        except:
            pass

def main():
    """主函数"""
    print("=" * 60)
    print("  数据库连接测试工具")
    print("=" * 60)
    
    # 测试SQLite连接
    sqlite_ok = test_sqlite_connection()
    
    # 测试MySQL连接
    mysql_ok = test_mysql_connection()
    
    print("\n" + "=" * 60)
    print("  测试结果汇总")
    print("=" * 60)
    print(f"SQLite连接: {'✅ 成功' if sqlite_ok else '❌ 失败'}")
    print(f"MySQL连接: {'✅ 成功' if mysql_ok else '❌ 失败'}")
    
    if sqlite_ok and mysql_ok:
        print("\n🎉 所有数据库连接测试通过，可以开始迁移！")
        return 0
    else:
        print("\n❌ 部分数据库连接失败，请检查配置！")
        return 1

if __name__ == "__main__":
    sys.exit(main())
