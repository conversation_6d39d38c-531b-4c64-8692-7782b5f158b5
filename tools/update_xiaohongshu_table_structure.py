#!/usr/bin/env python3
"""
更新小红书笔记数据表结构
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from app.database import engine, SessionLocal

def update_xiaohongshu_table_structure():
    """更新小红书笔记数据表结构"""
    
    try:
        with engine.connect() as connection:
            print("正在更新小红书笔记数据表结构...")
            
            # 删除旧的唯一索引
            try:
                connection.execute(text("DROP INDEX IF EXISTS uq_account_note_time"))
                print("✅ 删除旧的唯一索引")
            except Exception as e:
                print(f"⚠️  删除旧索引失败（可能不存在）: {e}")
            
            # 重命名和添加新字段
            alter_commands = [
                # 重命名字段
                "ALTER TABLE xiaohongshu_note_data RENAME COLUMN note_id TO temp_note_id",
                "ALTER TABLE xiaohongshu_note_data RENAME COLUMN publish_time TO first_publish_time",
                "ALTER TABLE xiaohongshu_note_data RENAME COLUMN note_type TO content_type",
                
                # 添加新字段
                "ALTER TABLE xiaohongshu_note_data ADD COLUMN avg_view_duration REAL DEFAULT 0.0",
                "ALTER TABLE xiaohongshu_note_data ADD COLUMN barrage_count INTEGER DEFAULT 0",
                
                # 删除临时字段
                "ALTER TABLE xiaohongshu_note_data DROP COLUMN temp_note_id"
            ]
            
            for command in alter_commands:
                try:
                    connection.execute(text(command))
                    print(f"✅ 执行: {command}")
                except Exception as e:
                    print(f"⚠️  执行失败: {command} - {e}")
            
            # 创建新的唯一索引
            create_unique_index_sql = """
            CREATE UNIQUE INDEX IF NOT EXISTS uq_account_note_title_time 
            ON xiaohongshu_note_data (account_id, note_title, first_publish_time);
            """
            
            connection.execute(text(create_unique_index_sql))
            print("✅ 创建新的唯一索引")
            
            connection.commit()
            print("✅ 表结构更新成功")
            
            # 验证表结构
            print("\n📋 更新后的表结构:")
            result = connection.execute(text("PRAGMA table_info(xiaohongshu_note_data)"))
            for row in result:
                print(f"  {row[1]}: {row[2]} {'NOT NULL' if row[3] else 'NULL'} {f'DEFAULT {row[4]}' if row[4] else ''}")
                
            # 显示索引信息
            print("\n🔑 索引信息:")
            result = connection.execute(text("PRAGMA index_list(xiaohongshu_note_data)"))
            for row in result:
                print(f"  {row[1]}: {'UNIQUE' if row[2] else 'INDEX'}")
                
    except Exception as e:
        print(f"❌ 更新表结构失败: {e}")
        return False
        
    return True

def main():
    """主函数"""
    print("=== 小红书笔记数据表结构更新 ===")
    
    try:
        # 更新表结构
        if update_xiaohongshu_table_structure():
            print("\n🎉 表结构更新完成!")
        else:
            print("\n❌ 表结构更新失败!")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ 更新过程中发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
